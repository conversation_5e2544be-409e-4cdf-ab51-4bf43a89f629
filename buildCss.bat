@echo off
powershell -Command ^
    "if (Test-Path 'dist/assets/index.css') {" ^
        "$content = Get-Content -Path 'dist/assets/index.css';" ^
        "$preText = 'export const minified_css = `';" ^
        "$postText = '`;//END';" ^
        "$modifiedContent = $preText + $content + $postText;" ^
        "Set-Content -Path 'src/minified-css.js' -Value $modifiedContent;" ^
    "} else {" ^
        "Write-Host ' dist/assets/index.css not found!' \"`n\"'>>> After build run again';" ^
    "}"
