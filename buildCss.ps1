# Define the path to monitor
$pathToMonitor = "dist"  # Ensure this is the correct full path

# Create a new FileSystemWatcher object
$watcher = New-Object System.IO.FileSystemWatcher
$watcher.Path = $pathToMonitor
$watcher.IncludeSubdirectories = $true
$batFilePath = "buildCss.bat"  # Ensure this is the correct full path to the .bat file

# Specify the types of changes to watch for
$watcher.NotifyFilter = [System.IO.NotifyFilters]'FileName, LastWrite, LastAccess, CreationTime, DirectoryName'

# Filter the files to watch (e.g., only .txt files). Adjust this to the file type you need.
# $watcher.Filter = "*.*"  # Watch all files initially for troubleshooting
$watcher.Filter = "*.css*"  # Watch all files initially for troubleshooting

# Define the action to take when a change is detected
$action = {
    $eventType = $Event.SourceEventArgs.ChangeType
    $filePath = $Event.SourceEventArgs.FullPath
    Write-Host -ForegroundColor Blue "File '$filePath' $eventType"
    
    try {
        if (Test-Path 'dist/assets/index.css') {
            $content = Get-Content -Path 'dist/assets/index.css';
            $preText = 'export const minified_css = `';
            $postText = '`;//END';
            $newContent = $preText + $content + $postText;
            $existingContent = Get-Content -Path 'src/minified-css.js'
            if ($existingContent -ne $newContent) {
                Set-Content -Path 'src/minified-css.js' -Value $newContent
                Write-Host -ForegroundColor Green "Updated 'src/minified-css.js' with new content."
            } else {
                Write-Host -ForegroundColor Yellow "'src/minified-css.js' already updated"
            }
        }
    }
    catch {
        Write-Host "Failed to execute .bat file: $_"
    }
}

# Register events
$onCreated = Register-ObjectEvent $watcher Created -Action $action
# $onChanged = Register-ObjectEvent $watcher Changed -Action $action
# $onDeleted = Register-ObjectEvent $watcher Deleted -Action $action
# $onRenamed = Register-ObjectEvent $watcher Renamed -Action $action

# Start the watcher
$watcher.EnableRaisingEvents = $true

# Keep the script running to continue monitoring (use Ctrl+C to stop)
Write-Host "Monitoring directory: $pathToMonitor. Press Ctrl+C to stop."
while ($true) { Start-Sleep -Seconds 1 }
