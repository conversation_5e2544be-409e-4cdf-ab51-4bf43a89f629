const stores = {
    vgc: {
        store_id: "982",
        locationId: "1048",
        store_name: "vivian-grace-creations",
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************.XBy0v26m5fnmWkFoBZ478F3JFdO5-JqNSfEITomLGyk",
    },
    wix: {
        store_id: "3431",
        locationId: "3674",
        store_name: "outhouse",
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.y5vwsr6wzDgL9-XC5X5DIUphQRi0XqBn4LM3Gww0fTw",
    },
    teststore09: {
        store_id: "590",
        locationId: "647",
        store_name: "teststore09",
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.SxjXX1OChku8pcSJjz-7a5N1GX6lJ6FHbQQ_QymaLcg",
    },
    teststore17: {
        store_id: "782",
        locationId: "841",
        store_name: "teststore01",
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.3uu2WWWgSZlMHHhYIWtyuTgBkAHZfJZF17s9VNjb4Oo",
    },
    teststore22_stage1: {
        store_id: "1487",
        locationId: "2982",
        store_name: "teststore22",
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAyMS0wNi0xMFQwNzoxNDo1NyswMDowMCIsInN0b3JlX2lkIjoxNDg3LCJzdG9yZV91aWQiOm51bGwsInN0b3JlX25hbWUiOm51bGwsInNvdXJjZSI6Im9ubGluZSIsImlzX29ubGluZSI6MSwibG9jYXRpb24iOjMxMzh9.tFv-EfBCIvapjqYGV7A7shq_DfSq7JINAc57TPavcEc",
    },
    teststore02: {
        store_id: "22",
        locationId: "3320",
        store_name: "teststore02",
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAxOS0wMS0wM1QxNTo1ODowNiswMDowMCIsInN0b3JlX2lkIjoyMiwic3RvcmVfdWlkIjpudWxsLCJzdG9yZV9uYW1lIjpudWxsLCJzb3VyY2UiOiJvbmxpbmUiLCJpc19vbmxpbmUiOjEsImxvY2F0aW9uIjozMzIwfQ.AwQkvEMrvvgBy0LuztuCKJDMWPT5PpOosMad3NlIEdo",
    },
    rentMasonBees: {
        store_id: "570",
        locationId: "627",
        store_name: "rent-mason-bees",
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAyMC0wMi0xMFQyMToxNzoxMyswMDowMCIsInN0b3JlX2lkIjo1NzAsInN0b3JlX3VpZCI6bnVsbCwic3RvcmVfbmFtZSI6bnVsbCwic291cmNlIjoib25saW5lIiwiaXNfb25saW5lIjoxLCJsb2NhdGlvbiI6NjI3fQ.XtMCU-X8yULHcZbdCwulqe_YL7gjZJkSe0xLpQsbbTA",
    },
    epsswing0x20xc: {
        store_id: "3745",
        locationId: "4012",
        store_name: "epsswing0x20xc",
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAyNS0wMy0wMiAwNzoyNjowNyIsInN0b3JlX2lkIjozNzQ1LCJzdG9yZV91aWQiOiI5YWNhMjUyMmY3MzcxMWVmYjQyZTAyMzA0YWE1ZDBhMyIsInN0b3JlX25hbWUiOiJlcHNzd2luZzB4MjB4YyIsInNvdXJjZSI6Im9ubGluZSIsImlzX29ubGluZSI6MSwibG9jYXRpb24iOjQwMTJ9.6XzQrImdDsG2JeYzTgsnE0lY4z3hBoFA8HspDNuzGz4",
    },
    blueskiesdrones: {
        store_id: "1279",
        locationId: "1363",
        store_name: "blueskiesdrones",
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************.-4a0EwDfVUo1VVCgCa0Cig_7zcWxdez3RMdP7zC8_BA",
    },
    palisadecycle: {
        store_id: "3658",
        locationId: "3955",
        store_name: "palisadecycle",
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************.jTX_BLbjjO8zrvCfcKT2xKjv6LSMVLXlAyE8COtdCJ4",
    },
    sunandfun: {
        store_id: "3703",
        locationId: "3969",
        store_name: "sun-and-fun",
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJkYXRlIjoiMjAyNS0wMi0xMiAwODoyOTowNCIsInN0b3JlX2lkIjozNzAzLCJzdG9yZV91aWQiOiIwMDg1YjI1Y2U5ODAxMWVmODA3YTAyM2QwYzYzNDI2OSIsInN0b3JlX25hbWUiOiJhdGsyY2l3OWtuZDVqZiIsInNvdXJjZSI6Im9ubGluZSIsImlzX29ubGluZSI6MSwibG9jYXRpb24iOjM5Njl9.QFKVz_2W43Cg4ud_TKhc2vqUSdXkNO0EtX0QvF2lPBw",
    },
    totallytubular: {
        store_id: 3733,
        locationId: 3999,
        store_name: 'totallytubular',
        access_token: 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjo0Nzk3LCJkYXRlIjoiMjAyNS0wNS0wNSAxMzoyMDozMyIsInN0b3JlX2lkIjozNzMzLCJsb2NhdGlvbiI6Mzk5OSwiYXBwX2lkIjo1NDcsImV4cGlyZSI6IjIwMjUtMDUtMDYgMTM6MjA6MDAiLCJpc19vbmxpbmUiOjAsInNvdXJjZSI6ImFwaSIsImRpc2FibGVfZGVmYXVsdF90aW1lIjpmYWxzZX0.bMXVEys-CJQAuXee5n6P-_8coDzTgiLLdvoDRVDtZ4k',
    },
    exquisite: {
        store_id: "2162",
        locationId: "2307",
        store_name: "exquisite-costumes",
        access_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJ1c2VyX2lkIjoyNjkzLCJkYXRlIjoiMjAyNC0xMi0xOSAxMzozMDoxOCIsInN0b3JlX2lkIjoyMTYyLCJsb2NhdGlvbiI6MjMwNywiYXBwX2lkIjo1MDgsImV4cGlyZSI6IjIwMjQtMTItMjAgMTM6MzA6MDAiLCJpc19vbmxpbmUiOjAsInNvdXJjZSI6ImFwaSIsImRpc2FibGVfZGVmYXVsdF90aW1lIjpmYWxzZX0.GbYJjOcLezLw_z7GKGuf6fX-nSmy_WAqtPhpSAHxp3s",
    },
}

var DOMAIN = 'http://127.0.0.1:5502/html-server/';
// var DOMAIN = 'http://localhost:5502/html-server/';


var RENTMY_GLOBAL = {
    ...stores.vgc,
    // ...stores.wix,
    // ...stores.teststore09,
    // ...stores.teststore17,
    // ...stores.teststore22_stage1,
    // ...stores.teststore02,
    // ...stores.rentMasonBees,
    // ...stores.sunandfun,
    // ...stores.totallytubular,
    ...stores.exquisite,

    env: { // optional
      API_BASE_URL : "",  
      ASSET_URL : "",     
      PAYMENT_DOMAIN : " http://localhost:4400/",
    },
    emDateTimePicker: { // optional
      // theme: 'dark',
      // colors: {
      //   primary_bg: '#1ba608',
      // },

      // time picker ui
      timePickerUi: 'standard', // optional
      timePickerButtons: true, // optional
      
      /* ------------------------------------------- */
      /*                 Details Page                */
      /* ------------------------------------------- */  
     

      // Start Date config
      detailsPage_startDatePicker_ajdustX: -12, //required for wix
      detailsPage_startDatePicker_ajdustY: 8, //required for wix
      detailsPage_startDatePicker_displayIn: 'modal', //required for wix
      detailsPage_useRangePicker_for_startDate: false, //required for wix
      detailsPage_startDate_allowRightSideTimePicker: true, //required for wix

      // End Date config
      detailsPage_endDatePicker_ajdustX: -13, //required for wix
      detailsPage_endDatePicker_ajdustY: -26, //required for wix
      detailsPage_endDatePicker_displayIn: 'modal', //required for wix
      detailsPage_useRangePicker_for_endDate: true, //required for wix
      detailsPage_endDate_allowRightSideTimePicker: true, //required for wix
      
      /* ------------------------------------------- */
      /*                   Cart Page                 */
      /* ------------------------------------------- */ 
      cartPage_datePicker_ajdustX: 0, // optional
      cartPage_datePicker_ajdustY: 0, // optional
      
      /* ------------------------------------------- */
      /*               In Page Cart Widget           */
      /* ------------------------------------------- */ 
      afterAddtoCart_open_widget: true, // optional
      afterAddtoCart_open_widget_datePicker: false,
    },

    afterOrder: {
      paymentSuccessUrl: '',
      paymentCancelUrl: '',
      justEmit: false,
      forIframe_topMode: false,
    },

    using_in_cli_project: "",
    ajax_url: "",
    home_url: "index.html",
    is_login_page: false, // optional
    wp_current_user: "1",
    after_add_to_cart_redirecto_cart: true, // optional    
    detailsPage_priceLimitShowFirst: 3, // optional

    useBackButtonFromInpageCartWidget: true, // optional

    checkout_extraCheckboxText: 'Kindly be aware that majority of our rentals items are not brand new and may exhibit wear and tear from previous events. Items are available for rent/lease in Good/Fair condition', // optional (if passes any text, It will be requred to create order)
    checkout_extraCheckboxText_alertText: '', // If not checked, show this mesage in toaster

    product_pacakge_by_slug: true,

    page: {
        products_list: "index.html",
        // product_details: "product-details-wix.html?uid={uuid}",
        product_details: "product-details.html?uid={uuid}",
        package_details: "package-details.html?uid={uuid}",
        cart: "cart.html",
        checkout: "checkout.html",
        login: "customer-login.html",
        registration: "customer-registration.html",
        partner_login: "partner-login.html",
        partner_registration: "partner-registration.html",
        reset_password: "customer-reset-password.html",
        customer_profile: "customer-profile.html",
        customer_change_password: "customer-change-password.html",
        customer_change_avatar: "customer-change-avatar.html",
        customer_order_history: "customer-order-history.html", 
        order_details: "customer-order-details.html",
        event_management: "rentmy-event-management/",
        rentmy_dashboard: "wp-admin/?page=rentmy",
        order_complete: "order-complete.html",
        wish_list: "wish-list.html?token={token}",
        // wish_list: "wish-list/{token}",
    },
    "is_varified_rentmy_event_nonce": "",
    contents: {
        use_dynamic_labels: true,
    },
}
