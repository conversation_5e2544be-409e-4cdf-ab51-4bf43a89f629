<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css">
  <script src="https://s3.us-east-2.amazonaws.com/cdn.rentmy.co/wp/em-datetimepicker.min.js" ></script>
  <script src="./EventEmitter.js"></script>
  <script src="./config.js"> </script> 
  
    <!-- <script src="script_prod.js" defer></script> -->
    <!-- <link rel="stylesheet" href="index.css">  -->
    <script src="http://localhost:4444/assets/script_prod.js" defer></script>
    <link rel="stylesheet" href="http://localhost:4444/assets/index.css"> 
  
</head>
<body class="container border">  
  <main class="my-5">



    <div class="RentMyContactUsWrapper RentMyWrapper">
        <div class="paragraph-content-body">
            <h2 class="fs-32">Contact us!</h2>
            <form class="mt-4">
                <div class="row"> 
                    <div class="col-md-6">
                        <label class="form-label" for="">First Name </label>
                        <div class="form-group">
                            <input data-rentmyattr="first_name" type="text" class="form-control" /> 
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="">Last Name </label>
                        <div class="form-group">
                            <input data-rentmyattr="last_name" type="text" class="form-control" /> 
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="">Phone </label>
                        <div class="form-group">
                            <input data-rentmyattr="phone" type="text" class="form-control" /> 
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="">Email </label>
                        <div class="form-group">
                            <input data-rentmyattr="email" type="email" class="form-control" /> 
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label" for="">extra_field </label>
                        <div class="form-group">
                            <input data-rentmyattr="extra_field" type="email" class="form-control" /> 
                        </div>
                    </div>
                    <div class="col-md-12">
                        <label class="form-label" for="">Questions / Comments:</label>
                        <div class="form-group">
                            <textarea data-rentmyattr="message" class="form-control"></textarea>
                        </div>
                    </div>
                </div> 
                 
                <div class="row" data-rentmyattr="CaptchaCodeArea">
                    <div class="col-md-12 mt-3">
                        <img src="" alt="" data-rentmyattr="CaptchaCode" class="RentMyCaptchaImage">
                    </div>
                    <div class="col-md-3 col-6">
                        <label class="form-label" for="">Answer:</label>
                        <div class="form-group">
                            <input data-rentmyattr="answer" class="form-control"></input>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-12 mt-3">
                        <button data-rentmyattr="submitButton" type="button" class="RentMyBtn RentMyBtnBlack" >Submit</button>
                    </div>
                </div>
            </form>
        </div>
    </div> 


    <!-- New letter modal code here -->



  </main>
<script>
    RentMyEvent.add_filter('contactus_success_message', (message) => {  
        return message
    })

    RentMyEvent.add_filter('contactus_payload', (payload) => { 
        /* modify here and the return */
        return {...payload}
    })

    RentMyEvent.add_filter('contactus_required_keys', (fields) => {  
        return {...fields}
   })
</script>
</body>
</html>