<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"> -->
  <!-- <script src="./EventEmitter.js"></script> -->
  <script src="./config.js"> </script>

  <!-- <script src="script_prod.js" defer></script> -->
  <!-- <script src="http://localhost:4444/assets/script_prod.js" defer></script> -->
  <!-- <script >
    var DOMAIN = 'test.com';
    var RENTMY_GLOBAL = {}; 
  </script> -->
  <script src="http://localhost:4444/assets/script_prod.js" ></script>
  <link rel="stylesheet" href="http://localhost:4444/assets/index.css"> 
  
</head>
<body class="container border">  
  <main class="my-5"> 

    <div id="RentMyCustomerChangeAvatarContainer" class="RentMyWrapper RentMyCustomerPortalWrapper">
        <div class="RentMyCustomPortalRow">
            <div class="RentMyLeftSidebarmenu">
                <div class="RentMyLeftSidebarmenuInner" data-rentmyattr="SideBar">
                    <div class="RentMyProfileImge">
                        <img src="" alt="" data-rentmyattr="ProfileImage">
                    </div>
                    <h5 class="RentMyProfileName" data-rentmyattr="customer_name">{{ customer_name }}</h5>
                    <div class="RentMySideMenu">
                        <ul>
                            <li><a data-rentmyattr="RentMyPageLink=customer_profile">Profile</a></li>
                            <li><a data-rentmyattr="RentMyPageLink=customer_change_password">Change Password</a></li>
                            <li><a data-rentmyattr="RentMyPageLink=customer_change_avatar" class="active">Change Avatar</a></li>
                            <li><a data-rentmyattr="RentMyPageLink=customer_order_history">Order History</a></li>
                            <li><a class="rentmy_logout_btn">Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="RentMyRightContent">
                <div class="RentMyPageHeader">
                    <h3>Change Avatar</h3>
                    <div class="RentMyPageHeaderRightSide"> </div>
                </div>
                <div class="RentMyContentBody">
                    <div class="RentMyContentBodyInner">
                        <form id="RentMyFileUploadForm">
                            <div class="RentMyRow">
                                <div class="RentMyAlertMessage RentMyFullwidth"></div>
                                <div class="RentMyInputGroup RentMyFullwidth">
                                    <label for="file">Upload Image (Maximum file size 2MB)</label>
                                    <div class="RentMyFileUpload" data-rentmyattr="FileUploadArea">
                                        <div class="FileSelect">
                                            <div class="FileSelectButton">Choose File</div>
                                            <div class="FileSelectName" data-rentmyattr="FileName">No file chosen...</div> 
                                            <input type="file" name="chooseFile">
                                        </div>
                                    </div>
                                </div>
                                <div class="RentMyButtonGroup RentMyNotBetween">
                                    <button id="RentMyFileUploadSubmitBtn" type="button" class="RentMyBtn RentMyUploadBtn">Upload</button>
                                    <img class="PreviewImage" data-rentmyattr="PreviewImage" src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8NzZ8fHByb2ZpbGV8ZW58MHx8MHx8fDA%3D" />
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

  </main>
<script>

</script>
</body>
</html>