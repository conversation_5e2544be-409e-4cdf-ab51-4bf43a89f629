<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"> -->
  <!-- <script src="./EventEmitter.js"></script> -->
  <script src="./config.js"> </script>

  <!-- <script src="script_prod.js" defer></script> -->
  <!-- <script src="http://localhost:4444/assets/script_prod.js" defer></script> -->
  <!-- <script >
    var DOMAIN = 'test.com';
    var RENTMY_GLOBAL = {}; 
  </script> -->
  <script src="http://localhost:4444/assets/script_prod.js" ></script>
  <link rel="stylesheet" href="http://localhost:4444/assets/index.css"> 
  
</head>
<body class="container border">  
  <main class="my-5"> 

    
    <div id="RentMyCustomerOrderHistory" class="RentMyWrapper RentMyCustomerPortalWrapper" >
        <div class="RentMyCustomPortalRow">
            <div class="RentMyLeftSidebarmenu">
                <div class="RentMyLeftSidebarmenuInner" SideBar>
                    <div class="RentMyProfileImge">
                        <img src="" alt="" ProfileImage>
                    </div>
                    <h5 class="RentMyProfileName">{{ customer_name }}</h5>
                    <div class="RentMySideMenu">
                        <ul>
                            <li><a RentMyPageLink="customer_profile">Profile</a></li>
                            <li><a RentMyPageLink="customer_change_password">Change Password</a></li>
                            <li><a RentMyPageLink="customer_change_avatar">Change Avatar</a></li>
                            <li><a RentMyPageLink="customer_order_history" class="active">Order History</a></li>
                            <li><a class="rentmy_logout_btn">Logout</a></li>
                        </ul>
                    </div>
                </div>
            </div>
    
            <div class="RentMyRightContent">
                <div class="RentMyPageHeader">
                    <h3>Order History</h3>
                    <div class="RentMyPageHeaderRightSide"> </div>
                </div>
                <div class="RentMyContentBody">
                    <div class="RentMyContentBodyInner">
                        <div id="RentMyOrderHistory" class="RentMyTableResponsive">
                            <table class="RentMyTable RentMyTableStriped" OrderListTable>
                                <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Address</th>
                                        <th>Quantity</th>
                                        <th>Price</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>{{order_id}}</td>
                                        <td>{{order_address}}</td>
                                        <td>{{total_quantity}}</td>
                                        <td>{{total_price}}</td>
                                        <td>{{status}}</td>
                                        <td class="InlineFlex">
                                            <a ViewDetails><i class="fa fa-eye"></i></a>
                                            <a DownloadPDF><i class="fa fa-file-pdf"></i></a>
                                            <a DeleteOrder><i class="fa fa-trash"></i></a>
                                            <!-- <a SendMessage><i class="fa fa-paper-plane"></i></a> -->
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div id="RentMyPagination"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>


  </main>
<script>

</script>
</body>
</html>