<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"> -->
  <!-- <script src="./EventEmitter.js"></script> -->
  <script src="./config.js"> </script>

  <!-- <script src="script_prod.js" defer></script> -->
  <!-- <script src="http://localhost:4444/assets/script_prod.js" defer></script> -->
  <!-- <script >
    var DOMAIN = 'test.com';
    var RENTMY_GLOBAL = {}; 
  </script> -->
  <script src="http://localhost:4444/assets/script_prod.js" ></script>
  <link rel="stylesheet" href="http://localhost:4444/assets/index.css"> 
  
</head>
<body class="container border">  
  <main class="my-5"> 

    <div id="RentMyResetPasswordContainer" class="RentMyWrapper">
        <div class="Contents">
            <div class="card-body pb-4">
                <div class="userlogin-box">
                    <div class="Describer">
                        <img src="" alt="forgot password">
                        <h1 class="Title" data-rentmyattr="Title">It happens to all of us.</h1>
                        <p data-rentmyattr="SubTitle">Enter your email to reset your password</p>
                    </div>
                    <div class="RentMyAlertMessage RentMyFullwidth" data-rentmyattr="AlertMessage"></div>               
                    <form>                      
                        <input type="email" class="RentMyInputField" placeholder="Email*" Email/>
    
                        <input type="password" class="RentMyInputField" placeholder="New Password" data-rentmyattr="Password"/> 
                        <input type="password" class="RentMyInputField" placeholder="Confirm Password" data-rentmyattr="ConfirmPassword"/>
                        
                        <div class="d-flex justify-content-center RentMyButtonGroup">
                            <button type="submit" class="RentMyBtn RentMyBackBtn RentMySubmitBtn" data-rentmyattr="RentMySubmitBtn">Submit</button>
                            <button type="submit" class="RentMyBtn RentMyBackBtn RentMyReturnBtn" data-rentmyattr="RentMyReturnBtn">Return</button>
                        </div>                            
                    </form>
                    
                </div>
            </div>
        </div>
    </div> 

  </main>
<script>

</script>
</body>
</html>