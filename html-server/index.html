<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"> -->
  <!-- <script src="./EventEmitter.js"></script> -->
  <script src="./config.js"> </script>

  <!-- <script src="script_prod.js" defer></script> -->
  <!-- <script src="http://localhost:4444/assets/script_prod.js" defer></script> -->
  <!-- <script >
    var DOMAIN = 'test.com';
    var RENTMY_GLOBAL = {}; 
  </script> -->
  <script src="http://localhost:4444/assets/script_prod.js" ></script>
  <link rel="stylesheet" href="http://localhost:4444/assets/index.css"> 

  <script>
    /**
     * RentMyData="limit=10 products=45,77,44 search=test\sproduct tags=44,66 sort_dir=ASC sort_by=name
     * Note: in search key, instead of space use back-slash (e.g. search=test\sproduct)
     * Example with all key :: RentMyData="
     * limit=10 
     * products=170720,186414,193913,75615,194085,170715,198529,191883,6106,193943,160074,197960,5041,201434,191878,5039
     * search=test\sproduct 
     * category=a311cc3b66c011ea82610212d7dcece2
     * tags=668,4960,164,163 
     * sortBy=product_name|rent_price|buy_price 
     * sort=ASC|DESC
     * onLoad=paginate
     * lazyloadByClick=true // pass [lazyloadByClick] attribute any element to workable this 
     */

  </script>
  
</head>
<body class="container border">  
  <main class="my-5">

    <div rentmy-search-widget></div>

    <p>
        <div class="RentMyWishlistCounter"></div>
    </p>
    <p>
        <div class="RentMyMiniCart"></div>
    </p>


    <!-- <iframe src="iframe.html" frameborder="0"></iframe> -->
    <!-- <div class="RentMyWrapperProductList RentMyWrapper" data-RentMyData="limit=21 category=ac8eddb3301d11ec96ec02caec14a78c"> -->        
    <div class="RentMyWrapperProductList RentMyWrapper" data-RentMyData="limit=21">
        <div class="RentMyProductRow RentMyProductListRow">

            <div class="RentMyFilterArea" data-rentmyattr="RentMyFilterArea">
                <div class="RentMyFilterAreaInner">
                    <div class="RentMyCategory">
                        <h3 class="RentMyFilterTitle">Category</h3>
                        <div class="CategoryMenuList scrollbar">
                            <ul class="CategoryMenu" data-rentmyattr="RentMyFilterByCategory">
                                <li><a href="#"><span>Category A</span> <i class="fa fa-angle-right"></i></a></li>
                                <li><a href="#"><span>Category B</span> <i class="fa fa-angle-right"></i></a></li>
                                <li><a href="#"><span>Category C</span> <i class="fa fa-angle-right"></i></a></li>
                                <li><a href="#"><span>Category D</span> <i class="fa fa-angle-right"></i></a></li>
                                <li><a href="#"><span>Category E</span> <i class="fa fa-angle-right"></i></a></li>
                                <li><a href="#"><span>Category F</span> <i class="fa fa-angle-right"></i></a></li>
                            </ul>
                        </div>
                    </div>
            
                    <div class="RentMyFilter">
                        <div class="RentMyFilterList">
                            <h3 class="RentMyFilterTitle">Filter</h3>
                            <div class="FilterCheckbox scrollbar" data-rentmyattr="RentMyFilterByTag">
                                <label class="RentMyCheckbox">
                                    <input type="checkbox">
                                    <span>&nbsp;&nbsp;</span>
                                    <a href="#">give donate</a>
                                </label>
                                <label class="RentMyCheckbox">
                                    <input type="checkbox">
                                    <span>&nbsp;&nbsp;</span>
                                    <a href="#">give donate</a>
                                </label>
                                <label class="RentMyCheckbox">
                                    <input type="checkbox">
                                    <span>&nbsp;&nbsp;</span>
                                    <a href="#">give donate</a>
                                </label>
                            </div>
                        </div>
                        <div class="RentMyPriceArea">
                            <h3 class="RentMyFilterSubTitle">Price</h3>
                            <div class="RentMyPrice">
                                <div class="RentMyRow" data-rentmyattr="RentMyFilterByPrice">
                                    <div class="RentMyInputGroup RentMyHalfwidth">
                                        <label for="old_password" data-rentmyattr="RentMyFilterByPriceMinLabel">Min</label>
                                        <input type="text" class="RentMyInputField" data-rentmyattr="RentMyMinPrice" />
                                    </div>
                                    <div class="RentMyInputGroup RentMyHalfwidth">
                                        <label for="old_password" data-rentmyattr="RentMyFilterByPriceMaxLabel">Max</label>
                                        <input type="text" class="RentMyInputField" data-rentmyattr="RentMyMaxPrice" />
                                    </div>
                                    <div class="RentMyButtonGroup RentMyNotBetween">
                                        <button class="RentMyBtn RentMyBtnBlack" data-rentmyattr="RentMyMinMaxSubmitBtn">Submit</button>
                                        <button class="RentMyBtn RentMyBtnRed" data-rentmyattr="RentMyMinMaxClearBtn">Clear</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="RentMyTypeArea">
                            <h3 class="RentMyFilterSubTitle">Type</h3>
                            <div class="RentMyType" data-rentmyattr="RentMyFilterByRentalType">
                                <label class="RentMyRadio" data-rentmyattr="RentMyFilterByLabelRent">
                                    <input type="radio" name="RentalType" value="rent" />
                                    Rent
                                    <span></span>
                                </label>
                                <br />
                                <label class="RentMyRadio" data-rentmyattr="RentMyFilterByLabelBuy">
                                    <input type="radio" name="RentalType" value="buy" />
                                    Buy
                                    <span></span>
                                </label>
                                <br />
                                <label class="RentMyRadio" data-rentmyattr="RentMyFilterByLabelAll">
                                    <input type="radio" name="RentalType" value="all" />
                                    All
                                    <span></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Product Area -->
            <div class="RentMyProductArea">
                <div class="RentMyRow SortProductRow">
                    <div class="SortProduct">
                        <label>Sort By</label>
                        <select class="RentMyInputField" data-rentmyattr="RentMyShortByDropdown">
                            <option value="sort_by=product_name&sort_dir=ASC">Product name A-Z</option>
                            <option value="sort_by=product_name&sort_dir=DSC">Product name Z-A</option>
                            <option value="sort_by=rent_price&sort_dir=ASC">Rental price low to high</option>
                            <option value="sort_by=rent_price&sort_dir=DSC">Rental price high to low</option>
                            <option value="sort_by=buy_price&sort_dir=ASC">Sale price low to high</option>
                            <option value="sort_by=buy_price&sort_dir=DSC">Sale price high to low</option>
                        </select>
                    </div>
                </div>
                <div class="RentMyRow">
                    <div class="RentMyProductItem" data-rentmyattr="RentMyProductItemSample">
                        <div class="RentMyProductItemInner">
                            <div class="RentMyProductImg">
                                <a href="#" data-rentmyattr="RentMyProductImageUrl">
                                    <img data-rentmyattr="RentMyProductImage"
                                        src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/193260/hrfnd9e_1669558177_2qqwq6o.jpg"
                                        class="ProductImg" alt="product img" />
                                </a>
                                <div class="RentMyProductOverlay">
                                    <a class="ProductCartIcon" href="#" data-rentmyattr="DetailsPageUrl" ><i class="fa fa-shopping-bag"></i></a>

                                    <div class="WishlistSingleItemOption" data-rentmyattr="WishListBtnArea">
                                        <button class="WishlistAddButton" data-rentmyattr="RentMyAddToWishListBtn"> <i class="la la-heart-o" ></i> </button>              
                                    </div>
                                    
                                </div>
                            </div>
                            <div class="RentMyProductBody">
                                <h4 class="ProductName" data-rentmyattr="RentMyProductName"><a href="#" data-rentmyattr="product_name">{{ product_name }}</a></h4>
                                <h5 class="ProductPrice" data-rentmyattr="RentMyProductPrice">{{ product_price }}</h5>
                                <div class="ProductButton" data-rentmyattr="ProductButtons">
                                    <a class="ProductDetailsBtn" href="#" data-rentmyattr="RentMyViewDetailsBtn">View Details</a>
                                    <button class="ProductCartBtn" href="#" data-rentmyattr="RentMyAddToCartBtn">Add to Cart</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

      </div>
    </div>




  </main>
<script>

</script>
</body>
</html>