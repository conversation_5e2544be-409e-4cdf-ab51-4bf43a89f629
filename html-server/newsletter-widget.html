<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"> -->
  <!-- <script src="./EventEmitter.js"></script> -->
  <script src="./config.js"> </script>

  <!-- <script src="script_prod.js" defer></script> -->
  <!-- <script src="http://localhost:4444/assets/script_prod.js" defer></script> -->
  <!-- <script >
    var DOMAIN = 'test.com';
    var RENTMY_GLOBAL = {}; 
  </script> -->
  <script src="http://localhost:4444/assets/script_prod.js" ></script>
  <link rel="stylesheet" href="http://localhost:4444/assets/index.css"> 
  
</head>
<body class="container border">  
  <main class="my-5"> 


    <!-- 
    ============== 
    This page UI is not ok, will be fix later
    But, funnctional
    
    -->

    <div id="RentMyNewLetterFormContainer" class="RentMyWrapper">
        <div>
            <h3 class="LoginTitle">Get $10 off your next move</h3>
            <div class="RentMyAlertMessage" RentMyAlertMessage></div>

            <form class="RentMyFrom" data-rentmyattr="RentMyNewLetterForm">
                <div class="RentMyInputGroup">
                    <input type="text" name="email" class="RentMyInputField" placeholder="Email" />
                </div>
                <div class="RentMyInputGroup"> 
                    <input type="text" name="date" class="RentMyInputField" placeholder="Estimated Move Date(MM-DD-YYYY)" />
                </div>
                <div class="RentMyButtonGroup">
                    <button class="RentMyBtn" type="submit" data-rentmyattr="RentMyNewLetterSubmitBtn">Submit for Code</button> 
                </div>
            </form>
        </div>
    </div>

    <div class="RentMyWrapperInpageCartWidget RentMyWrapper" data-backbutton="true"></div>



  </main>
<script>

</script>
</body>
</html>