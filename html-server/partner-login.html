<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css"> -->
  <!-- <script src="./EventEmitter.js"></script> -->
  <script src="./config.js"> </script>

  <!-- <script src="script_prod.js" defer></script> -->
  <!-- <script src="http://localhost:4444/assets/script_prod.js" defer></script> -->
  <!-- <script >
    var DOMAIN = 'test.com';
    var RENTMY_GLOBAL = {}; 
  </script> -->
  <script src="http://localhost:4444/assets/script_prod.js" ></script>
  <link rel="stylesheet" href="http://localhost:4444/assets/index.css"> 
  
</head>
<body class="container border">  
  <main class="my-5"> 

    <div id="RentMyCustomerLoginContainer" class="RentMyWrapper PartnerLogin">
        <div class="LoginElement">
            <h3 class="LoginTitle">Partner Login</h3>
            <div class="RentMyAlertMessage"></div>
            <form class="RentMyFrom" id="RentMyCustomerLoginForm">
                <div class="RentMyInputGroup">
                    <input type="text" name="email" class="RentMyInputField" placeholder="Email" />
                </div>
                <div class="RentMyInputGroup">
                    <input type="password" name="password" class="RentMyInputField" placeholder="Password" />
                </div>
                <div class="RentMyButtonGroup">
                    <button type="submit" class="RentMyBtn LoginBtn">Log in</button>
                    <div class="RentMyButtonGroup">
                        <a href="#" class="ForgotPassword" RentMyPageLink="reset_password">Forgot Password?</a>
                        <a href="#" class="NewAccount" RentMyPageLink="partner_registration">Sign Up</a>
                    </div>
                </div>
            </form>
        </div>
    </div>



  </main>
<script>

</script>
</body>
</html>