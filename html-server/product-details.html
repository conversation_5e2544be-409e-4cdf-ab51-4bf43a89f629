<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Document</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css">
  <script src="./EventEmitter.js"></script>
  <script src="./config.js"> </script>

  <!-- <script src="script_prod.js" defer></script> -->
  <!-- <script >
    var DOMAIN = 'test.com';
    var RENTMY_GLOBAL = {}; 
  </script> -->
  <script src="http://localhost:4444/assets/script_prod.js" ></script>
  <!-- <script src="https://s3.us-east-2.amazonaws.com/cdn.rentmy.co/wp/em-datetimepicker.min.js" ></script> -->
  <link rel="stylesheet" href="http://localhost:4444/assets/index.css"> 
  
</head>
<body class="container border">  
  <main class="my-5">

    
    <div class="RentMyWrapperProductDetails RentMyWrapper" data-rentmy="">
        <div class="RentMyProductDetailsRow">
            <div class="RentMyProductDetilsImg">
                <div class="RentMyProductDetailsImgList">
                    <ul data-rentmyattr="RentMyProductImages">
                        <li class="ActiveImg">
                            <img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/hql4fbx_1603633576_59u1aty.jpg"
                                alt="product img" />
                        </li>
                        <li>
                            <img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/rt3n4xn_1603634009_3grjf7a.jpg"
                                alt="product img" />
                        </li>
                        <li>
                            <img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/hql4fbx_1603633576_59u1aty.jpg"
                                alt="product img" />
                        </li>
                        <li>
                            <img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/rt3n4xn_1603634009_3grjf7a.jpg"
                                alt="product img" />
                        </li>
                    </ul>
                </div>
                <div class="RentMyProductDetailsImgShow">
                    <img data-rentmyattr="RentMyProductImage"
                        src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/hql4fbx_1603633576_59u1aty.jpg"
                        alt="product show img" />
                </div>
            </div>

            <div class="RentMyProductDetilsInfo" data-rentmyattr="RentMyProductDetilsInfo">
                <div class="product-payment-details">

                    <h2 class="RentMyProductName" data-rentmyattr="RentMyProductName" data-rentmyattr2="product_name">Product Name</h2>

                    <div class="RentMyBuyRentToggle" data-rentmyattr="RentMyBuyRentToggle">
                        <label for="BuyRentToggleSwitch" class="BuyRentToggleSwitch">
                            <input type="checkbox" id="BuyRentToggleSwitch" data-rentmyattr="BuyRentToggleSwitch" />
                            <div class="ToggleSwitchRound"></div>
                        </label>
                    </div>

                    <div class="ShortDescription" data-rentmyattr="CustomFieldShortDesc">
                        <p>Default Descritipn</p>
                    </div>

                    <h2 class="RentMyProductPrice" data-rentmyattr="RentMyProductPrice" data-rentmyattr2="product_price_text">$0.00</h2>


                    <div class="RentMyRecurring" data-rentmyattr="RentMyRecurring">
                        <h6 data-rentmyattr="RecurringTitle">Recurring Pricing</h6>
                        <ul data-rentmyattr="RecurringList">
                            <li data-rentmyattr="RecurringItem">
                                Recurring
                            </li>
                        </ul>
                    </div>


                    <div class="RentMyVariant" data-rentmyattr="RentMyVariant">
                        <h6 data-rentmyattr="VariantTitle">Rent My Variant Sizes</h6>
                        <ul data-rentmyattr="VariantList">
                            <li data-rentmyattr="VariantItem">Small</li>
                        </ul>
                    </div>


                   <!-- Old product option HTML 0000-->
                    <!-- <div RentMyProductOptions>
                        <div class="RentMyProductOptions" ProductOptionsItem>
                            <div class="CustomFieldInner">
                                <h6 ProductOptionsTitle>RentMy Product Options</h6>
                                <select></select>
                                <h6 class="product-option-predefined-radio" others ></h6>
                            </div>
                        </div>
                    </div> -->

                     <!-- New product option HTML -->
                    <div class="RentMyProductOptions" data-rentmyattr="RentMyProductOptions">
                        <div class="CustomFieldInner">
                            <h6 data-rentmyattr="ProductOptionsTitle" class="mt-3" >Product Options</h6>
                            <ul data-rentmyattr="ProductOptionsItem">
                                
                                <div data-rentmyattr="type-select" >
                                    <p data-rentmyattr="fieldLabel" class="m-0 mt-2" ></p>
                                    <select ></select>
                                </div>
                                <div data-rentmyattr="type-button" >
                                    <p data-rentmyattr="fieldLabel" class="m-0 mt-2" ></p>
                                    <li data-rentmyattr="fieldValue" ></li>
                                </div>
                                <div data-rentmyattr="type-radio" >
                                    <!-- <p fieldLabel class="m-0 mt-2" ></p>
                                    <li fieldValue ></li> -->
                                </div>
                                <div data-rentmyattr="type-richtext" >
                                    <p data-rentmyattr="fieldLabel" class="m-0 mt-2" ></p>
                                    <li data-rentmyattr="fieldValue" ></li>
                                </div>
                            </ul>
                        </div>
                    </div>


                     <!-- New product option HTML For Ticket booking -->
                     <div class="RentmyTicketArea" data-rentmyattr="RentmyTicketBookingProductOptionsArea">
                        <div class="RentmyTicketAreaInner" data-rentmyattr="CustomFieldInner">
                            <h6 class="RentmyTicketLabel" data-rentmyattr="ProductOptionsTitle">Tickets</h6>
                            <!-- Each item -->
                            <div class="RentmyTicketBox" data-rentmyattr="ProductOptionsItem">
                                <div class="RentmyTicketSelect">
                                    <button class="RentmyQtMinusBtn" data-rentmyattr="RentmyQtPlusBtn" ><i class="la la-angle-up"></i></button>
                                    <input type="text" class="RentmyQtInput" data-rentmyattr="RentmyQtInput" />
                                    <button class="RentmyQtPlusBtn" data-rentmyattr="RentmyQtMinusBtn" ><i class="la la-angle-down"></i></button>
                                </div>
                                <div type-button class="RentmyTicketOtherInfo">
                                    <h5 data-rentmyattr="fieldLabel">Adult</h5>
                                    <span data-rentmyattr="fieldValue">zł10.00</span>
                                </div>
                            </div> 
                            <div class="QtyContainer" data-rentmyattr="QtyContainer">
                                <small class="AvailableItem"> 
                                    <span data-rentmyattr="RentmyAvailableLabel">Available</span>:
                                    <span data-rentmyattr="RentmyAvailableQty">17</span>     
                                </small>
                            </div>
                        </div>
                    </div>


                    <div class="RentMyRentalStartDate" data-rentmyattr="RentMyRentalStartDate">
                        <div data-rentmyattr="usualDateRange">
                            <h6 data-rentmyattr="RentalStartDateTitle">Select Rental Start Date</h6>
                            <ul data-rentmyattr="RentalStartDateList">
                                <li data-rentmyattr="Today">Today</li>
                                <li data-rentmyattr="Tomorrow">Tomorrow</li>
                                <li data-rentmyattr="PickDate">Pick Date</li>
                            </ul>
                            <span data-rentmyattr="RentalStartDateSelectedLabel">
                                Today 08:00 AM
                            </span>
                        </div>
                    </div>

                    <!-- For Exact Times -->  
                    <div class="RentMyBookingExactTimes" data-rentmyattr="RentMyBookingExactTimes">
                        <div data-rentmyattr="TimeArea">
                            <h6 data-rentmyattr="TitleTag">Select Start time</h6>
                            <ul>
                                <li data-rentmyattr="exactTimeItem" data-rentmyattr="activeClass=timeActive">12:00 AM</li>
                            </ul>
                        </div>
                        <div class="TourNotAvailableMsg" data-rentmyattr="TourNotAvailableMessageArea">
                            This tour is not available on the date you selected. Please pick another date.
                        </div>
                    </div>


                    <div class="RentMyRentalDateRange" data-rentmyattr="RentMyRentalDateRange">
                        <h6 data-rentmyattr="RentalDateRangeTitle">
                            Rental Date Range
                        </h6>
                        <ul class="mb-4" data-rentmyattr="RentalDateRangeList">
                            <li data-rentmyattr="RentalDateRangeItem">
                                <span data-rentmyattr="PricePreText">1 hour</span>
                                <!-- <span Devider></span> -->
                                 <br />
                                 <span data-rentmyattr="Price">$10.00</span>
                            </li>
                        </ul>
                    </div>


                    <div class="RentMyRentalDateRange mb-3" data-rentmyattr="RentMyRentalEndDate">
                        <ul>
                            <li class="mt-0" data-rentmyattr="RentalEndDatePicker">
                                <span data-rentmyattr="rangePickerLabel" >Pick Date Range</span>
                            </li>
                        </ul>

                        <span data-rentmyattr="RentalEndDateSelectedLabel">
                            Today 09:00 AM
                        </span>
                    </div>                   

                    <div class="RentMyExactSelectDuration" data-rentmyattr="RentMyExactSelectDuration">
                        <h6 data-rentmyattr="RentMyExactSelectDurationTitle">
                            Select Duration
                        </h6>
                        <ul class="mb-4" data-rentmyattr="RentMyExactSelectDurationList">
                            <li data-rentmyattr="RentMyExactSelectDurationItem">
                                Exact Select Duration
                            </li>
                        </ul>
                    </div>

                    <div class="RentMyExactSelectTime" data-rentmyattr="RentMyExactSelectTime">
                        <h6 data-rentmyattr="RentMyExactSelectTimeTitle">
                            Select Exact Start time
                        </h6>
                        <ul class="mb-4" data-rentmyattr="RentMyExactSelectTimeList">
                            <li data-rentmyattr="RentMyExactSelectTimeItem">
                                Extact Times
                            </li>
                        </ul>
                    </div>


                    <div class="RentMyDeliveryOptions" data-rentmyattr="RentMyDeliveryOptions">
                        <h6 data-rentmyattr="DeliveryOptionsTitle">Delivery Options</h6>
                        <ul data-rentmyattr="DeliveryOptionsList">
                            <li data-rentmyattr="DeliveryOptionsItem">Local Move</li>
                        </ul>
                    </div>


                    <div class="RentMySelectLocation" data-rentmyattr="RentMySelectLocation">
                        <h6 data-rentmyattr="SelectLocationTitle">Select Location</h6>
                        <ul data-rentmyattr="SelectLocationList">
                            <li data-rentmyattr="SelectLocationItem">Default location</li>
                        </ul>
                    </div>


                    <div class="QuantityContainer" data-rentmyattr="RentmyQuantityContainer">
                        <label data-rentmyattr="QuantityContainerTitle">Quantity</label>
                        <div class="QuantityBtn">
                            <button class="RentMyBtn" data-rentmyattr="QuantityDecrementBtn">-</button>
                            <input type="text" autocomplete="off" name="qty" class="InputQuantity" data-rentmyattr="NumberOfQuantity" />
                            <button class="RentMyBtn" data-rentmyattr="QuantityIncrementBtn">+</button>
                        </div>

                        <small class="info"> 
                            <span data-rentmyattr="RentmyAvailableLabel">Available</span>
                            <span data-rentmyattr="RentmyAvailableQty">17</span>
                        </small>
                    </div>

                    <div data-rentmyattr="RentmyAvailableNotice" class="my-2"> </div>

                     <div data-rentmyattr="ProductIdArea">
                       <strong data-rentmyattr="ProductIdLabel">Product ID :</strong><span data-rentmyattr="ProductIdNo"> 000</span>
                     </div>
                    <div class="RentMyCartBtnArea" data-rentmyattr="RentMyCartBtnArea">
                        <button class="RentMyBtn RentMyAddCartBtn" data-rentmyattr="RentMyAddCartBtn">ADD TO CART</button>
                        <button class="RentMyBtn RentMyAddWishlistBtn" data-rentmyattr="RentMyAddToWishlistBtn">Add To Wishlist</button>
                    </div>

                </div>
            </div>
        </div>

        <div class="RentMyProductDescription">
            <h3 class="RentMyProductDesTitle">Product Description</h3>
            <div class="RentMyProductDesBody" data-rentmyattr="RentMyProductDescription"> </div>
        </div>


        <div class="RentMyRelatedProduct">
            <h3 class="RentMyRelatedProductTitle">Related Products</h3>
            <div class="RentMyRelatedProductBody">
                <div class="RentMyRow" data-rentmyattr="RentMyRelatedProducts">
                    <div class="RentMyProductItem" data-rentmyattr="RentMyProductItem">
                        <div class="RentMyProductItemInner">
                            <div class="RentMyProductImg">
                                <a href="#" data-rentmyattr="RentMyProductImageUrl">
                                    <img data-rentmyattr="RentMyProductImage" src="" class="ProductImg" alt="" />
                                </a>
                                
                                <div class="RentMyProductOverlay">
                                    <a class="ProductCartIcon" href="#" data-rentmyattr="DetailsPageUrl" ><i class="fa fa-shopping-bag"></i></a>

                                    <div class="WishlistSingleItemOption" data-rentmyattr="WishListBtnArea">
                                        <button class="WishlistAddButton" data-rentmyattr="RentMyAddToWishListBtn"> <i class="la la-heart-o" ></i> </button>              
                                    </div>
                                    
                                </div>
                            </div>
                            <div class="RentMyProductBody">
                                <h4 class="ProductName" data-rentmyattr="RentMyProductName"> Product_name </h4>
                                <h5 class="ProductPrice" data-rentmyattr="RentMyProductPrice"> product_price </h5>
                                <div class="ProductButton" data-rentmyattr="ProductButtons">
                                    <a class="ProductDetailsBtn" href="#" data-rentmyattr="RentMyViewDetailsBtn">View Details</a>
                                    <button class="ProductCartBtn" href="#" data-rentmyattr="RentMyAddToCartBtn">Add to Cart</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

    <!-- In page cart widget -->
    <div class="RentMyWrapperInpageCartWidget RentMyWrapper"></div>

  </main>
<script>

</script>
</body>
</html>