body {
    margin: 0;
    padding: 0;
    font-family: "Cormorant Garamond", serif;
}

.RmRentalOption {
    margin-bottom: 20px;
}
.RmRentalOption h6 {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 5px;
}
.RmRentalOption ul {
    width: 300px;
    margin: 0;
    padding: 0;
    margin-top: -10px;
}
@media (max-width:450px) {
    .RmRentalOption ul {
        width: 100%;
    }
}
.RmRentalOption ul li {
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 32px;
    border: 1px solid #ccc;
    padding-right: 15px;
    cursor: pointer;
    margin-right: 10px;
    margin-top: 10px;
    border-radius: 5px;
    font-weight: 400;
    color: #495057;
    font-size: 15px;
    overflow: hidden;
}
.RmRentalOption ul li.RmDaterangeActive {
    border: 1px solid #aaa;
    font-weight: 500;
}
.RmRentalOptionDaytime {
    display: flex;
    align-items: center;
    background-color: #f2f3f8;
    width: 85px;
    height: 100%;
    padding-left: 15px;
    border-right: 1px solid #ccc;
}
.RmRentalOption ul li.RmDaterangeActive .RmRentalOptionDaytime {
    border-right: 1px solid #aaa;
}
.BundleItemRow {
    display: flex;
}
.BundleItemRowLeft {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    width: 65px;
}
.BundleItemImgArea {
    display: flex;
    align-items: center;
    width: 100%;
    height: 32px;
    margin-bottom: 10px;
}
.BundleItemImg {
    margin-right: 5px;
}
.BundleItemImg img {
    width: 35px;
    height: 35px;
}
.BundleItemQty {
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    opacity: 0.6;
    padding: 0 6px 0 5px;
    font-size: 14px;
}
.BundleItemRowRight {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}
.BundleItemText {
    display: flex;
    -webkit-box-align: center;
    align-items: center;
    width: 100%;
    height: 32px;
    margin-bottom: 10px;
    padding: 0 5px;
    font-size: 14px;
    color: #344045;
    font-weight: 500;
}
.BundleSelect {
    display: block;
    width: 100%;
    width: 250px;
    height: 32px;
    background: #fff;
    font-size: 13px;
    padding: 0 10px;
    padding-right: 30px;
    border: 1px solid #DDE1E3;
    border-radius: 6px;
    font-family: "Cormorant Garamond", serif;
    font-weight: 600;
    margin: 0px;
    /* -webkit-appearance: none; */
}
.BundleSelect:focus,
.BundleSelect:hover,
.BundleSelect:active {
    border: 1px solid #000;
}


.RmRentalDateRange {
    margin-bottom: 20px;
}
.RmRentalDateRange h6 {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
    margin-bottom: 5px;
}
.RmRentalDateRange ul {
    width: 312px;
    margin: 0;
    padding: 0;
    margin-top: -10px;
}
.RmRentalDateRange ul li {
    display: inline-flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: 32px;
    border: 1px solid #ccc;
    cursor: pointer;
    margin-right: 10px;
    margin-top: 10px;
    border-radius: 5px;
    font-weight: 400;
    color: #495057;
    font-size: 15px;
    overflow: hidden;
}
.RmRentalDateRange ul li.RmDaterangeActive {
    border: 1px solid #aaa;
    font-weight: 500;
}
.RmRentalDateRangeIcon {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f2f3f8;
    width: 40px;
    height: 100%;
    border-left: 1px solid #ccc;
    text-align: center;

}
.RmRentalDateRange ul li.RmDaterangeActive .RmRentalDateRangeIcon {
    border-right: 1px solid #aaa;
}