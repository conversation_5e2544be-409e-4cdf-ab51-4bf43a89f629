<script setup>
import { ref, onMounted, inject, provide } from 'vue';
import AppHolder from './AppHolder.vue';
import { useGlobalStore } from './stores/global';
const globalStore = useGlobalStore();
let isMounted = ref(false);
let { helper } = inject('utils');
 

onMounted( async ()=>{
    // let { getQuery } = helper.withURL

    let rentmy_cdn_apis_initialized = sessionStorage.getItem('rentmy_cdn_apis_initialized')

    if(!sessionStorage.getItem('QA_TESTING_MODE')){
        sessionStorage.setItem('QA_TESTING_MODE', 'no')
    }

    await globalStore.getStoreAndLocations();
    await globalStore.getContents();
    await globalStore.getCurrencyConfig();
    
    if(!rentmy_cdn_apis_initialized){
        sessionStorage.setItem('rentmy_cdn_apis_initialized', RENTMY_GLOBAL?.store_id)
        await globalStore.getCountries();
    } else {
        globalStore.just_update_store_variable('currency_config')
    }   

    isMounted.value = true;
})
</script>

<template>
    <template v-if="isMounted">
        <AppHolder></AppHolder>
    </template>    
</template>