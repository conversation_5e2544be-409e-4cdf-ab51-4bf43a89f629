@keyframes fadeUp {
    from {
        opacity: 0;
        transform: translateY(var(--pixel, 20px));
        /* You can adjust the distance of the fade-up effect */
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Example usage on an element */
.fadeUp {
    animation: fadeUp var(--duration, 0.3s) ease-out;
}

.fadeUp-3ms {
    animation: fadeUp var(--duration, 0.3s) ease-out;
}

.fadeUp-4ms {
    animation: fadeUp var(--duration, 0.4s) ease-out;
}

.fadeUp-5ms {
    animation: fadeUp var(--duration, 0.5s) ease-out;
}

.fadeUp-6ms {
    animation: fadeUp var(--duration, 0.6s) ease-out;
}

.fadeUp-7ms {
    animation: fadeUp var(--duration, 0.7s) ease-out;
}

.fadeUp-8ms {
    animation: fadeUp var(--duration, 0.8s) ease-out;
}

.fadeUp-9ms {
    animation: fadeUp var(--duration, 0.9s) ease-out;
}


/* <span v-if="variable" class="RrentMyBtnLoader"><span> */
span.RrentMyBtnLoader {
    width: var(--size, 16px);
    height: var(--size, 16px);
    border: var(--border, 2px) solid var(--c, #FFF); 
    border-bottom-color: transparent;
    border-radius: 50%;
    display: inline-block;
    box-sizing: border-box;
    animation: rotation var(--speed, .6s) linear infinite;
}

@keyframes rotation {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}