.cp{
    cursor: pointer !important;
}
ul.retnmy-pagination a{
    text-decoration: none;
}


/* note section start */

.note-pragraph {
    display: inline-block;
    width: 30%;
    margin-top: 10px;
}
.note-pragraph p {
    margin-bottom: 0;
    font-weight: 400;
    line-height: 18px;
}
.note-pragraph a {
    font-size: 14px !important;
    text-decoration: none;
    color: #222;
    font-weight: 500;
}
.note-pragraph a:hover {
    text-decoration: underline;
}
.note-pragraph a i {
    font-size: 15px;
    font-weight: bold;
}
@media (max-width:1400px) {
    .note-pragraph {
        width: 30%;
    }
    .note-right {
        width: 40%;
    }
}
@media (max-width:1199px) {
    .note-pragraph {
        width: 20%;
    }
    .note-right {
        width: 30%;
    }
}

/* note section end */

/* item search start */
.admin-cart .search-close-icon {
    position: absolute;
    right: 15px;
    top: calc(50% - 10px);
}

*,*:after,*:before {
    box-sizing: border-box;
}
.item-search-dropdown {
    min-width: 250px; /* It will be change by javaScript */
    width: auto !important; 
    max-width: 350px;
    margin: 0 auto;
    background: var(--rentmy-searboxresult-bg, white);
    cursor: pointer;
    outline: none;
    box-shadow: 0px 4px 9px #00000099;
}

.item-search-dropdown ul.dropdown {
    background-color: var(--rentmy-searboxresult-bg, white);
    transition: all 0.1s ease-out;
    list-style: none;
    opacity: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}
.item-search-dropdown ul.dropdown li a {
    display: block;
    text-decoration: none;
    color: var(--rentmy-searboxresult-text-color, #333);
    padding: 10px;
    transition: all 0.1s ease-out;
}
 
.item-search-dropdown ul.dropdown li i {
    color: inherit;
    vertical-align: middle;
}
 
.item-search-dropdown ul.dropdown li:hover a {
    color: rgb(0, 0, 0);
    background-color: var(--rentmy-searboxresult-text-hover-color, rgb(207, 207, 207));
}
.item-search-dropdown.active:after {
    border-width: 0 6px 6px 6px;
}
 
.item-search-dropdown.active ul.dropdown {
    opacity: 1;
    pointer-events: auto;
    margin: 15px 0px;
    padding: 0;
    background-color: var(--rentmy-searboxresult-bg, white);
    height: 100%;
}
.item-search-dropdown ul.dropdown-item {
    background-color: var(--rentmy-searboxresult-bg, white);
  color: #333;
  margin: 2px 0px;
  padding: 10px 0px 10px 20px;
  border-bottom: 1px solid var(--rentmy-searboxresult-item-border, lightgray);
}
/* item search end */

.form-control{
    width: 100% !important;
}

.form-group label,
.quant label {
  font-weight: 500;
  color: #74788d !important;
}

.admin-quantity span {
  height: 40px !important;
  line-height: 28px;
  padding: 5px 5px;
  margin-top: -2px !important;
}
.size,
.quantity {
  width: 90px;
  height: 35px !important;
}
.quantity.admin-quantity {
  width: 190px;
}
.admin-quantity input {
  height: 40px;
  border: 1px solid #f2f3f8 !important;
  width: 80px;
}

.admin-quantity input:focus {
  box-shadow: none;
  outline: 0;
  border: 1px solid #f2f3f8 !important;
}
@media (max-width: 1199px) {
  .admin-quantity {
    width: 100%;
  }
  .admin-quantity input {
    width: 80px;
  }
}

/* -------------------------------------------------------------------------- */
/*                         Customer Portal Sidebar Css                        */
/* -------------------------------------------------------------------------- */
.rm-wp-customer-wrapper {
    overflow: hidden;
    border-left: 1px solid #eee;
    border-right: 1px solid #eee;
    border-color: #eee #e7e7e7;
    border-style: solid;
    border-width: 1px;
    border-radius: 3px;
    margin-top: 50px;
}
.rm-wp-row {
    flex: 1 0 auto;
    max-width: 100%;
    width: 100%;
    display: flex;
    flex-direction: row;
}
.rm-wp-left-sidebarmenu {
    width: 250px;
    padding: 0;
    flex: 0 0 auto;
}
.rm-wp-sidebarmenu-inner {
    background-color: #fff;
    height: 100%;
    margin-top: 0;
    box-shadow: 0 0 15px 0 rgba(51,77,136,.05);
    border-radius: 3px;
    border-right: 1px solid #eee;
}
.rm-profile-img {
    margin-bottom: 20px;
}
.rm-profile-name {
    padding-bottom: 15px;
    padding-left: 15px;
}
.rm-side-menu ul {
    list-style: none;
    padding: 0;
}
.rm-side-menu ul li a {
    padding: 5px 15px;
    display: block;
    border-bottom: 1px solid #eee;
}
.rm-side-menu ul li:last-child a {
    border-bottom: none;
}
/* .rm-side-menu {
    padding-bottom: ;
} */
.rm-wp-right-content {
    padding: 0;
    flex: 1 auto;
    min-width: 0;
    max-width: 100%;
}
.rm-wp-page-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #fff;
    height: 60px;
    padding-left: 20px;
    padding-right: 20px;
    border-bottom: 1px solid #eee;
}
select{
    border: 1px solid #ccc;
    padding: 6px 12px;
    margin-right: 10px;
    margin-top: 8px;
    border-radius: 3px;
    font-weight: 400;
    color: #777;
    font-size: 14px;
    padding-left: 8px;
    padding-right: 5px;
}
.rm-customer-content {
    padding: 20px 20px;
}
.rm-customer-info {
    justify-content: space-between;
}
.rm-customer-content-inner {
    background-color: #fff;
}
.RentMyNoProductFoundMessage{
    font-weight: 300;
    font-size: 1rem;
    line-height: 25px;
    letter-spacing: 0.2px;
    color: #888;
}
@media screen and (max-width: 900px) {
    .RentMyNoProductFoundMessage{
        text-align: center;
        margin-top: 20px;
    }
}
@media screen and (min-width: 901px) {
    .RentMyNoProductFoundMessage{
        margin-left: 20px;
    }
}

.disabled-cursor {
    opacity: .8;
    cursor: not-allowed !important;
    pointer-events: none !important;
}

.color-variants li{
    width: 28px !important;
    height: 28px !important;
    padding: 0px !important;
}

.startdate-active {
    border: 1px solid #333 !important;
}

.daterange-active {
    border: 1px solid #333 !important;
}
:where(.inside-contents-opacity-0) > *{
    opacity: 0;
}
/* Custom CSS for .rentmy_checkbox */
.rentmy_checkbox {
    appearance: none; 
    -webkit-appearance: none; 
    -moz-appearance: none; 
    width: 20px; 
    height: 20px; 
    border: 2px solid black; 
    border-radius: 3px; 
    background-color: white; 
    cursor: pointer; 
    margin-left: 10px;
    margin-right: 10px;
    position: relative; 
}

.rentmy_checkbox:checked {
    background-color: black;  
}

.rentmy_checkbox:checked::after {
    content: "";
    position: absolute;
    left: 4px;
    top: 1px;
    width: 5px;
    height: 12px;
    border: solid white;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
    margin-left: 2px;
}