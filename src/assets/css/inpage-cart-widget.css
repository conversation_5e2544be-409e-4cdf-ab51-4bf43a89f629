.RentMyWrapperInpageCartWidget .branded,
.RentMyWrapperInpageCartWidget .branded:before,
.RentMyWrapperInpageCartWidget .branded:after {
    background-color: var(--rentmy-cart-widget-color, var(--rentmy-primary-color, var(--rentmy-primary-color-default)));
    color: #FFFFFF !important;
}


/* Widget Launcher */

.RentMyWrapperInpageCartWidget{
    display: none;
}
.RentMyWrapperInpageCartWidget[wrapper-is-ready="true"]{
    display: block;
}

.RentMyWrapperInpageCartWidget #InpageCartWidgetLauncher {
    height: 60px;
    width: 60px;
    position: fixed; 
    bottom: 15px; 
    right: 15px;
    overflow: hidden;
    cursor: pointer;
    -webkit-transform-origin: center;
    -ms-transform-origin: center;
    transform-origin: center;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    box-shadow: 0 5px 40px rgba(0, 0, 0, 0.16) !important;
    border-radius: 8px;
    transition: width 100ms ease-in;
    transition: all 100ms ease-in;
    color: #515151;
    z-index: 999;
}

.RentMyWrapperInpageCartWidget #InpageCartWidgetLauncher:hover {
    width: 365px;
}

.RentMyWrapperInpageCartWidget #InpageCartWidgetLauncher #launcher-icon {
    width: 60px;
    height: 60px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.RentMyWrapperInpageCartWidget #InpageCartWidgetLauncher #launcher-icon i {
    color: #fff;
    font-size: 26px !important;
    display: inline-block;
    line-height: 60px;
}



.RentMyWrapperInpageCartWidget #InpageCartWidgetLauncher hr:not([size]) {
    height: 1px;
    padding: 0px;
    margin: 0px;
}

.RentMyWrapperInpageCartWidget #InpageCartWidgetLauncher .SummaryText {
    display: flex;
}

.RentMyWrapperInpageCartWidget #InpageCartWidgetLauncher .Summary {
    background: #fbfdfc;
    width: 290px;
    padding: 8px 8px;
    height: 60px;
    display: inline-block;
    vertical-align: top;
    font-size: 13px;
}

.RentMyWrapperInpageCartWidget #InpageCartWidgetLauncher .Summary .SummaryText {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.RentMyWrapperInpageCartWidget .CartItemsArea ul.CartItems .QuantityToggler {
    border-radius: 3px;
    border: 1px solid #f1f3f4;
    background: transparent;
    padding: 3px 8px 5px 8px;
    cursor: pointer;
    color: #213B47;
    width: 25px;
    display: inline-block;
    text-align: center;
}

.RentMyWrapperInpageCartWidget .CartItemsArea ul.CartItems .Quantity {
    margin: 0 8px;
}

.RentMyWrapperInpageCartWidget .CartItemsArea ul.CartItems .Item p.ItemPrice {
    text-align: right;
    color: #586C76 !important;
    font-size: 15px;
}

.RentMyWrapperInpageCartWidget .CartItemsArea ul.CartItems .Item .RemoveProduct {
    position: absolute;
    top: 0;
    right: 0;
    padding: 7px;
    cursor: pointer;
    color: #213b47;
    width: 25px;
    display: inline-block;
    text-align: center;
    opacity: 0.5;
    border: none;
    background: transparent;
}



/* Main contents */
.RentMyWrapperInpageCartWidget #Sidebar {
    position: fixed;
    width: 340px;
    height: 100%;
    right: 15px;
    bottom: 15px;
    pointer-events: none;
    z-index: 999;
}

.RentMyWrapperInpageCartWidget .SidebarInner {
    position: absolute;
    background: #fafbfb;
    border-radius: 8px;
    box-shadow: 0 5px 40px rgba(0, 0, 0, 0.16) !important;
    height: 80%;
    width: 100%;
    min-height: 440px;
    min-width: 300px;
    max-height: 700px;
    transition: all 100ms ease-in;
    bottom: 0;
    right: 0;
    overflow: hidden;
    pointer-events: auto;
    display: flex;
    flex-direction: column;
}

.RentMyWrapperInpageCartWidget .SidebarInner .SideBarHeader {
    border-radius: 8px 8px 0 0;
    width: 100%;
    position: relative;
    z-index: 1;
}

.RentMyWrapperInpageCartWidget .jVUQHV .Details-fZFFaa {
    width: calc(100% - 39px);
}

.RentMyWrapperInpageCartWidget .SidebarInner .SideBarHeader .CloseSidebar {
    position: absolute;
    top: 0px;
    right: 0px;
    padding: 15px;
    cursor: pointer;
    font-weight: 400;
    z-index: 3;
    opacity: 0.6;
    border: none;
    background: transparent;
}

.RentMyWrapperInpageCartWidget .SidebarInner .SideBarHeader .HeaderTitle {
    font-size: 10pt;
    text-transform: uppercase;
    text-align: center;
    padding: 20px 0 14px;
    z-index: 2;
    position: relative;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    font-weight: 500;
}

.RentMyWrapperInpageCartWidget .SideBarHeader .WidgetBackButton { 
    padding: 4px 8px;
    position: absolute;
    border-radius: 8px;
    font-weight: 600;
    left: 16px;
    top: 13px;
    border-radius: 50%;
    transition: all 0.3s;
    background: var(--rentmy-bg, var(--rentmy-bg-default));
    color: var(--rentmy-primary-color, var(--rentmy-primary-color-default));
    border: 2px solid var(--rentmy-border, var(--rentmy-border-default));
    box-shadow: 0px 3px 5px #00000003;
    cursor: pointer;
    z-index: 21;
}
.RentMyWrapperInpageCartWidget .SideBarHeader .WidgetBackButton:hover i { 
    transition: all 0.3s;
}
.RentMyWrapperInpageCartWidget .SideBarHeader .WidgetBackButton:hover i { 
    transform: translateX(-2px); 
}

.RentMyWrapperInpageCartWidget .SidebarInner .DateRangeTextAndIcon {
    position: relative;
    display: flex;
    justify-content: space-between;
    -webkit-box-align:start;
    align-items: center;
    padding: 13px 18px !important;
}

.RentMyWrapperInpageCartWidget .CartItemsArea {
    overflow-x: hidden;
    overflow-y: auto;
    width: 100%;
    padding-bottom: 10px;
    position: relative;
    flex-grow: 1;
    z-index: 0;
}

.RentMyWrapperInpageCartWidget .CartItemsArea ul.CartItems {
    list-style: none;
    margin: 0;
    padding: 0px 10px;
}

.RentMyWrapperInpageCartWidget .CartItemsArea ul.CartItems .Item {
    margin: 10px auto 0 auto;
    padding: 10px;
    border-radius: 3px;
    font-size: 16px;
    font-weight: 400;
    background-color: #fff;
    box-shadow: rgba(0, 0, 0, 0.2) 0px 1px 2px 0px;
    position: relative;
    z-index: 2;
    opacity: 1; 
    transform: scale(1);
}

.RentMyWrapperInpageCartWidget .CartItemsArea img {
    border-style: none;
}

.RentMyWrapperInpageCartWidget .CartItemsArea ul.CartItems .Item img.bq-product-image {
    width: 60px;
    display: inline-block;
}

.RentMyWrapperInpageCartWidget .CartItemsArea ul.CartItems .Item .CartLine {
    display: inline-block;
    vertical-align: top;
    margin-left: 10px;
    color: #515151;
    width: 95%;
    font-size: 13px;
    position: relative;
}

.RentMyWrapperInpageCartWidget .CartItemsArea ul.CartItems .Item .CartLine .ItemName {
    line-height: 1.3;
}

.RentMyWrapperInpageCartWidget .SidebarSummery {
    width: 100% !important;
    background: #fff !important;
    border-top: 1px solid #f2f3f4 !important;
    padding: 0 15px !important;
    bottom: 0;
    color: #515151;
    z-index: 3;
}

.RentMyWrapperInpageCartWidget .DeviderLine {
    max-height: 180px;
    overflow-y: auto;
    margin-right: -15px;
    padding-right: 15px;
    background: linear-gradient(rgb(255, 255, 255) 33%, rgba(255, 255, 255, 0)) 0% 0% / 100% 30px no-repeat local, linear-gradient(rgba(255, 255, 255, 0), rgb(255, 255, 255) 66%) 0px 100% / 100% 30px local, radial-gradient(farthest-side at 50% 0px, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0)) 0% 0% / 100% 10px scroll, radial-gradient(farthest-side at 50% 100%, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0)) 0px 100% / 100% 10px scroll rgb(255, 255, 255);
}

.RentMyWrapperInpageCartWidget .SidebarSummery .Details {
    margin-top: 12px;;
    display: flex;
    width: 100%;
    justify-content: space-between;
}

.RentMyWrapperInpageCartWidget .SidebarInner .ButtonGroup {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.RentMyWrapperInpageCartWidget .SidebarInner .ButtonGroup .Button {
    display: block;
    width: 100%;
}

.RentMyWrapperInpageCartWidget .SidebarSummery .Button {
    padding: 10px;
    display: block;
    text-align: center;
    font-size: 16px;
    border-radius: 5px;
    margin: 5px 0;
    text-transform: uppercase;
    cursor: pointer;
    text-decoration: none;
    border: none;
}
.RentMyWrapperInpageCartWidget .SidebarSummery .Button:last-child {
    margin-bottom: 15px;
}



@media (max-width: 534px) {
    
    .RentMyWrapperInpageCartWidget .SidebarInner{
        min-height: 100vh;
    }

    .RentMyWrapperInpageCartWidget #Sidebar {
        width: 100%;
        right: 0;
        bottom: 0
    }

    .RentMyWrapperInpageCartWidget .SidebarInner {
        height: 100%;
        position: fixed;
        z-index: 999;
        top: 0;
        left: 0;
        border-radius: 0
    }

    .RentMyWrapperInpageCartWidget .SidebarInner .SideBarHeader {
        border-radius: 0
    }

    .RentMyWrapperInpageCartWidget .SidebarInner .SideBarHeader .HeaderTitle {
        border-radius: 0
    }

}