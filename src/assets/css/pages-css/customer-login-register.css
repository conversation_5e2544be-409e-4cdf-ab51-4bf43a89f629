#rentmy-customer-login-page .rentmy-customer-login {
    width: 50px;
    height: 50px;
    display: block;
    border-radius: 50%;
    background-color: #007bff8a;
    color: #fff;
    position: fixed;
    content: icon;
    top: 220px;
    right: 20px;
    line-height: 45px;
    text-align: center;
    cursor: pointer;
}

#rentmy-customer-login-page .rentmy-customer-login i {
    font-size: 12px;
}

#rentmy-customer-login-page .rentmy-customer-login i {
    color: #fff;
}

#rentmy-customer-login-page .rentmy-customer-login-modal.fade {
    opacity: 1 !important;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .modal-dialog {
    max-width: 600px;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .modal-body {
    padding: 0;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .login-register-content {
    width: 100%;
    box-shadow: none;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .card {
    border: none !important;
    width: 100%;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .nav.nav-pills,
#rentmy-customer-login-page .rentmy-customer-login-modal .nav.nav-tabs {
    margin-bottom: 0;
    background-color: #212529;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .login-register-content .nav-tabs .nav-item {
    width: 50%;
    margin-left: 0 !important;
    margin-right: 0 !important;
    border-bottom: none;
    background-color: #edf1f5;
    border: none !important;
    font-weight: 500 !important;
    color: #333;
    border-radius: 0;
    text-align: center;
    padding: 14px;
    font-size: 15px;
    text-decoration: none;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .nav-tabs .nav-item.show .nav-link,
#rentmy-customer-login-page .rentmy-customer-login-modal .nav-tabs .nav-link.active {
    background-color: #212529 !important;
    border: none !important;
    color: #fff !important;
    text-align: center;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .nav-link {
    padding: 1rem 1rem;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .tab-content {
    padding-bottom: 20px;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .nav-tabs {
    border-bottom: none;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .login-title {
    width: 100%;
    height: auto;
    text-align: center;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .login-title h1 {
    font-size: 20px;
    font-weight: 500;
    text-transform: uppercase;
    color: #222;
    letter-spacing: 1px;
    padding-top: 5px;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .userlogin-body {
    width: 100%;
    height: auto;
    padding: 20px 0 0;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .loginform a {
    font-size: 11px;
    color: #7f8c8d;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .loginform a:hover {
    text-decoration: underline;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .userlogin-body .input-field {
    width: 100%;
    height: auto;
    padding: 13px 15px 13px 20px;
    font-size: 15px;
    color: #7f8c8d;
    background: #edf1f5;
    border: 1px solid #edf1f5;
    outline: 0;
    border-radius: 3px;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .userlogin-body input:hover,
#rentmy-customer-login-page .rentmy-customer-login-modal .userlogin-body input:focus {
    box-shadow: none;
    border: 1px solid #ddd;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .login {
    width: 200px;
    height: auto;
    padding: 10px;
    text-align: center;
    font-size: 15px;
    background-color: #212529;
    color: #fff;
    border: 0px;
    transition: .3s ease-in-out;
    -webkit-transition: .3s ease-in-out;
    outline: 0;
    cursor: pointer;
    border-radius: 3px;
    font-weight: 400;
    text-transform: uppercase;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .login:focus,
#rentmy-customer-login-page .rentmy-customer-login-modal .login:hover {
    background-color: #e7e8e9;
    outline: 0;
    border: none;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .userlogin-body .custom-control label {
    color: #555 !important;
    font-size: 14px;
    font-weight: 400 !important;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .userlogin-body .custom-control a {
    color: #555 !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    text-decoration: underline;
    padding: 0;
    position: unset;
    letter-spacing: 0;
    display: inline-block !important;
    text-transform: unset;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .custom-checkbox .custom-control-input:checked~.custom-control-label::before {
    background-color: #212529;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .login-close {
    position: absolute;
    top: -16px;
    right: -10px;
    background-color: #fff;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50px;
    z-index: 9;
    cursor: pointer;
    font-size: 14px;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .storelogin-content {
    text-align: center;
    padding-top: 25px;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .onlinestore-create-account {
    width: 100%;
    float: left;
    padding: 20px 90px 5px;
    text-align: center;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .onlinestore-create-account a {
    cursor: pointer;
    text-decoration: underline !important;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .onlinestore-create-account a:hover {
    text-decoration: underline !important;
    color: #888;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .userlogin-box {
    padding: 0 90px;
    transition: all .3s;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .userlogin-body .input-field {
    width: 100%;
    height: auto;
    padding: 13px 15px 13px 20px;
    font-size: 15px;
    color: #7f8c8d;
    background: #edf1f5;
    border: 1px solid #edf1f5;
    outline: 0;
    border-radius: 3px;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .login {
    width: 130px;
    height: 46px;
    padding: 10px;
    text-align: center;
    font-size: 15px;
    background-color: #212529;
    color: #fff;
    border: 0px;
    transition: .3s ease-in-out;
    -webkit-transition: .3s ease-in-out;
    outline: 0;
    cursor: pointer;
    border-radius: 3px;
    font-weight: 400;
    text-transform: uppercase;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .loginform a {
    font-size: 14px;
    color: #7f8c8d;
    padding-top: 12px;
    text-decoration: none !important;
    cursor: pointer !important;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .loginform a:hover {
    text-decoration: unset !important;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .usersignup-box {
    padding: 0 20px !important;
    transition: all .3s;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .userlogin-body .form-control,
#rentmy-customer-login-page .rentmy-customer-login-modal .userlogin-body .input-field {
    width: 100%;
    height: 50px;
    padding: 13px 15px 13px 20px;
    font-size: 15px;
    color: #7f8c8d;
    background: #edf1f5;
    border: 1px solid #edf1f5;
    outline: 0;
    border-radius: 3px;
}

#rentmy-customer-login-page .rentmy-customer-login-modal .login-close {
    position: absolute;
    top: -16px;
    right: -10px;
    background-color: #fff;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50px;
    z-index: 9;
    cursor: pointer;
}


#rentmy-customer-login-page .rentmy-customer-login-element {
    opacity: 1 !important;
    display: block;
    max-width: 600px;
}

#rentmy-customer-login-page .rentmy-customer-login-element .modal-body {
    padding: 0;
}

#rentmy-customer-login-page .rentmy-customer-login-element .login-register-content {
    width: 100%;
    box-shadow: none;
}

#rentmy-customer-login-page .rentmy-customer-login-element .card {
    border: none !important;
    width: 100%;
}

#rentmy-customer-login-page .rentmy-customer-login-element nav-tabs {
    margin-bottom: 0;
    background-color: #212529;
    width: 100%;
}

#rentmy-customer-login-page .rentmy-customer-login-element .login-register-content .nav-tabs .nav-item {
    width: 50% !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
    border-bottom: none;
    background-color: #edf1f5;
    border: none !important;
    font-weight: 500 !important;
    color: #333;
    border-radius: 0;
    text-align: center;
    padding: 14px;
    font-size: 15px;
    text-decoration: none;
}

#rentmy-customer-login-page .rentmy-customer-login-element .nav-tabs .nav-item.show .nav-link,
#rentmy-customer-login-page .rentmy-customer-login-element .nav-tabs .nav-link.active {
    background-color: #212529 !important;
    border: none !important;
    color: #fff !important;
    text-align: center;
}

#rentmy-customer-login-page .rentmy-customer-login-element .nav-link {
    padding: 1rem 1rem;
}

#rentmy-customer-login-page .rentmy-customer-login-element .tab-content {
    padding-bottom: 20px;
}

#rentmy-customer-login-page .rentmy-customer-login-element .nav-tabs {
    border-bottom: none;
}

#rentmy-customer-login-page .rentmy-customer-login-element .login-title {
    width: 100%;
    height: auto;
    text-align: center;
}

#rentmy-customer-login-page .rentmy-customer-login-element .login-title h1 {
    font-size: 20px;
    font-weight: 500;
    text-transform: uppercase;
    color: #222;
    letter-spacing: 1px;
    padding-top: 5px;
}

#rentmy-customer-login-page .rentmy-customer-login-element .userlogin-body {
    width: 100%;
    height: auto;
    padding: 20px 0 0;
}

#rentmy-customer-login-page .rentmy-customer-login-element .loginform a {
    font-size: 11px;
    color: #7f8c8d;
}

#rentmy-customer-login-page .rentmy-customer-login-element .loginform a:hover {
    text-decoration: underline;
}

#rentmy-customer-login-page .rentmy-customer-login-element .userlogin-body .input-field {
    width: 100%;
    height: auto;
    padding: 13px 15px 13px 20px;
    font-size: 15px;
    color: #7f8c8d;
    background: #edf1f5;
    border: 1px solid #edf1f5;
    outline: 0;
    border-radius: 3px;
}

#rentmy-customer-login-page .rentmy-customer-login-element .userlogin-body input:hover,
#rentmy-customer-login-page .rentmy-customer-login-element .userlogin-body input:focus {
    box-shadow: none;
    border: 1px solid #ddd;
}

#rentmy-customer-login-page .rentmy-customer-login-element .login {
    width: 200px;
    height: auto;
    padding: 10px;
    text-align: center;
    font-size: 15px;
    background-color: #212529;
    color: #fff;
    border: 0px;
    transition: .3s ease-in-out;
    -webkit-transition: .3s ease-in-out;
    outline: 0;
    cursor: pointer;
    border-radius: 3px;
    font-weight: 400;
    text-transform: uppercase;
}

#rentmy-customer-login-page .rentmy-customer-login-element .login:focus,
#rentmy-customer-login-page .rentmy-customer-login-element .login:hover {
    background-color: #e7e8e9;
    outline: 0;
    border: none;
}

#rentmy-customer-login-page .rentmy-customer-login-element .userlogin-body .custom-control label {
    color: #555 !important;
    font-size: 14px;
    font-weight: 400 !important;
}

#rentmy-customer-login-page .rentmy-customer-login-element .userlogin-body .custom-control a {
    color: #555 !important;
    font-size: 14px !important;
    font-weight: 400 !important;
    text-decoration: underline;
    padding: 0;
    position: unset;
    letter-spacing: 0;
    display: inline-block !important;
    text-transform: unset;
}

#rentmy-customer-login-page .rentmy-customer-login-element .custom-checkbox .custom-control-input:checked~.custom-control-label::before {
    background-color: #212529;
}

#rentmy-customer-login-page .rentmy-customer-login-element .login-close {
    position: absolute;
    top: -16px;
    right: -10px;
    background-color: #fff;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50px;
    z-index: 9;
    cursor: pointer;
    font-size: 14px;
}

#rentmy-customer-login-page .rentmy-customer-login-element .storelogin-content {
    text-align: center;
    padding-top: 25px;
}

#rentmy-customer-login-page .rentmy-customer-login-element .onlinestore-create-account {
    width: 100%;
    float: left;
    padding: 20px 90px 5px;
    text-align: center;
}

#rentmy-customer-login-page .rentmy-customer-login-element .onlinestore-create-account a {
    cursor: pointer;
    text-decoration: underline !important;
}

#rentmy-customer-login-page .rentmy-customer-login-element .onlinestore-create-account a:hover {
    text-decoration: underline !important;
    color: #888;
}

#rentmy-customer-login-page .rentmy-customer-login-element .userlogin-box {
    padding: 0 90px;
    transition: all .3s;
}

#rentmy-customer-login-page .rentmy-customer-login-element .userlogin-body .input-field {
    width: 100%;
    height: auto;
    padding: 13px 15px 13px 20px;
    font-size: 15px;
    color: #7f8c8d;
    background: #edf1f5;
    border: 1px solid #edf1f5;
    outline: 0;
    border-radius: 3px;
}

#rentmy-customer-login-page .rentmy-customer-login-element .login {
    width: 130px;
    height: 46px;
    padding: 10px;
    text-align: center;
    font-size: 15px;
    background-color: #212529;
    color: #fff;
    border: 0px;
    transition: .3s ease-in-out;
    -webkit-transition: .3s ease-in-out;
    outline: 0;
    cursor: pointer;
    border-radius: 3px;
    font-weight: 400;
    text-transform: uppercase;
}

#rentmy-customer-login-page .rentmy-customer-login-element .loginform a {
    font-size: 14px;
    color: #7f8c8d;
    padding-top: 12px;
    text-decoration: none !important;
    cursor: pointer !important;
}

#rentmy-customer-login-page .rentmy-customer-login-element .loginform a:hover {
    text-decoration: unset !important;
}

#rentmy-customer-login-page .rentmy-customer-login-element .usersignup-box {
    padding: 0 20px !important;
    transition: all .3s;
}

#rentmy-customer-login-page .rentmy-customer-login-element .userlogin-body .form-control,
#rentmy-customer-login-page .rentmy-customer-login-element .userlogin-body .input-field {
    width: 100%;
    height: 50px;
    padding: 13px 15px 13px 20px;
    font-size: 15px;
    color: #7f8c8d;
    background: #edf1f5;
    border: 1px solid #edf1f5;
    outline: 0;
    border-radius: 3px;
}

#rentmy-customer-login-page .rentmy-customer-login-element .login-close {
    position: absolute;
    top: -16px;
    right: -10px;
    background-color: #fff;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    border-radius: 50px;
    z-index: 9;
    cursor: pointer;
}