<script setup>
import { inject, defineProps, defineComponent, } from "vue"; 
import { createComponentHTML } from "@utils/functions/withComponent"; 
let { domElement, http } = inject("utils");
let { wrapper } = defineProps(["wrapper"]);
import { Toaster } from "@/import-hub";  
let { BtnLoader } = inject('components');
let log = console.log
const site_specific = JSON.parse(localStorage.getItem('rentmy_contents'))?.site_specific
const isActiveTheNewsLetter = [true, undefined].includes(site_specific?.confg?.newsletter)

 

createComponentHTML(wrapper, [
    {
        selector: '[RentMyNewLetterForm]',
        attr: {
            '@submit.prevent': 'log'
        },
        child: [
            {
                selector: 'input[name="email"]',
                attr: {
                    'v-model': 'payload.email',
                    ':required': 'true',
                    ':disabled': 'isActiveTheNewsLetter === false',
                }
            },
            {
                selector: 'input[name="date"]',
                attr: {
                    'v-model': 'payload.options.value',
                    ':required': 'false',
                    ':disabled': 'isActiveTheNewsLetter === false',
                }
            },
            {
                selector: '[RentMyNewLetterSubmitBtn]',
                attr: {
                    '@click.prevent.stop': 'onSubmit',
                    ':disabled': '!allowToSubmit || !isActiveTheNewsLetter',
                },
                text: ` __EXISTING_HTML__ <btn-loader v-if="calling"></btn-loader> 
                `
            }
        ],
    },
    {
        selector: '[RentMyAlertMessage]',
        attr: {
            'v-if': 'message'
        },
        text: '{{ message }}'
    }
])


let template = wrapper.innerHTML
wrapper.innerHTML = '' 


let RentmyNewLetter = defineComponent({
    components: {
        BtnLoader
    },
    template, 
    data(){
        return {
            log,
            calling: false,
            isActiveTheNewsLetter,
            message: '',
            defautlDateValue: '',
            payload: {
       
                options: {
                    estimated_move_date: {
                        label: "Estimated move date",
                        // value: "10-22-2025" // dynamic
                        value: '',
                    }
                },
                email: "", // dynamic
                type: "newsletter"
             
            },
        }
    },
    computed: {
        allowToSubmit: function(){
            let email = this.payload.email
            let date = this.payload.options.value || '' 
            if(!email || !date) return false
            else return (email && date && (/^\d{2}-\d{2}-\d{4}$/.test(date)));
        }
    },
    methods:{
        clearPayload: function(){
            this.payload.email = ''
            this.payload.options.estimated_move_date.value = this.defautlDateValue
            // Store specific code (VGC)
            let first_name = wrapper.querySelector('[name="first_name"]')
            let last_name = wrapper.querySelector('[name="last_name"]')
            if(first_name) first_name.value = ''
            if(last_name) last_name.value = ''
        },
        onSubmit: function(event){
            this.calling = true
            let { payload } = this
            http.post('/contactus', payload).then(response => {
                // console.log(response.data);

                Toaster().success('Submitted Successfully')
                this.clearPayload()
                this.calling = false
            }).catch(err => {
                Toaster().success('Submitted Successfully')
                this.clearPayload()
                this.calling = false
            })
            
        },
    },
    mounted(){ 
        if(!isActiveTheNewsLetter){
            this.message = 'The newsletter is not active for this store'
        }
        domElement.setWraperIsReady(wrapper)

        /**
         * Some sotore can hide date, so getting a default 
         * This input must be a hidden element
         */
        let dateElement = wrapper.querySelector('[type="hidden"][name="date"]') 
        if(dateElement){ 
            let default_value = dateElement.getAttribute('datevalue')
            if(default_value) { 
                this.defautlDateValue = default_value
                this.payload.options.value = default_value 
            }
        }
    },

})

// wrapper.innerHTML = ''




</script>

<template>
    <Teleport :to="wrapper">
        <RentmyNewLetter></RentmyNewLetter>
    </Teleport>
</template>

