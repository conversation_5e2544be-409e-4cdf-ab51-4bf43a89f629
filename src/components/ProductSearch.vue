<script setup>
/**
 * TO call any where this, just call
 * <div rentmy-search-widget></div>
 */
import { ref, inject ,toRaw, defineProps } from 'vue';
import debounce from 'lodash/debounce'
import { useUserStore } from '../modules/user/controllers/user.controller';
import { createComponentHTML } from "@utils/functions/withComponent";
let { domElement, helper } = inject('utils');
import Loader from '@components/Loader.vue'
import { emitter } from '@/import-hub';
let isProductListPage = inject('isProductListPage')

const searchTerms = ref('')
const loader = ref(false)
const allowSearch = ref(false)

emitter.on('onchange__productlist__filter', ()=>{
  searchTerms.value = ''
  helper.withURL.deleteQuery("search")
})

const searchItems = ref([])
const clearSettimeout = ref(null);
const searchInputField = ref(null)
let enter_pressed = ref(false);

let props = defineProps({
  showBuyRent: {
    default: true,
    required: false,
    type: Boolean,
  },
  wrapper: {
    default: null,
    required: false,
  },
})

const emit = defineEmits(['getProductData', 'view-single-product']);
const debouncedFilter = debounce(getFilteredResults, 400);


async function getFilteredResults() {
  try {
    if(allowSearch.value && searchTerms.value && searchTerms.value?.length > 2) {
      loader.value = true;
      const userStore = useUserStore()
      let response = null;
      if(props.showBuyRent){
        response = await userStore.searchProduct(searchTerms.value) // For Auth User
      }else{
        response = await userStore.searchProduct2(searchTerms.value) // No Auth required
      }
      if(response?.status =='OK') {
        loader.value = false;
        searchItems.value = response.result.data
        if(enter_pressed.value){
          enter_pressed.value = false;
          searchItems.value = [];
        }
      }
    } else {
      searchItems.value = []
    }    
  } catch (err) {
    console.error(`${err}`);
  }
}



function clearSearch(event) {
  if(event) event.preventDefault();
  searchTerms.value = ''
  searchItems.value = [];
  emitter.emit('from-widget-search-product', '');
}

function searchDropdownActive() {
  if(searchItems.value?.length) return { active: true }
}

function formatVarientLabel(item) {
    if(item.rent || item.buy) {
      const arr = [];
      for (let i = 0; i < item.variant_chain_name?.length; i++) {
        arr.push(
          `${item.variant_set_name[item.variant_set_id[i]]}: ${
            item.variant_chain_name[i]
          }`
        );
      }
      return item?.variant_set_id?.includes(1) ? "" : arr.join(" -> ");
    }
    return '';
}
async function onClickRent(item) {
  emit('getProductData', {
    type: 'rent',
    data: toRaw(item)
  });
  clearSearch();
}

async function onClickBuy(item) {
  emit('getProductData', {
    type: 'buy',
    data: toRaw(item)
  });
  clearSearch();
}

function pressedEnter(event, {eventName=null}={}){
  event.preventDefault();
  if(eventName != 'input'){
    enter_pressed.value = true;
  }
  emit('getProductData', searchTerms.value);
  
  if(!isProductListPage && eventName != 'input'){
    helper.productSearchRedirect(searchTerms.value);
  } 

  clearTimeout(clearSettimeout.value);
  clearSettimeout.value = setTimeout(() => {
    emitter.emit('from-widget-search-product', searchTerms.value);
  }, 300);
  
  searchItems.value = [];
}

let focus_in_timeout = null;
function focusOut(){
  clearTimeout(focus_in_timeout);
  focus_in_timeout = setTimeout(() => {
    searchItems.value = [];
  }, 100)
}

function clear_focus_in_timeout (){
  clearTimeout(focus_in_timeout);
}

function createProductURL(product){  
  let { page } = window?.RENTMY_GLOBAL
  let url
  if(product?.type == 1){
      url = page.product_details
  } else {
      url = page.package_details
  } 
  return helper.withURL.generateURL(url, product);
}

let inputElement = props.wrapper.querySelector('input[type=text]') || props.wrapper.querySelector('input[type=search]');
let SearchButton = props.wrapper.querySelector('[SearchButton]');
let options = domElement.parseOptions(props.wrapper)

if(!inputElement){ 
  props.wrapper.innerHTML = `
    <div class="m-portlet__body form-panel p-0 pt-0 add-item-form-area-new">
      <div class="row m-0">
          <div class="form-group admin-cart orderitem-search col-md-12 p-0 mb-0">
            <input ref="SearchInput" type="text" class="form-control" placeholder="Search Item" autocomplete="off" autocapitalize="off" SearchInput >
            <div class="search-close-icon" SearchButton><i class="fa fa-close"></i></div>  
          </div>
      </div>
  </div>  
  `
}

if(options?.loader !== false){
  props.wrapper.insertAdjacentHTML("beforeend", `<span v-show="loader" ref="btnLoaderComp" class="m-loader" ></span>`);

}

props.wrapper.insertAdjacentHTML("beforeend", `
  <teleport to="body">
    <div ref="searchResultPopup" @click.stop="false" v-show="show && searchItems?.length" class="item-search-dropdown" :class="searchDropdownActive()" searchResultPopup>
      <ul class="dropdown">
        <li v-for="item in searchItems" :key="item">
          <template v-if="showBuyRent">
            <button v-if="item.buy || item.rent" type="button" class="dropdown-item">
                <div>{{item.name}}</div>
                <div class="colorPurpel">
                  <small style="font-style: italic;">
                    {{ formatVarientLabel(item) }}
                  </small>
                </div>
                <div class="mt-2">
                    <button v-if="item.buy" @click.stop="onClickBuy(item)" class="btn btn-sm btn-xsm btn-outline-dark" style="margin-right: 10px;">Buy</button>
                    <button v-if="item.rent" @click.stop="onClickRent(item)" class="btn btn-sm btn-xsm btn-outline-danger">Rent</button>
                </div>
            </button>
          </template>
          <template v-else>
            <template v-if="item.buy || item.rent">
              <template v-if="using_in_cli_project">
                <a type="button" role="option" class="dropdown-item cp" :href="createProductURL(item)" @click.stop="RentMyEvent.emit('cdn:navigate_product_details_page', item)">
                    <div>{{item.name}}</div>
                    <div class="colorPurpel">
                      <small style="font-style: italic;">
                        {{ formatVarientLabel(item) }}
                      </small>
                    </div>
                </a>
              </template>
              <template v-else>
                <a type="button" role="option" class="dropdown-item" :href="createProductURL(item)">
                    <div>{{item.name}}</div>
                    <div class="colorPurpel">
                      <small style="font-style: italic;">
                        {{ formatVarientLabel(item) }}
                      </small>
                    </div>
                </a>
              </template>
            </template>
          </template>
        </li>
      </ul>
    </div>  
  </teleport>
`);

createComponentHTML(props.wrapper, [
    {
        selector: "[SearchInput]",
        attr: { 
          'ref': 'SearchInput',
          'v-model': 'searchTerms',
          '@keyup.enter': 'pressedEnter',
          '@click': 'allowSearch = true;setPopupPosition();show=true',
          '@keydown': 'allowSearch = true;setPopupPosition();show=true',
          '@touchstart': 'allowSearch = true;setPopupPosition();show=true',
          '@focusin': 'clear_focus_in_timeout',
          '@input': `(event) => {
            pressedEnter_afterDelay(event)            
          }`,
          'autocapitalize': 'off',
          'autocorrect': 'off',
          'spellcheck': 'false',
        },
    },
    {
        selector: "[searchbutton]",
        attr: { 
          '@click.prevent': `(event)=>{
            searchTerms = '';
            helper.withURL.deleteQuery("search");
            pressedEnter(event);
          }`,
          ':class': `{ 
          'cp': true,
          'inside-contents-opacity-0': options.onLoaderHideBtnContens && loader
          }`
        },
    },
]);

let template = props.wrapper.innerHTML;
props.wrapper.innerHTML = '';


let searchComponent = {
  components: {
    Loader,
  },
  template,
  data() {
      return {  
        props,        
        searchTerms,        
        loader,     
        helper,   
        options,        
        allowSearch,        
        searchItems,        
        searchInputField,        
        enter_pressed,       
        emit,        
        debouncedFilter,        
        inputElement,        
        SearchButton,      
        timeout: null,  
        RentMyEvent: RentMyEvent,
        using_in_cli_project: RENTMY_GLOBAL?.using_in_cli_project,
        show: false,
        timeout2: null
      }
  },
  async mounted() {  
    searchTerms.value = helper?.withURL?.getQuery('search') || null;    
    document.addEventListener('scroll', (event) => {
      this.setPopupPosition();
    })  
    document.addEventListener('mousemove', (event) => {
      this.setPopupPosition();
    })  
    document.addEventListener('click', (event) => {
      this.show = false;
      this.setPopupPosition();
    })  
    props.wrapper.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
    })  
    
  },
  watch: {
    searchTerms(a, b) {
      this.setPopupPosition()
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        this.getFilteredResults();
      }, 400);
    }
  },
  methods: {
    log: console.log,
    pressedEnter_afterDelay(event){
      clearTimeout(this.timeout2)
      
      this.timeout2 = setTimeout(()=>{
        this.enter_pressed = false;
        if(!event.target.value){
          this.searchTerms = ''
          helper.withURL.deleteQuery("search")
        } 
        this.pressedEnter(event, {eventName: 'input'})
      }, 300)
    },
    setPopupPosition(){
      let inputRect = this.$refs.SearchInput.getBoundingClientRect();
      let searchResultPopup = this.$refs.searchResultPopup;
      let btnLoaderComp = this.$refs.btnLoaderComp;

      searchResultPopup.style.position = `fixed`;
      searchResultPopup.style.top = `${inputRect.bottom}px`;
      searchResultPopup.style.left = `calc(${inputRect.left}px + var(--rentmy-searboxresult-adjust-left, 0px))`;
      searchResultPopup.style.width = `calc(${inputRect.width}px + var(--rentmy-searboxresult-adjust-width, 0px))`;
      searchResultPopup.style.maxHeight = `300px`;
      searchResultPopup.style.overflowY = `auto`;
      
      searchResultPopup.style.zIndex = '999999100000';
  
      if(btnLoaderComp){
        btnLoaderComp.style.position = `fixed`;
        btnLoaderComp.style.top = `calc(${inputRect.top}px + var(--rentmy-searboxloader-adjust-top, 12px))`;
        btnLoaderComp.style.left = `calc(${inputRect.right}px + var(--rentmy-searboxloader-adjust-left, -30px))`;
        btnLoaderComp.style.zIndex = '9999991000000';
      }
    },
    getFilteredResults,
    clearSearch,
    searchDropdownActive,
    formatVarientLabel,
    onClickRent,
    onClickBuy,
    pressedEnter,
    focusOut,
    clear_focus_in_timeout,
    createProductURL,     
  },    
}

</script>

<template>
  <Teleport :to="wrapper">
    <searchComponent></searchComponent>
  </Teleport>
</template>

