<script setup>
import { ref, inject, watch, toRaw, defineProps, onMounted } from 'vue';
import debounce from 'lodash/debounce'
import { useUserStore } from '../modules/user/controllers/user.controller';
// import { createComponentHTML } from "@utils/functions/withComponent";
let { domElement, helper } = inject('utils');
import Loader from '@components/Loader.vue'
import { emitter } from '@/import-hub';
let isProductListPage = inject('isProductListPage')

const searchTerms = ref('');
const loader = ref(false);
const allowSearch = ref(false);
const searchItems = ref([]);
const SearchInput = ref(null); // ---- HTML element
const searchResultPopup = ref(null); // ---- HTML element
const btnLoaderComp = ref(null); // ---- HTML element
let enter_pressed = ref(false);



let props = defineProps({
  showBuyRent: {
    default: true,
    required: false,
    type: <PERSON>olean,
  },
  wrapper: {
    default: null,
    required: false,
  },
})


let timeout = ref(null);
let show = ref(false);

async function getFilteredResults() {
  try {
    if(allowSearch.value && searchTerms.value && searchTerms.value?.length > 2) {
      loader.value = true;
      const userStore = useUserStore()
      let response = null;
      if(props.showBuyRent){
        response = await userStore.searchProduct(searchTerms.value) // For Auth User
      }
      if(response?.status =='OK') {
        loader.value = false;
        searchItems.value = response.result.data
        if(enter_pressed.value){
          enter_pressed.value = false;
          searchItems.value = [];
        }
      }
    } else {
      searchItems.value = []
    }    
  } catch (err) {
    console.error(`${err}`);
  }
}


const emit = defineEmits(['getProductData', 'view-single-product']);
const debouncedFilter = debounce(getFilteredResults, 400);





function clearSearch(event) {
  if(event) event.preventDefault();
  searchTerms.value = ''
  searchItems.value = [];
  emitter.emit('from-widget-search-product', '');
}

function searchDropdownActive() {
  if(searchItems.value?.length) return { active: true }
}

function formatVarientLabel(item) {
    if(item.rent || item.buy) {
      const arr = [];
      for (let i = 0; i < item.variant_chain_name?.length; i++) {
        arr.push(
          `${item.variant_set_name[item.variant_set_id[i]]}: ${
            item.variant_chain_name[i]
          }`
        );
      }
      return item?.variant_set_id?.includes(1) ? "" : arr.join(" -> ");
    }
    return '';
}
async function onClickRent(item) {
  emit('getProductData', {
    type: 'rent',
    data: toRaw(item)
  });
  clearSearch();
}

async function onClickBuy(item) {
  emit('getProductData', {
    type: 'buy',
    data: toRaw(item)
  });
  clearSearch();
}

function pressedEnter(event){
  event.preventDefault();
  enter_pressed.value = true;
  emit('getProductData', searchTerms.value);
  
  if(!isProductListPage){
    helper.productSearchRedirect(searchTerms.value);
  }
  emitter.emit('from-widget-search-product', searchTerms.value);
  searchItems.value = [];
}

let focus_in_timeout = null;
function focusOut(){
  clearTimeout(focus_in_timeout);
  focus_in_timeout = setTimeout(() => {
    searchItems.value = [];
  }, 100)
}

function clear_focus_in_timeout (){
  clearTimeout(focus_in_timeout);
}

function createProductURL(product){  
  let { page } = window?.RENTMY_GLOBAL
  let url
  if(product?.type == 1){
      url = helper.withURL.setQuery({'uid': product?.uuid,}, page.product_details, true)
  } else {
      url = helper.withURL.setQuery({'uid': product?.uuid,}, page.package_details, true)
  } 
  return helper.withURL.generateURL(url, product);
}

let options = domElement.parseOptions(props.wrapper);

function setPopupPosition(){
  try {
    if(SearchInput.value){
      let inputRect = SearchInput.value.getBoundingClientRect(); 
  
      if(searchResultPopup.value){
        searchResultPopup.value.style.position = `fixed`;
        searchResultPopup.value.style.top = `${inputRect.bottom}px`;
        searchResultPopup.value.style.left = `calc(${inputRect.left}px + var(--rentmy-searboxresult-adjust-left, 0px))`;
        searchResultPopup.value.style.width = `calc(${inputRect.width}px + var(--rentmy-searboxresult-adjust-width, 0px))`;
        searchResultPopup.style.maxHeight = `300px`;
        searchResultPopup.style.overflowY = `auto`;
        searchResultPopup.value.style.zIndex = '9';
  
      }
  
      if(btnLoaderComp.value){
        btnLoaderComp.value.style.position = `fixed`;
        btnLoaderComp.value.style.top = `calc(${inputRect.top}px + var(--rentmy-searboxloader-adjust-top, 12px))`;
        btnLoaderComp.value.style.left = `calc(${inputRect.right}px + var(--rentmy-searboxloader-adjust-left, -30px))`;
        btnLoaderComp.value.style.zIndex = '9';
      }

    }
  } catch (error) {
    console.warn('setPopupPosition___error', error)
  }
}

watch(searchTerms, (a, b)=>{
  setPopupPosition()
  clearTimeout(timeout.value);
  timeout.value = setTimeout(() => {
    getFilteredResults();
  }, 400);
})


onMounted(()=>{
  try {
    searchTerms.value = helper?.withURL?.getQuery('search') || null;    
    document.addEventListener('scroll', (event) => {
      setPopupPosition();
    })  
    document.addEventListener('mousemove', (event) => {
      setPopupPosition();
    })  
    document.addEventListener('click', (event) => {
      show.value = false;
      setPopupPosition();
    })  
    props.wrapper.addEventListener('click', (event) => {
      event.preventDefault();
      event.stopPropagation();
    }) 
    
  } catch (error) {
    
  }
})


</script>


<template>

  <div class="m-portlet__body form-panel p-0 pt-0 add-item-form-area-new">
      <div class="row m-0">
          <div class="form-group admin-cart orderitem-search col-md-12 p-0 mb-0">
            <input 
            ref="SearchInput" 
            type="text" 
            class="form-control w-100" 
            placeholder="Search Item" 
            autocomplete="off" 
            autocapitalize="off" 
            v-model="searchTerms"
            @keyup.enter="pressedEnter"
            @keyup.click="allowSearch = true;setPopupPosition();show=true"
            @keydown="allowSearch = true;setPopupPosition();show=true"
            @touchstart="allowSearch = true;setPopupPosition();show=true"
            @focusin="clear_focus_in_timeout"
            @input="searchTerms ? false : helper.withURL.deleteQuery('search')"
            SearchInput >
            <div class="search-close-icon" SearchButton><i class="fa fa-close"></i></div>  
          </div>
      </div>
      <span v-show="loader" ref="btnLoaderComp" class="m-loader" ></span>
  </div>  

  
  
 
  <teleport to="body">
    <div ref="searchResultPopup" @click.stop="false" v-show="show && searchItems?.length" class="item-search-dropdown" :class="searchDropdownActive()" searchResultPopup>
      <ul class="dropdown">
        <li v-for="item in searchItems" :key="item">
          <template v-if="showBuyRent">
            <button v-if="item.buy || item.rent" type="button" class="dropdown-item">
                <div>{{item.name}}</div>
                <div class="colorPurpel">
                  <small style="font-style: italic;">
                    {{ formatVarientLabel(item) }}
                  </small>
                </div>
                <div class="mt-2">
                    <button v-if="item.buy" @click.stop="onClickBuy(item)" class="btn btn-sm btn-xsm btn-outline-dark" style="margin-right: 10px;">Buy</button>
                    <button v-if="item.rent" @click.stop="onClickRent(item)" class="btn btn-sm btn-xsm btn-outline-danger">Rent</button>
                </div>
            </button>
          </template>
        </li>
      </ul>
    </div>  
  </teleport>

  
</template>

