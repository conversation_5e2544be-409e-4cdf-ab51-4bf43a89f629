<script setup>
import { ref, onMounted, defineProps, inject, computed, defineComponent } from "vue";
import { createComponentHTML } from "@utils/functions/withComponent";
let { helper, currency, domElement } = inject("utils");
let { wrapper } = defineProps(["wrapper"]);
let emitter = inject("emitter");
let UseDefaultUI = wrapper.querySelector('[IconArea]') ? false : true;
const log = console.log


const site_specific = JSON.parse( localStorage.getItem("rentmy_contents") )?.site_specific; 
const is_active_wish_list = site_specific?.confg?.inventory?.wishlist?.active

import { useWishlistStore } from "@stores/wishlist";
const wishlistStore = useWishlistStore()
/**
 * Passing inner HTML of .RentMyWishlistCounter from /templates/wishlist.html
 * This feature, will be help, when client will be cart icon from WordPress Menu
 * From Wordpress menu, there is no way to add html
*/
if(!wrapper.querySelector('[IconArea]')) { 
  wrapper.innerHTML = `
    <div class="icon-area" IconArea> 
      <i class="fa fa-heart"></i>
      <div class="count"><span TotalQuantity>0</span></div>
    </div>
  `
}

createComponentHTML(wrapper, [
  {
    selector: "[IconArea]", 
    attr: { 
      'v-if': 'isMounted',
      '@click.stop': 'wishlistStore.goToWishListPage()', 
    },
    text: false,
  }, 
  {
    selector: "[TotalQuantity]", 
    text: `{{ totalQuantity }}`,
  },
]);

let componenHTML = wrapper.innerHTML;
wrapper.innerHTML = ''; 

let WishListCounterComponent = defineComponent({
  template: componenHTML,
  props: ['currency', 'helper'],
  setup(props) {
    let isMounted = ref(false); 
    let cartData = ref(null); 
    let totalQuantity = computed(()=> wishlistStore.listof_wishlist?.wish_list_items?.length || 0);  
 
    onMounted(()=>{
      isMounted.value = true
      domElement.setWraperIsReady(wrapper)
      
      if(!is_active_wish_list){
        wrapper.remove()
      } else {
        wrapper.classList.add('FeatureActivated')
      }
    })
    
    return {
      helper,
      isMounted,
      cartData, 
      wishlistStore,
      totalQuantity, 
    }
  }
})

</script>

<template>
  <template v-if="is_active_wish_list">
      <teleport :to="wrapper">
        <WishListCounterComponent></WishListCounterComponent> 
      </teleport>
  </template>
</template>


