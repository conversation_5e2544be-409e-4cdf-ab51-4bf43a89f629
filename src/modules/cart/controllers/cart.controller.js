import { defineStore } from 'pinia'
import { reactive, computed, ref, onMounted } from 'vue'
import cartService from '../services/cart.service'
import { useGlobalStore } from '@stores/global';
import { changeNullToEmpty } from '@utils/functions'
import cookie from '@utils/cookie';

export const useCartStore = defineStore('cart', () => {

  const state = reactive({
    
  })

  const globalStore = useGlobalStore();  

  let currencyConfig = computed(() => globalStore.currency_config );

  const getCart = async (token, params={}) => {
    if(!token) return;
    let response = await cartService.getCart(token, params);
    return response;
  };

  const updateCart = async (payload) => {
    let response = await cartService.updateCart(payload);
    return response;
  };

  const removeItem = async (payload) => {
    let response = await cartService.removeItem(payload);
    return response;
  };

  const applyCoupon = async (payload) => {
    let response = await cartService.applyCoupon(payload);
    return response;
  };

  const getRelatedProducts = async (cart_token) => {
    let response = await cartService.getRelatedProducts(cart_token);
    return response;
  };

  async function init() {
    // ---
  }


  return {
    currencyConfig,
    
    init,
    getCart,   
    updateCart,   
    removeItem,   
    applyCoupon,   
    getRelatedProducts,   
  }
})