import http from  '@utils/http';

export default {
  async getCart(token, params={}) {
    let response = await http.get(`/carts/${token}`, {params})    
    return response.data
  }, 
  async updateCart(payload) {
    let response = await http.post(`/carts/update`, payload)    
    return response.data
  }, 
  async removeItem(payload) {
    let response = await http.post(`/carts/cart-remove-item`, payload)    
    return response.data
  }, 
  async applyCoupon(payload) {
    let response = await http.post(`/carts/apply-coupon`, payload)    
    return response.data
  }, 
  async getRelatedProducts(cart_token) {
    let response = await http.get(`/products/${cart_token}/user/related-products?source=cart`)    
    return response.data
  }, 
}