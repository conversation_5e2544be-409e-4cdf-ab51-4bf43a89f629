<script setup>
import EmDateTimePicker from '@components/em-datetimepicker/EmDateTimePicker.vue';
import { ref, reactive, inject, defineProps, computed, defineComponent } from 'vue';
import { useCartStore } from '../controllers/cart.controller';
import { datePipe } from "@utils/functions";
import { createComponentHTML } from "@utils/functions/withComponent";
let { helper, http, currency } = inject('utils');
import { removeOnlineStoreCartDate } from "@utils/functions";
import { useGlobalStore } from "@stores/global"; 

let { wrapper, isInpageWidget } = defineProps([
    'wrapper', 
    'isInpageWidget',
]);
let emitter = inject('emitter');
let showLoginModal = inject('showLoginModal', false);
const after_login_action = inject('after_login_action', 'reload')
let globalStore = useGlobalStore();
let globalLoader = inject('globalLoader');
let allowDyanmicContent = inject('allowDyanmicContent');
let printSelector = inject('printSelector');
let setWraperIsReady = inject('setWraperIsReady');
import { Toaster } from '@/import-hub';
import { useProductListStore } from '../../product-list/controllers/productList.controller';
const log = console.log

import { useWishlistStore } from "@stores/wishlist";
const wishlistStore = useWishlistStore()

const productListStore = useProductListStore();

let cartStore = useCartStore();
let shoppingBagImage = RentMyEvent.apply_filters('Cart:ShoppingBagImage', RENTMY_GLOBAL?.images?.emptybag_image);
let shoppingPageURL = RentMyEvent.apply_filters('Cart:ContinueShoppingUrl', RENTMY_GLOBAL?.page?.products_list);
const site_specific = JSON.parse(localStorage.getItem('rentmy_contents'))?.site_specific;
const confg = JSON.parse(localStorage.getItem('rentmy_contents'))?.site_specific?.confg;
const is_active_wish_list = site_specific?.confg?.inventory?.wishlist?.active
let usingRange = computed(() => {
    return (confg.show_start_date && !confg.show_end_date) || (!confg.show_start_date && confg.show_end_date);
})

const DATE_FORMAT = isInpageWidget ? 'MM-DD-YY' : confg?.date_format || 'MM-DD-YYYY'

let showingStartTime = confg.show_start_time
let showingEndTime = confg.show_end_time
let showingTimePicker = (confg.show_start_time || confg.show_end_time)

const isMounted = ref(false);
const cartData = ref(null);
const taxesAndFees = ref(0);
const pickerModelValue = ref(null);
const dateTimePicker = ref(null);
const picker = reactive({
    cartData: false,
    showDatePicker: false,
    min_date: false,
    disable: false,
});

function onClickToggleDatePicker(){
    if(dateTimePicker.value){
        dateTimePicker.value.toggle();
    }
}




let noRentalDateSelecte_message = 'Rental date not selected';


createComponentHTML(wrapper, RentMyEvent.apply_filters('options:cart_page',
[
    {
        selector: "[InsideContainer]",
        template: true,
        attr: { 'v-if': 'cartItems?.length || isInpageWidget' },
        child: [
            /* -------------------------------------------------------------------------- */
            /*                              with date picker                              */
            /* -------------------------------------------------------------------------- */
            {
                selector: "[RenatalDateArea]",
                attr: { 'v-if': '(confg?.show_start_date || confg?.show_end_date) && (cartData?.rent_start || cartData?.rent_end || isInpageWidget)' },
                child: [
                    {
                        selector: "[DateRange]",
                        attr: { 'v-if': '!picker.showDatePicker' },
                        child: [
                            {
                                selector: "[DateText]",
                                attr: {
                                    '@click': 'log(withFormat(pickerModelValue || widgetDateText))'
                                },
                                text: `{{ withFormat(pickerModelValue || widgetDateText) || noRentalDateSelecte_message }}`
                            }, 
                            {
                                selector: "b",
                                text: `{{ allowDyanmicContent ? site_specific?.cart?.rent_date : '__EXISTING_HTML__' }}` 
                            }                           
                        ]
                    },
                    {
                        selector: "[EditDate]",
                        attr: { '@click.stop': 'picker.showDatePicker = true' },
                    },
                    
                    {
                        selector: "[DatePicker]",
                        attr: { 'v-show': 'picker.showDatePicker' },
                    },
                    {
                        selector: "[BtnCancel]",
                        attr: { 
                            'v-if': 'picker.showDatePicker',
                            '@click': 'picker.showDatePicker = false',
                        },
                    },
                ]
            },

            
            /* -------------------------------------------------------------------------- */
            /*                             With inpage widget                             */
            /* -------------------------------------------------------------------------- */
            {
                selector: '[Sidebar]',
                attr: { 
                    'v-show': 'showSideBar',
                    ':class': `{fadeUp: showSideBar}`,
                }
            },
            {
                selector: '[ToggleDatePicker]',
                attr: {
                    '@click.stop': 'onClickToggleDatePicker',
                },
            },           
            {
                selector: '[SidebarCloseIcon]',
                attr: {
                    '@click.stop': 'showSideBar = false',
                },
            },
            {
                selector: '[CartPageUrl]',
                attr: {
                    ':href': 'RENTMY_GLOBAL?.page?.cart',
                    '@click.stop': 'goTo("cart")', 
                },
            },
            {
                selector: '[backButton]',
                attr: { 
                    '@click.stop': 'helper.backPreviousPage()', 
                }, 
            },
            

            /* -------------------------------------------------------------------------- */
            /*                        With in page widget, launcher                       */
            /* -------------------------------------------------------------------------- */
            {
                selector: '[InpageCartWidgetLauncher]',
                attr: {
                    'v-if': '!showSideBar',
                    '@click.stop': 'showSideBar = true;asMounted()'
                },
                child: [                    
                    {
                        selector: '[DateText]',
                        text: `{{ withFormat(pickerModelValue || widgetDateText) || noRentalDateSelecte_message }}`
                    },
                    {
                        selector: '[TotalAmount]',
                        attr: {
                            'v-if': 'totalQuantity'
                        },
                        text: `{{ TotalAmount }}`,
                    },
                    {
                        selector: '[TotalQuantity]',
                        attr: {
                            'v-if': 'totalQuantity'
                        },
                        text: `{{ totalQuantity }} {{ totalQuantity > 1 ? 'Items' : 'Item' }}`,
                    },
                    {
                        selector: '[TotalQuantity]',
                        attr: {
                            'v-else': ''
                        },
                        text: `Your shopping cart is empty`,
                    },
                ],
            }
        ],
    },
    {
        selector: "[InsideContainer]",
        template: true,
        attr: { 'v-else': '' },
        skipIcon: true,
        text: `          
            <div class="text-center fadeIn">
                <img src="${shoppingBagImage}" class="RentMyEmptyBagImage">
                <h4 class="my-4"> Your shopping cart is Empty </h4>
                <a class="RentMyBtn ContinueShoppingOnEmptyCart" href="${shoppingPageURL}">Continue Shopping</a>
            </div>
        `,
    },
    
    /* ------------------------------------------ */
    /*         Start With RentMyCartTabl          */
    /* ------------------------------------------ */    
    {
        selector: "[CartItem]",
        template: true,
        attr: { 'v-for': '(item, index) in cartItems', ':key': 'index' },
    },
    {
        selector: "[CartItem] [DeleteIconArea]",
        attr: { '@click': 'removeItem(item)' },
    },
    {
        selector: "[CartItem] [ImageArea] img",
        attr: { ':src': `helper.getProductImage(item)`, '@error': 'setDefaultImage' },
    },
    {
        selector: "[CartItem] [ItemNameArea]",
        text: `
            <strong class="CartItemTitle">{{ item?.product?.name }}</strong>

            <!--Variant Chain-->
            <template v-if="item.product.variant_chain && item.product.variant_chain.indexOf('Unassigned') < 0">
                <div class="CartItemVariantName"><small>{{ item.product.variant_chain }}</small></div>
            </template>

            <!--custom fields-->
            <template v-if="item?.cart_product_options?.length">
                <ul class="ProductCustomFieldsInCart">
                    <template v-for="(fields, index) in item?.cart_product_options" :key="index">
                        <template v-if="fields.options && fields.options.length > 0">
                            <template v-for="(option, key) in fields?.options" :key="key">
                                <template v-if="option.label && option.value">
                                    <li v-if="['rich', 'Delivery Only'].includes(option.label) == false">
                                       
                                        {{ option.label }}:                                     
                                        <div v-html="option.value + (key === fields.options.length - 1 ? '' : ', ')" ></div>
                                        
                                    </li>
                                </template>
                            </template>
                        </template>
                    </template>
                </ul>
            </template>
        `,
    },
    {
        selector: "[CartItem] [ItemPrice]",
        text: `{{  currency.format(item.price) }}`,
    },
    {
        selector: "[CartItem] [IncrDecrArea] [DecrementBtn]",
        attr: { '@click': '(!item?.isDisabled && item?.quantity > 1) ? updateQuantity(item, 0) : false'},
        text: ``,
    },
    {
        selector: "[CartItem] [IncrDecrArea] [IncrementBtn]",
        attr: { '@click': '!item?.isDisabled ? updateQuantity(item, 1) : false' },
        text: ``,
    },
    {
        selector: "[CartItem] [IncrDecrArea] [QuantityInput]",
        attr: { ':disabled': 'true', ':value': 'item?.quantity' },
        text: 'false',
    },
    {
        selector: "[CartItem] [IncrDecrArea] [QuantityText]",
        attr: {},
        text: '{{ item?.quantity }}',
    },
    {
        selector: "[CartItem] [ItemPriceArea]",
        text: `{{ currency.format(item?.sub_total) }} <span v-if="getCouponDiscountText(item)">({{ getCouponDiscountText(item) }} Coupon applied)</span>`,
    },
    /* -------- End With RentMyCartTable -------- */

    /* -------- Start Summery -------- */
    {
        selector: "[RentMyCartTable]", 
        child: [
            {
                selector: "thead th:nth-child(3)",
                text: `{{ allowDyanmicContent ? site_specific?.cart?.th_product : '__EXISTING_HTML__' }}`
            },
            {
                selector: "thead th:nth-child(4)",
                text: `{{ allowDyanmicContent ? site_specific?.cart?.th_unit_price : '__EXISTING_HTML__' }}`
            },
            {
                selector: "thead th:nth-child(5)",
                text: `{{ allowDyanmicContent ? site_specific?.cart?.th_quantity : '__EXISTING_HTML__' }}`
            },
            {
                selector: "thead th:nth-child(6)",
                text: `{{ allowDyanmicContent ? site_specific?.cart?.th_subtotal : '__EXISTING_HTML__' }}`
            },
        ]
    },
    {
        selector: ".RentMyCartTotal",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_cart_total : '__EXISTING_HTML__' }}`
    },
    {
        selector: "[RentMySummeryTable] [SubtotalAmount]",
        text: '{{ subtotalAmount() }}',
    },
    {
        selector: "[RentMySummeryTable] [SubtotalLabel]",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.th_subtotal : '__EXISTING_HTML__' }}`
    },
    {
        selector: "[RentMySummeryTable] [TaxesFeesLabel] td:nth-child(1)",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_taxes_and_fees : '__EXISTING_HTML__' }}`
    },
    {
        selector: "[RentMySummeryTable] [DepositeAmountRow]",
        attr: { 
                'v-if': 'site_specific?.cart?.lbl_total_deposite || cartData?.deposit_amount'
             }
    },
    {
        selector: "[RentMySummeryTable] [DepositeAmountRow] td:nth-child(1)",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_total_deposite : '__EXISTING_HTML__' }}`
    },
    {
        selector: "[RentMySummeryTable] [DepositeAmount]",
        text: '{{ currency.format(cartData?.deposit_amount || 0) }}',
    },
    {
        selector: "[RentMySummeryTable] [TaxesFeesLabel]",
        attr: { 
                'v-if': 'confg?.checkout?.combine_additional_charges_with_taxes'
              }
    },
    {
        selector: "[RentMySummeryTable] [TaxesFees]",
        text: '{{ currency.format(taxesAndFees) }}',
    },
    {
        selector: "[RentMySummeryTable] [AdditionalChargeAmountRow]",
        attr: { 
                'v-if': 'cartData?.additional_charge && !confg?.checkout?.combine_additional_charges_with_taxes'
              }
    },
    {
        selector: "[RentMySummeryTable] [AdditionalChargeAmount]",
        text: '{{ currency.format(cartData?.additional_charge || 0) }}',
    },
    {
        template: true,
        selector: "[RentMySummeryTable] tr:has([lbl_shipping])",
        // attr: { 'v-if': 'isActiveShipping()' }, 
        attr: { 'v-if': 'site_specific?.cart?.lbl_shipping && site_specific?.cart?.lbl_next_step' }, 
        child: [
            {
                selector: "[lbl_shipping]",
                attr: { '@click': 'log(confg)' },
                text: `{{ site_specific?.cart?.lbl_shipping || 'Shipping Cost' }}`,
            },
            {
                selector: "[shipping_value] small",
                text: `{{ site_specific?.cart?.lbl_next_step || 'Calculated in the next step' }}`,
            },
        ]
    },
    {
        template: true,
        selector: "[RentMySummeryTable] tr:has([LblDeliveryTax])",
        attr: { 'v-if': 'isActiveDelivery() && site_specific?.cart?.lbl_delivery_tax' }, 
        child: [
            {
                selector: "[LblDeliveryTax]",       
                text: `{{ site_specific?.cart?.lbl_delivery_tax || 'Delivery Tax' }}`,
            },
        ]
    }, 
    {
        selector: "[RentMySummeryTable] [TaxAmountLabel]",
        attr: { 
                'v-if': '!confg?.checkout?.combine_additional_charges_with_taxes && site_specific?.cart?.lbl_tax'
              }
    },
    {
        selector: "[RentMySummeryTable] [TaxAmountLabel] td:nth-child(1)",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_tax : '__EXISTING_HTML__' }}`
    },
    {
        selector: "[RentMySummeryTable] [AdditionalChargeAmountRow] td:nth-child(1)",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_additional_charge : '__EXISTING_HTML__' }}`
    },
    {
        selector: "[RentMySummeryTable] [TaxAmount]",
        text: '{{ currency.format(cartData?.tax?.total || 0) }}',
    },
    {
        selector: "[RentMySummeryTable] [TotalAmount]",
        text: '{{ TotalAmount }}',
    },
    {
        selector: "[RentMySummeryTable] [LblTotal]",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_total : '__EXISTING_HTML__' }}`
    },
    /* -------- End Summery -------- */

    /* -------- Start Coupon -------- */
    {
        selector: "input[CouponInput]",
        attr: { 'v-model': 'coupon', '@keyup.enter.stop': 'applyCoupon' },
    },
    ...printSelector({
        selector: "input[CouponInput]",
        attr: { ':placeholder': `site_specific?.cart?.txt_coupon` },
    }),
    {
        selector: "[ApplyCouponBtn]",
        attr: { '@click.stop': 'applyCoupon', ':disabled': '!coupon' },
    },
    {
        selector: "[ApplyCouponBtn]",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.btn_coupon : '__EXISTING_HTML__' }}`
    },
    /* -------- End Coupon -------- */

    /* -------- Others -------- */
    {
        selector: "[ContinueShoppingBtn]",
        attr: { '@click.stop': 'goTo("shopping")' },
    },
    {
        selector: "[ContinueShoppingBtn]",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.btn_continue : '__EXISTING_HTML__' }}`
    },
    {
        selector: "[ProceedCheckoutBtn]",
        attr: { '@click.stop': 'goTo("checkout")' },
    },
    {
        selector: "[ProceedCheckoutBtn]",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.btn_checkout : '__EXISTING_HTML__' }}`
    },
    {
        selector: "[MakeQuotoBtn]",
        attr: { 
            'v-if': 'site_specific?.confg?.checkout?.quote_order',
            '@click.stop': 'makeQuote()',
        },
        text: `{{ allowDyanmicContent ? site_specific?.cart?.btn_checkout_quote : '__EXISTING_HTML__' }}`
    },
    /* -------- End Others -------- */

    /* -------- Start Addon/Related Products -------- */
    {
        selector: "[RentMyRelatedProducts]",
        attr: { 'v-if': 'relatedProducts?.length' },
    },
    {
        selector: "[RentMyRelatedProducts] [RentMyProductItem]",
        template: true,
        attr: { 'v-for': '(product, index) in relatedProducts', ':key': 'index' },
        child: [
            {
                selector: "a[RentMyProductImageUrl]",
                attr: { ':href': 'getProductDetailsURL(product)' },
            },
            {
                selector: "img[RentMyProductImage]",
                attr: { ':src': 'helper.getProductImage(product)', '@contextmenu': 'log(product)' },
            },
            {
                selector: "[RentMyProductName]",
                attr: { ':src': 'helper.getProductImage(cartData, product)' },
                text: `<a :href="getProductDetailsURL(product)">{{ product?.name }}</a>`,
            },
            {
                selector: "[RentMyProductPrice]",
                attr: {
                    ':class': `is_active_wish_list ? 'mb-0' : ''`
                },
                text: `{{ getProductPrice(product) }}`,
            },
            
            {
                selector: '[DetailsPageUrl]',
                attr: {
                    '@click.stop.prevent': `gotToDetailsPage(product)`,
                }
            },
            {
                selector: "[WishListBtnArea]",
                attr: { 'v-if': 'is_active_wish_list' },
                child: [
                    {
                        selector: '[RentMyAddToWishListBtn]',
                        attr: {
                            '@click.stop': 'addToWishList(product)'
                        }
                    },
                ]
            },
            {
                selector: "[ProductButtons]",
                attr: { 'v-if': '!is_active_wish_list' },
                child: [
                    {
                        selector: "[RentMyViewDetailsBtn]",
                        attr: { ':href': 'getProductDetailsURL(product)' },
                    },
                    {
                        selector: "[RentMyAddToCartBtn]",
                        attr: { 
                            'v-if': 'hasBuyPrice(product)', 
                            '@click.stop': 'createCart(product)' 
                        },
                    },
                ]
            },
        ]
    },
    /* -------- End Addon/Related Products -------- */

]));
let template = wrapper.innerHTML
wrapper.innerHTML = '';

let cartPageComponent = defineComponent({
    template,
    data() {
        return {            
            site_specific,
            confg,
            isInpageWidget,
            dateTimePicker,
            pickerModelValue,
            usingRange,
            picker,
            isMounted,
            helper,
            currency,
            cartData,
            cartItems: [],
            token: null,
            coupon: '',
            relatedProducts: null,
            RentMyEvent: RentMyEvent,
            showSideBar: false,
            RENTMY_GLOBAL,
            noRentalDateSelecte_message,
            allowDyanmicContent,
            wishlistStore,
            is_active_wish_list,
        }
    },
    async mounted() { 

        let startDate = sessionStorage.getItem('online_inagecart_widget_startDate');
        let endDate = sessionStorage.getItem('online_inagecart_widget_endDate');

        setTimeout(() => {
            if(dateTimePicker.value && startDate && endDate){
                dateTimePicker.value.setDate(startDate, endDate);
                dateTimePicker.value.setTime(startDate, endDate);
            }
        }, 500);

        emitter.on('open_inCartWidget', async (bool) => {
            if(!afterAddtoCart_open_widget) return;
            globalLoader.show();
            this.showSideBar = true; 
            if(afterAddtoCart_open_widget_datePicker && dateTimePicker.value){
                dateTimePicker.value.show();
            }
            await this.asMounted();
            globalLoader.hide();
        });
        
        emitter.on('change_date', this.whenChageRentalDate);
        RentMyEvent.on('cart_page:change_date', this.whenChageRentalDate);
        await this.asMounted();
        
        this.isMounted = true;  

        setTimeout(() => {
            setWraperIsReady(wrapper);   
            globalLoader.hide();
            RentMyEvent.emit('cdn:cart_page:mounted', this.cartData || {});            
        }, isInpageWidget ? 2500 : 0);      
    },
    computed: {        
        TotalAmount: function(){
            return currency.format(this.cartData?.total || 0);
        },
        totalQuantity: function(){
            if(!this.cartItems?.length) return 0; 
            return this.cartItems?.reduce((a, b) => (a + b?.quantity), 0);
        },
        widgetDateText: function(){
            let startDate = sessionStorage.getItem('online_inagecart_widget_startDate');
            let endDate = sessionStorage.getItem('online_inagecart_widget_endDate');
            if(startDate && endDate){
                return `${startDate} - ${endDate}`;
            } else {
                return '';
            }
        },
        allowToGoCheckout: function(){
            let isAutheticated =  Boolean(RENTMY_GLOBAL?.rentmy_customer_info);
            const guest_checkout = [true, undefined].includes(site_specific?.confg?.customer?.customer_guest_checkout)
            if(!guest_checkout && !isAutheticated){
                return false
            } else {
                return true
            }
        },
    },
    methods: {
        log,
        datePipe,
        onClickToggleDatePicker,
        asMounted: async function(){
            try {
                globalStore.getDeliverySettings();
                let data = localStorage.getItem("user_cart");
                if (data && data != "undefined") {
                    let _data = JSON.parse(data);
                    this.token = helper.withURL.getQuery('token') || _data?.token;
                    this.cartData = _data;
                    this.taxesAndFees = (this.cartData?.additional_charge || 0) + (Number(this.cartData?.tax?.total) || 0);
                    this.cartItems = _data?.cart_items;  
                    
                    if(this.cartData?.rent_start && this.cartData?.rent_end){
                        dateTimePicker.value.setDateTime(this.cartData?.rent_start, this.cartData?.rent_end)
                        setTimeout(() => {
                            dateTimePicker.value.triggerChange()
                        }, 100);

                    }

                    if(_data.coupon_id){
                        this.setCouponPlaceHolder();
                    }
                }
            } catch (error) {
                
            }
            await cartStore.init();
            await this.getCart(localStorage.getItem('token'));
            this.getRelatedProducts();
        },
        isExluded(type) {
            let arr = this.cartData?.excluded_fulfilment_type || [];
            return arr.includes(type);
        }, 
        isActiveShipping() {
            return globalStore.deliverySettings?.shipping && !this.isExluded('shipping');  
        },
        isActiveDelivery: function(){ 
            return globalStore.deliverySettings?.delivery && !this.isExluded('delivery');  
        }, 
        
        whenChageRentalDate: async function(data){
            try {  
                let startDate = (data?.startDate || '');
                let endDate = (data?.endDate || '');
                
                let startDate_time = startDate + (data?.startTime ? (' ' + data?.startTime) : data?.startTime);
                let endDate_time = endDate + (data?.endTime ? (' ' + data?.endTime) : data?.endTime);
                
                let token = localStorage.getItem('token');

                emitter.emit('changed:reantalDate:from:inPageCartWidget', data);

                sessionStorage.setItem('online_inagecart_widget_startDate', startDate_time);
                sessionStorage.setItem('online_inagecart_widget_endDate', endDate_time);

                if(!token){
                    return;
                }

                this.picker.showDatePicker = false;
                const payload = {
                    token,
                    type: "cart",
                    start_date: startDate_time || (startDate || ''),
                    end_date: endDate_time || (endDate || ''),
                    source: "online",
                    fullfilment_option: sessionStorage.getItem('online_fullfilment_option'),
                }
                globalLoader.show();
                await http.post('/products/availability', payload)
                await this.getCart();
                globalLoader.hide();
                RentMyEvent.emit('cdn:cart_page:changed_rental_date_range', this.cartData);
          
            } catch (error) {
                console.warn('Cart.vue2', error)
            }
        },
        getRelatedProducts: async function(){
            if(!this.cartItems?.length) return;
            let Config = JSON.parse(localStorage.getItem('rentmy_contents') || '{}');
            let show_related_product_as_addon = Config?.site_specific?.confg?.show_related_product_as_addon;
            if(!show_related_product_as_addon){
                let el = wrapper.querySelector('[RentMyRelatedProducts]');
                if(el) el.remove();
                return;
            } else {
                cartStore.getRelatedProducts(this.token).then(response => {
                    if(response.status == 'OK'){
                        this.relatedProducts = response.result.data;              
                    }
                })
                .catch(error => {
                    console.warn('getRelatedProducts__error::', error);
                });
            }

        },
        getCart: async function(){
            try {
                let response = await cartStore.getCart(localStorage.getItem('token'));
                if(response.status == 'OK'){
                    let data = response.result.data;
                    localStorage.setItem('user_cart', JSON.stringify(data));
                    // this.cartData = data; // this line showing error, show taken plan B
                    Object.keys(this.cartData)?.forEach(key => {
                        this.cartData[key] = data?.[key];
                    })
                    this.cartItems = data?.cart_items;

                    // dateTimePicker
                    setTimeout(() => {
                        let rent_start = this.cartData?.rent_start;
                        let rent_end = this.cartData?.rent_end;
                        let min_rent_start = this.cartData?.min_rent_start;
                        if(rent_start && rent_end && dateTimePicker.value){  
                            dateTimePicker.value.updateOptions({minDate: min_rent_start});
                            setTimeout(() => {
                                dateTimePicker.value.setDateTime(rent_start, rent_end);
                            }, 100);
                        }
                    }, 1000);  

                    if(data.coupon_id){
                        this.setCouponPlaceHolder();
                    } 
                } else{
                    if(response.result.message){
                        if(!isInpageWidget){
                            Toaster().error(response.result.message);
                        }
                        this.cartData = null;
                        this.cartItems = null;
                        localStorage.removeItem('user_cart');
                        removeOnlineStoreCartDate()
                    }
                }
            } catch (error) {
                
            }
        },
        removeItem: function(item){
            let payload = {
                token: this.cartData.token,
                cart_item_id: item.id,
                product_id: item.product.id,
            }
            globalLoader.show();
            cartStore.removeItem(payload).then(response => {
                globalLoader.hide();
                if(response.status == 'OK'){
                    this.cartData = response.result.data;
                    this.cartItems = response.result.data?.cart_items;
                    localStorage.setItem('user_cart', JSON.stringify(response.result.data));
                    if(this.cartItems.length == 0){
                        removeOnlineStoreCartDate('minimum'); 
                        windowLocation().reload();
                    }
                } else{
                    if(response.result.message){
                        Toaster().error(response.result.message);
                    }
                }
            })
        },
        updateQuantity: function(item, num=1){
            let payload = {
                token: this.cartData?.token,
                id: item?.id,
                increment: num,
                price: item?.price,
                sales_tax: item?.sales_tax,
            }
            if(item?.cart_product_options?.length){
                payload.option_id = item?.cart_product_options[0]?.id;
            }
            item.isDisabled = true;
            globalLoader.show();
            cartStore.updateCart(payload).then(response => {
                item.isDisabled = false;
                globalLoader.hide();
                if(response.status == 'OK'){
                    if(response.result.data){
                        this.cartData = response.result.data;
                        this.cartItems = response.result.data?.cart_items;
                        if(this.cartData){
                            localStorage.setItem('user_cart', JSON.stringify(response.result.data));
                        }
                    } else {
                        if(response.result?.error){
                            Toaster().error('Selected quantity is not available')
                        }
                    }
                } else{
                    if(response.result.message){
                        Toaster().error(response.result.message);
                    }
                }
            })

        },
        setDefaultImage: function(event){
            event.target.src = RENTMY_GLOBAL?.images?.default_product_image;
        },
        subtotalAmount: function(format=true){
            return currency.format(this.cartData?.sub_total || 0);        
        },
        setCouponPlaceHolder: function(){
            let el = wrapper.querySelector('input[CouponInput]');
            if(el) el.placeholder = 'Coupon already applied';       
        },
        applyCoupon: function(){
            let payload = {
                token: this.token,
                coupon: this.coupon,
            };
            globalLoader.show();
            cartStore.applyCoupon(payload).then(response => {
                globalLoader.hide();
                if(response.status == 'OK'){
                    Toaster().success('Coupon Applied')
                    let _data = response.result.data
                    this.cartData = _data;
                    this.cartItems = _data?.cart_items;
                    localStorage.setItem('user_cart', JSON.stringify(_data));   
                    if(_data.coupon_id){
                        this.coupon = '';
                        this.setCouponPlaceHolder();
                    }                 
                } else{
                    if(response.result.message || response.result.error){
                        Toaster().error(response.result.message || response.result.error);
                    }
                }
            })     
        },
        getCouponDiscountText: function(item){
             if(item.discount?.coupon_amount){
                return currency.format(item.discount?.coupon_amount);
             }else{
                return null;
             }
        },
        goTo: function(name){
            if(RENTMY_GLOBAL?.using_in_cli_project){
                if(name == 'shopping'){
                    RentMyEvent.emit('cdn:goto:product_list', null);
                } 
                else if(name == 'checkout'){  
                    RentMyEvent.emit('cdn:goto:checkout_page', null);
                }
                else if(name == 'cart'){  
                    RentMyEvent.emit('cdn:goto:cart_page', null); // comming from inpage cart widget
                }
            } else {
                if(name == 'shopping'){
                    window.open(RENTMY_GLOBAL.page.products_list, '_self');
                } 
                else if(name == 'checkout'){ 
                    // window.open(RENTMY_GLOBAL.page.checkout, '_self');
                    if(this.allowToGoCheckout){
                        window.open(RENTMY_GLOBAL.page.checkout, '_self');
                    } else {
                        showLoginModal.value = true 
                        after_login_action.value = 'goto_checkout_page' 
                    }  
                }
                else if(name == 'cart'){  
                    console.log('// comming from inpage cart widget');
                    window.open(RENTMY_GLOBAL.page.cart, '_self');
                }
            }
        },
        makeQuote: function(){   
            let cehckoutPage = helper.withURL.setQuery({quote: 'true'}, RENTMY_GLOBAL.page.checkout, true);
            window.open(cehckoutPage, '_self');            
        },
        getProductDetailsURL: function(product){   
            let detailsPageUrl = product?.type == 2 ? window.RENTMY_GLOBAL.page?.package_details : window.RENTMY_GLOBAL.page?.product_details;
            return helper.withURL.generateURL(detailsPageUrl, product)
        },
        getProductPrice: function(product){   
            let { currencyConfig } = cartStore;
            let priceLabel = currency.formatListPrice(product.prices?.[0], currencyConfig);
            return priceLabel?.price || 0;
        },
        hasBuyPrice: function(product){   
            let { prices } = product;
            let _prices = helper.formatBuyRent(prices)
            let isBuyType = false
            if (_prices && Object.keys(_prices).length > 0) {
                if(_prices?.buy?.type) isBuyType = true;
            }   
            return !!isBuyType;
        },
        createCart: function(product, {fromWishListBtnArea=false}={}){        
            globalLoader.show()
   
            productListStore.createCart(product).then(response => {
                globalLoader.hide()
                this.disabled = true;
                if (response.status == 'OK') {
                    if(response.result.error){
                        Toaster().error(response.result.error);
                        return;
                    }
                    Toaster().success('Item added to cart')
                    localStorage.setItem('user_cart', JSON.stringify(response.result.data));
                } else {
                    if(response.result.error){
                        Toaster().error(response.result.error);
                    }
                }
            })
        },
        withFormat: function(dateText){  

            // if(!isInpageWidget) return dateText;
            if(!dateText) return ''; 

            let [ startDate, endDate ] = dateText.split(' - ');
            if(!endDate) endDate = startDate;
            {
                let parts = startDate.split(' ');
                let date = parts[0];
                let time = showingStartTime ? parts.slice(1).join(' ') : '';
                startDate = `${helper.formatDate(date, DATE_FORMAT)}` + (time ? ` ${time}` : '');
                startDate = startDate.replace(/\s{2,}/g, ' ');
            }
            {
                let parts = endDate.split(' ');
                let date = parts[0];
                let time = showingEndTime ? parts.slice(1).join(' ') : '';
                endDate = `${helper.formatDate(date, DATE_FORMAT)}` + (time ? ` ${time}` : '');
                endDate = endDate.replace(/\s{2,}/g, ' ');
            }
            if(this.isAlwaysHideEndDate()) {
              return `${startDate}`;
            }
            return `${startDate} - ${endDate}`;
        },
        isAlwaysHideEndDate: function() {
            let status = false;
            if(site_specific?.confg && site_specific?.confg?.hasOwnProperty("checkout")) {
              status = site_specific?.confg?.checkout?.hide_enddate_time || false
            }
            return status; 
        },
        addToWishList: async function(product) {
            globalLoader.show()
            await wishlistStore.addToList({product})
            globalLoader.hide()
        },
        gotToDetailsPage: async function(product) {
            let fullUrl = this.getProductDetailsURL(product)
            if(this.RENTMY_GLOBAL.using_in_cli_project){
                
            } else {
                window.open(fullUrl, '_self')
            }
        },
    },    
})
let pickerTheme = RENTMY_GLOBAL?.emDateTimePicker?.theme || 'light';
let pickerColors = RENTMY_GLOBAL?.emDateTimePicker?.colors || {};

let cartPage_datePicker_ajdustX = RENTMY_GLOBAL?.emDateTimePicker?.cartPage_datePicker_ajdustX || 0;
let cartPage_datePicker_ajdustY = RENTMY_GLOBAL?.emDateTimePicker?.cartPage_datePicker_ajdustY || 0;

let afterAddtoCart_open_widget = RENTMY_GLOBAL?.emDateTimePicker?.afterAddtoCart_open_widget ?? true;
let afterAddtoCart_open_widget_datePicker = RENTMY_GLOBAL?.emDateTimePicker?.afterAddtoCart_open_widget_datePicker ?? false;

let timePickerUi = RENTMY_GLOBAL?.emDateTimePicker?.timePickerUi || 'standard';
let timePickerButtons = RENTMY_GLOBAL?.emDateTimePicker?.timePickerButtons ?? true;

</script>

<template>
    <teleport :to="wrapper">
        <cartPageComponent></cartPageComponent>
    </teleport> 

    <div v-show="picker.showDatePicker">
        <template v-if="isMounted && (confg?.show_start_date || confg?.show_end_date)">
            <teleport :to="isInpageWidget ? wrapper : '#RentMyDatePicker'">         
                <EmDateTimePicker ref="dateTimePicker"
                    v-model="pickerModelValue"
                    @change="(data) => { emitter.emit('change_date', data);log({data}) }"
                    @close="false"
                    :displayFormat="DATE_FORMAT"
                    :__rangePicker__="(confg.show_start_date && !confg.show_end_date) || (!confg.show_start_date && confg.show_end_date)" 
                    :rangePicker="(confg.show_start_date && confg.show_end_date)" 
                    :startDate="order_details?.start_date" 
                    :timePicker="showingTimePicker" 
                    :minDate="cartData?.min_rent_start"
                    :isDisabled="picker.disable"
                    :theme="pickerTheme"
                    :colors="pickerColors"
                    :autoOpen="false"
                    :timePickerButtons="timePickerButtons"
                    :timePickerUi="timePickerUi"
                    :use24FormatTimeForEvents="true"
                    style="max-width: 374px;"
                    :invisible="isInpageWidget ? true : false"
                    :adjustX="cartPage_datePicker_ajdustX"
                    :adjustY="cartPage_datePicker_ajdustY"
                    :use24Format="false"
                    displayIn="modal"
                    >
                </EmDateTimePicker>
            </teleport>   
        </template>   
    </div>
    
</template>