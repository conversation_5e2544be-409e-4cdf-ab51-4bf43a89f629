import { reactive, computed, ref, onMounted } from 'vue';
import checkoutService from '../services/checkout.service';
import { useGlobalStore } from '@stores/global';
import cookie from '@utils/cookie';

export const useCheckoutStore = defineStore('checkout', () => {
  
  const state = reactive({
    settings: null,
    deliverySettings: null,
    customFields: null,
    primaryAddressList: null,
    shippingAddressList: null,
    additionalCharges: null,
    termAndConditions: null,
    isFreeShipping: false,
  })

  const globalStore = useGlobalStore(); 


  let settings = computed(() => state.settings );
  let deliverySettings = computed(() => state.deliverySettings );
  let customFields = computed(() => state.customFields );
  let primaryAddressList = computed(() => state.primaryAddressList );
  let shippingAddressList = computed(() => state.shippingAddressList );
  let additionalCharges = computed(() => state.additionalCharges );
  let termAndConditions = computed(() => state.termAndConditions );
  let isFreeShipping = computed(() => state.isFreeShipping );
  
  const getCart = async (token, params={}) => {
    let response = await checkoutService.getCart(token, params);
    return response;
  };
  const freeShipping = async (cartToken) => {
    if(!cartToken) return;
    let response = await checkoutService.freeShipping(cartToken);
    state.isFreeShipping = response?.result?.data
  };  
  const getCheckoutCustomFields = async () => {
    let response = await checkoutService.getCheckoutCustomFields();
    state.customFields = response.result.data;
  };  
  const getTermAndConditions = async (product_ids = '') => {
    let response = await checkoutService.getTermAndConditions(product_ids);
    if(response.status == 'OK' && response?.result?.data?.length){  
        /* old code */
        // state.termAndConditions = response.result.data
        // return  

        let contents = response.result.data
            .sort((a, b) => (a.parent === b.parent ? 0 : a.parent ? -1 : 1))
            .reduce((acc, item) => {
              let productName = item.product_name ? `<strong>${item.product_name}</strong><br>` : '';
              return acc + productName + item.content;
            }, ''); 

        let parentItem = response.result.data.filter(item => item?.parent)?.[0] || {}

        state.termAndConditions = {
          "contents": {
                ...parentItem,
                "content": contents, 
            }
        }

        // console.log('state.termAndConditions', state.termAndConditions);
    }
  }; 
  const getAddressList = async (type/**Primary | Shipping */, address_id=null) => {
    let response = await checkoutService.getAddressList(type, address_id);
    if(response.status == 'OK' && response?.result?.data){
      if(type == 'Primary'){
        state.primaryAddressList = response.result.data;
      }
      else if(type == 'Shipping'){
        state.shippingAddressList = response.result.data;
      }
    }
  };  
  const getAdditionalCharges = async (cart_token) => {
    let response = await checkoutService.getAdditionalCharges(cart_token);
    if(response.status == 'OK' && response?.result?.data){ 
        let additionalCharges = response.result.data;     
        state.additionalCharges = additionalCharges?.map(charge => {
          if(charge.is_required) charge.fee.amounts = [charge.fee.amounts[0]];
          return charge;
        })
    }
  }; 
  const cartDelivery = async ({token=null, shipping_method=1, shipping_cost=0, tax=0}={}) => {
    let payload = {
      tax,
      token,
      shipping_cost,
      shipping_method,
     }
    let response = await checkoutService.cartDelivery(payload);
    if(response.status == 'OK' && response?.result?.data){ 
        let cartData = response.result.data;
        return cartData;
    }
  }; 

  return {
    settings,
    deliverySettings,
    customFields,
    primaryAddressList,
    shippingAddressList,
    additionalCharges,
    termAndConditions,
    isFreeShipping,
    
    getCart,
    freeShipping,
    getCheckoutCustomFields,
    getTermAndConditions,
    getAddressList,
    getAdditionalCharges,
    cartDelivery,
  }
})
import { defineStore } from 'pinia';