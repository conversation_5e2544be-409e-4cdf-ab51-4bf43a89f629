import http from  '@utils/http';
import { httpAuth } from  '@utils/http'

export default {
  async getCart(token, params={}) {
    let response = await http.get(`/carts/${token}`, {params});
    return response.data
  }, 
  async freeShipping(cartToken) {
    let response = await http.get(`/free-shipping/${cartToken}`);  
    return response.data
  },
  async getCheckoutCustomFields() {
    let response = await http.get(`/custom-fields?section=checkout`);   
    return response.data
  },
  async getTermAndConditions(product_ids = '') {
    // let response = await http.get(`/pages/terms-and-conditions`, {params: { product_ids }}); // old api
    let response = await http.get(`/terms-and-condition`, { params: { product_ids }}); // new api
    return response.data
  },
  async getAddressList(type/**Primary | Shipping */, address_id=null){
    let url = `/customers/address/${address_id || ''}?type=${type}`;
    let response = await httpAuth.get(url);
    return response.data;
  },
  async getAdditionalCharges(cart_token){
    let response = await http.get(`/settings/orders/additional-charges?type=active&cart_token=${cart_token}`);
    return response.data;
  },
  async cartDelivery(payload){
    let response = await http.post(`carts/delivery`, payload);
    return response.data;
  },
}