<script setup>
import { ref, inject, defineProps, provide, computed, defineComponent, } from "vue";
import SignaturePad from 'signature_pad';
import { useCheckoutStore } from "../controllers/checkout.controller";
import Pagination from "@components/Pagination.vue";
import { removeOnlineStoreCartDate, datePipe } from "@utils/functions";
import { createComponentHTML } from "@utils/functions/withComponent";
import { useCartStore } from '../../cart/controllers/cart.controller'
let { helper, cookie, domElement, http, labelSelector, currency } = inject("utils");
let { userService } = inject("services");
let allowDyanmicContent = inject('allowDyanmicContent');
let printSelector = inject('printSelector');
let { wrapper } = defineProps(["wrapper"]);
let globalLoader = inject("globalLoader");
let setWraperIsReady = inject("setWraperIsReady");
import { Toaster, emitter } from "@/import-hub";

import { useWishlistStore } from "@stores/wishlist";
const wishlistStore = useWishlistStore()
 
import { useGlobalStore } from "@stores/global";
import { GenerateCompoenentHtml } from "./GenerateCompoenentHtml";
import initAutocomplete from "@/googleMap";
import TermsAndConditions from "@components/modal/TermsAndConditions.vue";

let globalStore = useGlobalStore();
let checkoutStore = useCheckoutStore();
let cartStore = useCartStore();
let modalNonClosableUntilLogin = ref(false);
let orders = ref([]);
provide("modalNonClosableUntilLogin", modalNonClosableUntilLogin);
let showLoginModal = inject('showLoginModal', false); 
provide("showLoginModal", showLoginModal);

/* ---------------------------------------- */
/*         Generating Template HTML         */
/* ---------------------------------------- */
GenerateCompoenentHtml(wrapper, allowDyanmicContent, printSelector);

/* ----- End Generating Template HTML ----- */

let template = wrapper.innerHTML;
wrapper.innerHTML = "";

let checkoutComponent = defineComponent({
  components: {
    TermsAndConditions
  },
  template: `
    ${template} 
    <template v-if="checkoutStore.termAndConditions?.contents?.content">
      <TermsAndConditions 
        v-model="showModal" 
        title="Terms and Conditions" 
        :hasCheckboxes="hasCheckboxes"
        :content="checkoutStore.termAndConditions?.contents?.content">
      </TermsAndConditions>
    </template>`,
  data() {
    return {
      helper,
      initAutocomplete,
      globalStore,
      currency,
      checkoutStore,
      cartStore,
      RentMyEvent,
      RENTMY_GLOBAL,
      SignaturePad,
      hasCheckboxes: false,
      print_data: [], // just for dev
      currencyConfig: null,
      isMounted: false,
      isAuthenticated: false,
      is_show_signin: false,
      sameAsAbove: true,
      showShippingForm: false,
      isShowBillingAddressSection: false,
      store_name: RENTMY_GLOBAL.store_name,
      countries: [],
      default_location: null,
      all_locations: null,
      customer_info: RENTMY_GLOBAL.rentmy_customer_info,
      cartData: null,
      selectedCountry: null,
      site_specific: null,
      allowDyanmicContent,
      shipping_method: null,
      shipping_methods: [],
      delivery_costs: [],
      is_delivery_by_zone: false,
      is_delivery_outside_of_area: false,
      current_location: null,
      tyipingForm: 'billingForm',
      delivery_error: null,
      selected_shipping_address: '',
      deliveryAddressErrorMsg: null,
      signaturePad: null,
      show_signature_pad: false,
      accepted_term_and_conditions: RentMyEvent.apply_filters('cdn:term_and_condition_value', false),
      customer_guest_checkout: true,
      orders,
      wishlistStore,
      showWishListSummary: false,
      highlight_shipping_method: false,
      tax_calculate_by: '', // billing | shipping | delivery  | store (delivery && sipping are same)
      isQuote: helper.withURL.getQuery('quote') === 'true',

      /** 
       * With Extra Checkobox
       */
      checkout_extraCheckboxText: RENTMY_GLOBAL?.checkout_extraCheckboxText || '',
      isChecked_extraCheckbox: !RENTMY_GLOBAL?.checkout_extraCheckboxText ? true : false,

      showModal: false,
      errors: {
        billingForm: {},
        shippingForm : {},
        customFields : {},
      },

      /** trucking delivery method selection delivery | pickup | shipping >> anyone selection is required */
      shippingType: {
        type: '',
        applied: '',
      },

      /* ------------------------------------ */
      /*         Start Order Payload          */
      /* ------------------------------------ */
      common: {
        token: localStorage.getItem('token'),
        address_id: null,
        special_requests: null,
        special_instructions: null,
        driving_license: null,
        pickup : null, // location_id: 2615
        signature: null,
        is_customer_account: false,
        delivery: null, // shipping/delivery cost object
        shipping_method: null,
        "currency": '', // "USD",
        "location_id": null,
        // payment_amount 0 for prevent hosted payment validation
        payment_amount: 0
      },
      billingForm: {
        first_name: null,
        last_name: null,
        mobile: null,
        email: null,
        company: null,
        country: "us",
        address_line1: null,
        address_line2: null,
        city: null,
        state: null,
        zipcode: null,
      },
      custom_fields: {},
      shippingForm: {
        shipping_first_name: null,
        shipping_last_name: null,
        shipping_mobile: null,
        shipping_email: null,
        shipping_company: null,
        shipping_country: null,
        shipping_address1: null,
        shipping_address2: null,
        shipping_city: null,
        shipping_state: null,
        shipping_zipcode: null,
        combinedDeliveryAddress: null,
      },
      /* -------- End Start Order Payload ------- */
      timeout: null,
      allowDyanmicContent
    };
  },
  setup() {    
    let showLoginModal = inject("showLoginModal");
    return {
      showLoginModal,
    };
  },
  async mounted() {

    {
      const site_specific = JSON.parse( localStorage.getItem("rentmy_contents") )?.site_specific; 
      this.showWishListSummary = site_specific?.confg?.inventory?.wishlist?.active && helper.withURL.getQuery('order_type') === 'quote' && helper.withURL.getQuery('with') === 'wishlist'
      this.tax_calculate_by = site_specific?.confg?.tax?.address
    }

    await this.updateCart();

    if(localStorage.getItem('current_location')){
        this.current_location = JSON.parse(localStorage.getItem('current_location'));
        this.common.location_id = this.current_location.id;
        this.common.pickup = this.current_location?.id;
        this.billingForm.country = this.current_location.country;
    }

    if(localStorage.getItem('rentmy_currency_config')){
        let store_currency = JSON.parse(localStorage.getItem('rentmy_currency_config'));
        this.common.currency = store_currency.code;
    }

    this.shipping_method = sessionStorage.getItem('online_fullfilment_option');
    let { rentmy_customer_info } = RENTMY_GLOBAL;
    if (rentmy_customer_info) {
      this.isAuthenticated = true;
      Object.keys(this.billingForm).forEach(
        (key) => (this.billingForm[key] = rentmy_customer_info[key] ?? null)
      );
    } 

    await globalStore.getTags();
    let settings = JSON.parse(localStorage.getItem('online_store'))
    if (settings) {
      this.selectedCountry = settings?.location?.country;
      this.addressList = settings?.locations;
    }
    await globalStore.getLocationList();
    this.currencyConfig = globalStore.currency_config;
    await globalStore.getDeliverySettings(); //{delivery, instore_pickup, shipping}
    this.default_location = globalStore.storeAndLocations?.location;
    this.all_locations = globalStore.storeAndLocations?.locations;

    await checkoutStore.freeShipping(this.cartData?.token);
    await checkoutStore.getCheckoutCustomFields();

    if(this.cartData?.cart_items?.length){ 
      const products_ids = this.cartData?.cart_items.map(item => item.product_id).join(',') 
      await checkoutStore.getTermAndConditions(products_ids);
    }

    if (this.isAuthenticated) {
      await checkoutStore.getAddressList('Primary'); // checkoutStore.primaryAddressList
      let primaryAddress = checkoutStore.primaryAddressList?.filter(addr => addr.is_primary)?.[0];
      if(primaryAddress) {
        this.onChangeBillingAddress(primaryAddress);
        this.shippingFormMakeSameAsBilling(true);
      }
      await checkoutStore.getAddressList('Shipping'); //checkoutStore.shippingAddressList
    }
    await checkoutStore.getAdditionalCharges(this.common.token);

    this.site_specific = JSON.parse( localStorage.getItem("rentmy_contents") )?.site_specific;
    this.is_show_signin = this.site_specific.confg?.customer?.active;
    this.countries = JSON.parse(localStorage.getItem("rentmy_countries"));

    this.customer_guest_checkout = [true, undefined].includes(this.site_specific?.confg?.customer?.customer_guest_checkout)
    if(!this.customer_guest_checkout && !this.isAuthenticated){ 
      this.showLoginModal = true
    } 
    

    emitter.on("forModal:loginSuccess", (data) => {
      windowLocation().reload();
    });
    setWraperIsReady();
    if(helper.withURL.getQuery('paymentStatus') === 'failed'){
      Toaster().error('Payment failed');
    }
    
    this.isMounted = true;
    setTimeout(() => {
      initAutocomplete(this.$refs.billing_addressLine1, this.billingForm.country);
    }, 1000)
    emitter.on('auto_fill_address', this.autoFillAddress);
    RentMyEvent.emit('cdn:checkout_page:mounted', true);

    // auto selcting delivery tab 
    if(this.isActiveDelivery()){
      this.onClickShippingTab('delivery');
    }
    else if(this.isActivePickup()){
      this.onClickShippingTab('pickup');
    }
    else if(this.isActiveShipping()){
      this.onClickShippingTab('shipping');
    }
  },
  watch: {
    shippingForm: {
      handler(newData, oldData) {  
        this.shippingFormMakeSameAsBilling(this.sameAsAbove);
        if(!this.isFillAllShippingReqiredFields()){
          this.delivery_costs = [];
        } else {
          this.getDeliveryCosts();
        }
        if(!this.sameAsAbove && this.shipping_method === 'shipping'){
          let wasApplied = this.shippingType.applied
          this.shippingType.applied = false
          this.highlight_shipping_method = true
          this.getShippingMethods()
        }
      },
      deep: true,
    },
    shippingType: {
      handler(newData, oldData) {  
        if(newData.applied === true){
          this.highlight_shipping_method = false
        } else {
          this.highlight_shipping_method = true
        }
      },
      deep: true,
    },
    
  },
  computed: {
    customFields: function () {
      if(checkoutStore.customFields?.length) {
        let fields = checkoutStore.customFields.filter((field) =>
          [0, 1, 2].includes(+field?.field_type)
        );
        fields.forEach(_field => {
          if(!(this.custom_fields.hasOwnProperty(_field.field_name)))
            this.custom_fields[_field.field_name] = null;
        });
        return fields;
      }
      return [];
    },
    showAdditionalField: function () {
       
      if ( this.site_specific && this.site_specific.confg['show_checkout_additional_field'] ) { 
        return this.site_specific.confg['show_checkout_additional_field'];
      }
      return false;
    }, 
  },
  methods: {
    datePipe,
    Toaster,
    urlToLabel: function(url) {  
      let text = url
      if(!text) return '';
      text = String(text);
      const ucFirst = (str) => (str.charAt(0).toUpperCase() + str.slice(1).toLowerCase());
      text = text.replace(/_/g, '-')
      let final_text = text.split('-').map(ucFirst).join(' ');
      return final_text
    },
    isValidEmail(email) {
      const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return regex.test(email); 
    },
    log: console.log,
    make_get_cart_params(params={}){
 
      let { tax_calculate_by } = this
  
      if(tax_calculate_by === 'store'){
        return params
      }
      else if(tax_calculate_by === 'billing'){

        const { 
          country: billing_country,
          city: billing_city,
          state: billing_state,
          zipcode: billing_zipcode,
         } = this.billingForm

         params = { ...params, billing_country, billing_city, billing_state, billing_zipcode }
        return params

      }
      else if(tax_calculate_by === 'shipping' || tax_calculate_by === 'delivery'){
        params = {...params, ...helper.clone(this.shippingForm, { only: [
          'shipping_country',
          'shipping_city',
          'shipping_state',
          'shipping_zipcode',
        ]})}
        return params
      }
    },
    async shippingFormMakeSameAsBilling(permit=false){
        if(this.billingForm.email){
          if(!this.isValidEmail(this.billingForm.email)){
            this.billingForm.email = ''
            Toaster().error("Email address is invalid");
          }
        }
        if(permit){
            this.shippingForm.shipping_first_name = this.billingForm.first_name;
            this.shippingForm.shipping_last_name = this.billingForm.last_name;
            this.shippingForm.shipping_mobile = this.billingForm.mobile;
            this.shippingForm.shipping_email = this.billingForm.email;
            this.shippingForm.shipping_company = this.billingForm.company;
            this.shippingForm.shipping_country = this.billingForm.country;
            this.shippingForm.shipping_address1 = this.billingForm.address_line1;
            this.shippingForm.shipping_address2 = this.billingForm.address_line2;
            this.shippingForm.shipping_city = this.billingForm.city;
            this.shippingForm.shipping_state = this.billingForm.state;
            this.shippingForm.shipping_zipcode = this.billingForm.zipcode;
            if(this.sameAsAbove){
              if(['shipping', 'delivery'].includes(this.shipping_method) && !checkoutStore.isFreeShipping && !this.showWishListSummary){
                await this.getShippingMethods()
                let wasApplied = this.shippingType.applied
                this.shippingType.applied = false
                if(wasApplied){
                  this.highlight_shipping_method = true
                }
              }
            }
        }

    },
    onChangeCountry(form_name = 'billing'){
        if(form_name == 'billing'){
            this.billingForm.city = null;
            this.billingForm.state = null;
            this.billingForm.zipcode = null;
        } else {
            this.shippingForm.shipping_city = null;
            this.shippingForm.shipping_state = null;
            this.shippingForm.shipping_zipcode = null;
        }
    },
    async updateCart(params={}, action_from='') {
      if(action_from !== 'additional_charges_checked'){
        // billing | shipping | delivery  | store
        let tax_address_is_store = this.tax_calculate_by === 'store'
        if(tax_address_is_store) return
      }
      
      if(this.showWishListSummary){
        return
      }
      // This api will be calclate tax
        const cartData = localStorage.getItem("user_cart");
        if(cartData) {
          this.cartData = JSON.parse(cartData);
          this.common.token = this.cartData.token;
          params = this.make_get_cart_params(params)
          let response = await cartStore.getCart(this.common.token, params);
          this.cartData = response.result.data;
          this.updateCheckoutInfo();
          this.productCustomPricingDataFormat(this.cartData);
        }

        checkoutStore.freeShipping(this.cartData?.token);
    },
    productCustomPricingDataFormat(cart) {
      this.orders = [];
      if(cart?.cart_items?.length) {
        cart.cart_items.forEach(item => {
        if (item?.cart_product_options?.length) {
          item.cart_product_options.forEach(options => {
            let text = '';
            let idx = 0;
            // Filter private product options 
            let publictOptions = options.options.filter(el => !el.is_private)
            const size = publictOptions.length;
            publictOptions.forEach(elem => { // idx = 0
              text = text + elem.label + ': ' + elem.value;
              if (idx < size - 1) {
                text = text + ', ';
              }
              idx++;
            });
            text = text + ' ( qty : ' + options.quantity + ' ) ';
            this.orders.push(text);
            text = '';
          });
        }
      });  
      }
    },
    onClickNewBillingAddress() {
      this.isShowBillingAddressSection = true;
      this.common.address_id = null; 

      this.billingForm.address_line1 = "";
      this.billingForm.address_line2 = "";
      this.billingForm.country = this.selectedCountry;
      this.billingForm.city = "";
      this.billingForm.state = "";
      this.billingForm.zipcode = "";
    },
    onChangeBillingAddress(address) {
      this.isShowBillingAddressSection = false;
      this.common.address_id = address.id;
      this.common.delivery = null;
      this.billingForm.address_line1 = address.address_line1;
      this.billingForm.address_line2 = address.address2;
      this.billingForm.country = address.country?.toUpperCase();
      this.billingForm.city = address.city;
      this.billingForm.state = address.state;
      this.billingForm.zipcode = address.zipcode;

      this.shippingFormMakeSameAsBilling(this.sameAsAbove);
      this.getDeliveryCosts() ;
    },
    async autoFillAddress(address) {
        this.common.address_id = null;
        if(this.tyipingForm == 'billingForm'){
            this.billingForm.address_line1 = address.location;
            this.billingForm.country = address.country_short_name;
            this.billingForm.city = address.city;
            this.billingForm.state = address.state;
            this.billingForm.zipcode = address.zipcode;
            this.shippingFormMakeSameAsBilling(this.sameAsAbove);

            this.updateCart();
        }
        else if (this.tyipingForm == 'shippingForm'){
            this.shippingForm.shipping_address1 = address.location;
            this.shippingForm.shipping_country = address.country_short_name;
            this.shippingForm.shipping_city = address.city;
            this.shippingForm.shipping_state = address.state;
            this.shippingForm.shipping_zipcode = address.zipcode;
        } 
        this.getDeliveryCosts() ;
        
    },
    onChangeUploadFile(event, fieldName) {
      let file = event.target.files?.[0];
      if (file) {
        const payload = { file, type: fieldName };
        http
          .post("/media/upload", payload, { formData: true })
          .then((response) => {
            if (response.data.status == "OK") {
              this.custom_fields[fieldName] =
                response.data.result.data.filename;                
            } else {
              event.target.value = null;
              Toaster().error("Upload Failed");
            }
          })
          .catch((error) => {
            Toaster().error(error);
          })
          .finally(() => {
            this.checkCustomFieldsValidation(fieldName);
          });
      }
    },
    // onClickTab
    async onClickShippingTab(type=null) { // pickup | shipping | delivery  

        if(!type) type = sessionStorage.getItem('online_fullfilment_option');
        if(!type){
            console.warn('onClickShippingTab()', 'sipping type not found');
            return;
        }

        this.shippingType = {
          type, 
          applied: type == 'pickup' ? true : '',
        }


        sessionStorage.setItem('online_fullfilment_option', type.toLocaleLowerCase()); 
        this.shipping_methods = [];       
        this.delivery_costs = [];

        this.shipping_method = type;

        if(type == 'pickup'){    
          globalLoader.show();
          let cartData = await checkoutStore.cartDelivery({token: this.common.token, shipping_method: 1});
          this.cartData = cartData;
          this.common.shipping_method = cartData?.shipping_method  // 4
          globalLoader.hide();  

          if(globalStore.locationList?.length){
            let location  = this.current_location.id
            this.onClickPickupAddress(globalStore.locationList.filter(loc => loc.id == location)?.[0])
          }
        }
        else if(type == 'shipping'){
            this.showShippingForm = false;
            this.selected_shipping_address = false;
            globalLoader.show();
            let cartData = await checkoutStore.cartDelivery({token: this.common.token, shipping_method: 4});
            this.cartData = cartData;
            this.common.shipping_method = cartData?.shipping_method  // 4
            globalLoader.hide(); 
        }
        else if(type == 'delivery'){
          this.showShippingForm = true;
          setTimeout(() => initAutocomplete(this.$refs.shipping_addressLine1) , 100);
          globalLoader.show();
          let cartData = await checkoutStore.cartDelivery({token: this.common.token, shipping_method: 2});
          this.cartData = cartData;
          this.common.shipping_method = cartData?.shipping_method // 2
          globalLoader.hide();
          this.getDeliveryCosts();
        }

        this.updateCart();
    },
    onClickPickupAddress(location) { 
        if(!this.common.token) return;
        this.common.pickup = location.id
        let payload = {
            "shipping_method": this.cartData.shipping_method,
            "token": this.common.token,
            "shipping_cost": this.cartData.delivery_charge,
            "tax": this.cartData.tax.total,
        }
        if(this.is_delivery_outside_of_area){
          payload.shipping_cost = '0'
        }
        if(payload.tax) payload.tax = 0
        http.post('/carts/delivery', payload).then(response => {
            this.updateCart();
        })

    },
    onClickShippingAddress(address) { 
        this.sameAsAbove = false;
        this.showShippingForm = false;
        this.selected_shipping_address = address;
        this.shippingForm.shipping_mobile = address.phone;
        this.shippingForm.shipping_email = address.email;
        this.shippingForm.shipping_country = address.country;
        this.shippingForm.shipping_address1 = address.address_line1;
        this.shippingForm.shipping_address2 = address.address_line2;
        this.shippingForm.shipping_city = address.city;
        this.shippingForm.shipping_state = address.state;
        this.shippingForm.shipping_zipcode = address.zipcode;
    },
    onClickShippingCreateNew() {
        this.sameAsAbove = false;
        this.showShippingForm = true;
        this.selected_shipping_address = null;
        Object.keys(this.shippingForm).forEach(key => this.shippingForm[key] = null );
        setTimeout(() => initAutocomplete(this.$refs.shipping_addressLine1) , 100);
    },
    async getShippingMethods() {
        const payload = {
            address: this.shippingForm,
            pickup: this.RENTMY_GLOBAL.locationId,
            token: this.common.token,
        }
        globalLoader.show();
        this.shipping_methods = null;
        let response = await http.post('/shipping/rate', payload) 
        globalLoader.hide();
        if(response.data.status == 'OK'){
            let methods = response.data.result; 
            delete methods.weight;               
            this.shipping_methods = methods;
        } 
    },
    updateCheckoutInfo() {
        let token = this.common.token;
        if(!token) return;
        const payload = {
          billing_info: {...this.billingForm},
          fulfilment_info: {...this.shippingForm},
        }
        // this.shipping_methods = null;
        http.post(`/carts/${token}/update-checkout-info`, payload).then(response => {
            // nothing
        })
    },
    onClickShppingMethodOrDeliveryCost(methodOrCost) {
        this.common.delivery = methodOrCost;
        const payload = {
            address: {...this.billingForm, ...this.shippingForm},
            shipping_cost: methodOrCost?.charge,
            shipping_method: this.cartData?.shipping_method,
            token: this.common.token,
        }
       
        if((this.shipping_method == 'delivery' || this.shipping_method == 'pickup') && this.is_delivery_outside_of_area){
          payload.shipping_cost = '0' 
        }
        globalLoader.show();
        if(payload.tax) payload.tax = 0
        http.post('/carts/delivery', payload).then(async (response) => {
            await this.updateCart(); 
            globalLoader.hide();  
            this.shippingType.applied = true
        })
    },   
    getDeliveryCosts() {
      if((this.isActiveDelivery() && this.shipping_method === 'delivery')){
        clearTimeout(this.timeout);
        this.timeout = setTimeout(() => {  
          
          const payload = {
              address: this.shippingForm,
              pickup: this.RENTMY_GLOBAL.locationId,
          } 
  
          let params = {
            // billing_country: this.billingForm.country?.toLowerCase() || '',
            // billing_city: this.billingForm.city || '',
            // billing_state: this.billingForm.state || '',
            // billing_zipcode: this.billingForm.zipcode || '',
  
            shipping_country: this.shippingForm.shipping_country?.toLowerCase() || '',
            shipping_city: this.shippingForm.shipping_city || '',
            shipping_state: this.shippingForm.shipping_state || '',
            shipping_zipcode: this.shippingForm.shipping_zipcode || '',
          }
          
          globalLoader.show();
          this.delivery_costs = [];
          this.is_delivery_outside_of_area = false;
          http.post('/delivery-charge-list', payload, {params}).then(async response => {
              globalLoader.hide();
              if(response.data.status == 'OK'){ 
                  this.deliveryAddressErrorMsg = null;                            
                  this.is_delivery_by_zone = this.delivery_costs?.[0]?.['charge_by'] === 'zone';
                  
                  this.delivery_costs = response.data.result?.location;
                  this.is_delivery_outside_of_area = this.delivery_costs?.[0]?.['max_distance'] == true; 
  
                  if(!this.isFillAllShippingReqiredFields()){
                    this.delivery_costs = [];
                    this.updateCart();
                    return;
                  }
  
                  if(this.delivery_costs?.length){
                    let selectedCost = null;
                    let index = this.delivery_costs.findIndex(addr => addr.matched === true);
                    // if(index > -1){
                    if(true){
                      this.delivery_costs.forEach((cost, i) => {
                        // if(i !== index){
                        //   cost.hideMe = true;
                        // } else {
                          cost.selectMe = true;
                          selectedCost = cost; 
                        // }
                      })
                    }
                    if(selectedCost){
                      this.onClickShppingMethodOrDeliveryCost(selectedCost);
                    }
                  }
                  
              } else {
                  this.deliveryAddressErrorMsg = response.data.result.error;               
              }         
          })
        }, 200);

      } else {
        this.updateCart();
      }

    },    
    onClickAdditionalCheckboxOrLabel(charge) {     
      this.onClickAdditionalAmount(charge, charge.fee.amounts?.[0], true);
    },
    onClickAdditionalAmount(charge, amount, useToogle=false) {
      if(!useToogle) charge.is_selected = true;
      const payload = {
            order: null,
            cart_token: this.common.token,          
            additional_charges: checkoutStore.additionalCharges?.map(_charge => {

            return ({
              id: _charge.id,
              is_selected: _charge.is_required || ((useToogle && charge.id == _charge.id) ? !_charge.is_selected : _charge.is_selected),
              value: _charge.is_required ? _charge.fee?.amounts[0] : (charge.id == _charge.id ? amount : _charge?.fee.amounts?.[0]) || '',
              selected_option: _charge?.selected_option || _charge?.options?.split(";")?.[0] || '',
              order_additional_charge_id: _charge?.existing?.id || '',
            })
          })
        }
        if(globalLoader.isShowing()) return;
        globalLoader.show();
        http.post('/orders/additional-charges/create', payload)
        .then(async response => {
          if(response.data.status == 'OK'){            
            await this.updateCart({action: 'add_service_charge'}, 'additional_charges_checked');  
            await checkoutStore.getAdditionalCharges(this.common.token);       
            globalLoader.hide();
          }
        })
        .catch(onClickAdditionalAmount_error => {
          console.warn({onClickAdditionalAmount_error});
        })
    },
    OnSubmitOptionChargeInputedAmount(charge, amount) {
      this.onClickAdditionalAmount(charge, amount || charge?.fee.amounts[0]);
    },
    OnCancelOptionChargeInputedAmount(charge) {
      charge.show_input_custom = false;
    },
    isFillAllShippingReqiredFields() {
      let { shipping_first_name,
          shipping_last_name,
          shipping_mobile,
          shipping_email,
          shipping_country,
          shipping_address1,
          shipping_city,
          shipping_state,
          shipping_zipcode,
        } = this.shippingForm;

        return (shipping_first_name &&
          shipping_last_name &&
          shipping_mobile &&
          shipping_email &&
          shipping_country &&
          shipping_address1 &&
          shipping_city &&
          shipping_state &&
          shipping_zipcode)
    },
    // placeOrder
    onClickPlaceOrder(event) {

      if(!this.customer_guest_checkout && !this.isAuthenticated){
        this.showLoginModal = true
        return
      }
      
      
      if(!this.common.token){
        Toaster().error('May your cart is empty (token not found)');
        return;
      }  

      if(!this.isValidateAll()){
        Toaster().warning('Please complete all required fields'); 
        return;
      }  
      
      if(this.site_specific?.confg?.checkout?.hide_fulfillment === false) {
        if(!this.shippingType.applied){ 

          if(!(this.checkoutStore.isFreeShipping && this.shippingType.type == 'shipping')){
            Toaster().error(`Please select a fulfilment option!`);  
            return;
          }
        }
      }
      if(!this.isQuote){
        if(!this.isChecked_extraCheckbox){
          Toaster().warning( RENTMY_GLOBAL?.checkout_extraCheckboxText_alertText || 'Please complete the required field(s)');
          return;
        }   
      }

      if(!this.accepted_term_and_conditions){
        Toaster().warning('Please accept Terms & Conditions');
        return;
      }       
      
      if(this.signaturePad){
        this.common.signature = this.signaturePad.toDataURL();
        if(this.signaturePad?.isEmpty()){
          Toaster().warning('Please add your signature');
          return;
        }      
      };
       
      
      const payload = {
        type : 2,
        // order_source : "WP Plugin",
        "order_source": "Online",
        ...this.common,
        ...this.billingForm,
        ...(this.shipping_method === 'pickup' ? {} : this.shippingForm),
        additional_charges: checkoutStore.additionalCharges?.map(charge => {
            return {
                id: charge.id,
                value: (charge.is_required ? charge.fee?.amounts[0] : charge?.existing?.amount) || ''
            }
        }),
        custom_checkout: {
          fields: this.customFields.map(field => {
            return { 
              [field.field_name]: this.custom_fields[field.field_name] || '' 
            }
          })
        },
        custom_values: this.customFields.map(field => {
          return {
              id : field.id || '',
              field_label: field.field_label || '',
              field_name : field.field_name || '',
              field_values : this.custom_fields[field.field_name] || '',
              type : field.field_type || '',
            }
        })
      };

      if(this.isQuote){
        payload.quote = true
      }

      if(globalLoader.isShowing()) return;      
      globalLoader.show();
      http.post('/orders/online', payload).then(async response => {
          globalLoader.hide();
          if(response.data?.status == 'OK'){
            const data = response.data?.result?.data;
            if(data.order.success){  
           

              let order_id = data.payment?.order_id;
              const customer_id = window.RENTMY_GLOBAL.rentmy_customer_info?.customer_id
              const location_id = window.RENTMY_GLOBAL.locationId;
              const store_slug = window.RENTMY_GLOBAL.store_name;
              const payment_source = 'online_checkout';
              const encodedData = helper.withURL.encodeString(`o=${order_id}&s=${store_slug}&l=${location_id}&c=${customer_id || ''}&p=${payment_source}`).replace(/=/g, '');
              const PAYMENT_DOMAIN = RENTMY_GLOBAL?.env?.PAYMENT_DOMAIN || import.meta.env.VITE_PAYMENT_DOMAIN;
              // For decode: decodeURIComponent(escape(atob(data)))
              let makeUrl = (params={}, page=null) => helper.withURL.urlEncoded(helper.withURL.setQuery(params, page, true));
              const uid = data?.order?.data?.uid || ''; 
              const successUrl = RENTMY_GLOBAL?.afterOrder?.paymentSuccessUrl || makeUrl({ uid }, RENTMY_GLOBAL?.page?.order_complete);
              const cancelUrl = RENTMY_GLOBAL?.afterOrder?.paymentCancelUrl || makeUrl({paymentStatus: 'failed'});
              const paymentURL = `${PAYMENT_DOMAIN}payments/${encodedData}/?success=${encodeURI(successUrl)}&cancel=${encodeURI(cancelUrl)}`;

              localStorage.setItem('last_order_uid', uid);

              let order_data = data?.order;

              localStorage.removeItem('token');
              localStorage.removeItem('user_cart');
              removeOnlineStoreCartDate();

              /* -------------------------------- */
              /*          Toater message          */
              /* -------------------------------- */
              let toasterMessage = RentMyEvent.apply_filters('cdn:order_successfull_message', 'Your order has been placed successful!');
              let placeOrderBtn = document.querySelector('[PlaceOrderBtn]');
              if(placeOrderBtn && placeOrderBtn.getAttribute('successMessage')){
                toasterMessage = placeOrderBtn.getAttribute('successMessage');
                if(toasterMessage) Toaster().success(toasterMessage);
              }
              /* ----------------------- End Toater Messaage ---------------------- */
                
              setTimeout(() => {
                if(RENTMY_GLOBAL?.using_in_cli_project || RENTMY_GLOBAL?.afterOrder?.justEmit){
                  RentMyEvent.emit('cdn:checkout_page:order_completed', {
                    paymentURL, 
                    uid: order_data?.data?.uid,
                    is_quote: false,
                  })
                } else {

              
                  if(helper.withURL.getQuery('quote') === 'true'){
                    helper.redirect('order_complete'); 
                  } else {   
                    if(this.RENTMY_GLOBAL?.afterOrder?.forIframe_topMode){


                      /**
                       * Add this line of code in prent top socope
                       ======================================================                       
                       window.addEventListener('message', function(event) {
                            if (event.data.action === 'goRentMyPaymentPage') {
                                window.open(event.data.url, '_self');
                            }          
                        });

                       */

                      console.log({ action: 'goRentMyPaymentPage', url: paymentURL });
                      window.top.postMessage({ action: 'goRentMyPaymentPage', url: paymentURL }, '*');

                    } else {
                      window.open(paymentURL, '_self');
                    }
                  }
                }

              }, 1500);

              
            } else { // order not success
              let errorMessage = '';
              let placeOrderBtn = document.querySelector('[PlaceOrderBtn]');
              if(placeOrderBtn && placeOrderBtn.getAttribute('errorMessage')){
                errorMessage = placeOrderBtn.getAttribute('errorMessage');
              }
              Toaster().error(errorMessage || response.data?.result?.error)
            }
        }
      })
      .catch(error => {
        console.log({error});
      })



    },
    onClickPlaceQuoteOrder_forWishList(event) {

    
      if(!wishlistStore.listof_wishlist?.wish_list_items?.length){
        Toaster().error('Your Wish List is Empty');
        return;
      }  

      if(!this.isValidateAll()){
        Toaster().warning('Please complete all required fields'); 
        return;
      }  
      
      const payload = {
        type : 2,
        // order_source : "WP Plugin",
        "order_source": "Online",
        ...this.common,
        ...this.billingForm,
        ...(this.isActivePickup() ? {} : this.shippingForm),
        quote: true,
        token: wishlistStore.wishlist_token,
        additional_charges: checkoutStore.additionalCharges?.map(charge => {
            return {
                id: charge.id,
                value: (charge.is_required ? charge.fee?.amounts[0] : charge?.existing?.amount) || ''
            }
        }),
        custom_checkout: {
          fields: this.customFields.map(field => {
            return { 
              [field.field_name]: this.custom_fields[field.field_name] || '' 
            }
          })
        },
        custom_values: this.customFields.map(field => {
          return {
              id : field.id || '',
              field_label: field.field_label || '',
              field_name : field.field_name || '',
              field_values : this.custom_fields[field.field_name] || '',
              type : field.field_type || '',
            }
        })
      };



      if(globalLoader.isShowing()) return;      
      globalLoader.show();
      http.post('/quote/from-wishlist', payload).then(async response => {
          globalLoader.hide();
          if(response.data?.status == 'OK'){
            const data = response.data?.result?.data;
            if(data.order.success){  
           

              let order_data = data?.order;
              let order_uid = order_data?.data?.uid
              let order_id = data.payment?.order_id;
              localStorage.setItem('last_order_uid', order_uid);

              localStorage.removeItem('token');
              localStorage.removeItem('user_cart');
              removeOnlineStoreCartDate();

              /* -------------------------------- */
              /*          Toater message          */
              /* -------------------------------- */
              let toasterMessage = RentMyEvent.apply_filters('cdn:order_successfull_message', 'Your order has been placed successful!');
              let placeOrderBtn = document.querySelector('[PlaceOrderBtn]');
              if(placeOrderBtn && placeOrderBtn.getAttribute('successMessage')){
                toasterMessage = placeOrderBtn.getAttribute('successMessage');
                if(toasterMessage) Toaster().success(toasterMessage);
              }
              /* ----------------------- End Toater Messaage ---------------------- */
                
              setTimeout(() => {
                if(RENTMY_GLOBAL?.using_in_cli_project || RENTMY_GLOBAL?.afterOrder?.justEmit){
                  RentMyEvent.emit('cdn:checkout_page:order_completed', {
                    paymentURL, 
                    uid: order_uid,
                    is_quote: true,
                  })
                } else {
                  helper.redirect('order_complete');  
                }

              }, 1500);

              
            } else { // order not success
              let errorMessage = '';
              let placeOrderBtn = document.querySelector('[PlaceOrderBtn]');
              if(placeOrderBtn && placeOrderBtn.getAttribute('errorMessage')){
                errorMessage = placeOrderBtn.getAttribute('errorMessage');
              }
              Toaster().error(errorMessage || response.data?.result?.error)
            }
        }
      })
      .catch(error => {
        console.log({error});
      })



    },
    /**
     * return ['in-store', 'delivery', 'shipping']
     */
    isExluded(type) {
      let arr = this.cartData?.excluded_fulfilment_type || [];
      return arr.includes(type);
    },
    isActivePickup() {
      return globalStore.deliverySettings?.instore_pickup && !this.isExluded('in-store');   
    },
    isActiveShipping() {
      return globalStore.deliverySettings?.shipping && !this.isExluded('shipping');  
    },
    isActiveDelivery: function(){
      return globalStore.deliverySettings?.delivery && !this.isExluded('delivery');  
    },    
    initSignature: function(){
      setTimeout(() => {
        let canvas = wrapper.querySelector('canvas');
        if(canvas){
          this.signaturePad = new SignaturePad(canvas);
        }
      }, 100);    
    }, 
    signatureUndo() {
      const data = this.signaturePad.toData();
      if (data) {
        data.pop(); // remove the last dot or line
        this.signaturePad.fromData(data);
      }
    }, 
    delay: function(callback, time=0){
      setTimeout(callback, time);
    },
    onClickTermsAndConditions: function(event){

      emitter.on('when-accepted-all-terms', (status) => {
        this.accepted_term_and_conditions = status;
      })

      // Signature Page
      if(this.site_specific?.confg?.signature?.online){
        this.show_signature_pad = true;     
        if(!this.signaturePad){
          this.initSignature();
        }   
      }

      // Terms and conditions
      let content = checkoutStore.termAndConditions?.contents?.content || ''; 

      const change = (bool, time=0)=> {
        setTimeout(() => {
          this.accepted_term_and_conditions = bool;
        }, time);
      }

      change(!this.accepted_term_and_conditions, 100);

      if(content){
        let dom = this.helper.domParser(content);
        let selector = 'input.rentmy_checkbox[name=rentmy_checkbox][type=checkbox]';
        let checkboxes = Array.from(dom?.querySelectorAll(selector));
        if(checkboxes?.length){
          this.hasCheckboxes = true;
          let isCheckedAll = checkboxes.every(el => el.checked === true);
          if(isCheckedAll){
            change(true, 20);
          } else {
            this.showModal = true;
            change(false, 20);
          }
        }
      } else {
        change(true, 20);
      } 
    }, 
    /* -------------------------------------------------------------------------- */
    /*                               With Validation                              */
    /* -------------------------------------------------------------------------- */
    isEmpty: function(object){
      return Object.keys(object)?.length === 0;
    },
    checkBillingValidation: function(key=''){
      const config = this.site_specific?.confg?.checkout;
      // key address = city/state/zipcode/country
      if(config.billing.address.show && config.billing.address.is_required){
        config.billing.city = {show: true, is_required: true};
        config.billing.state = {show: true, is_required: true};
        config.billing.state = {show: true, is_required: true};
        config.billing.zipcode = {show: true, is_required: true};
        config.billing.country = {show: true, is_required: true};
        config.billing.address_line1 = {show: true, is_required: true};
      }
      
      for(const field_name in config.billing){
        let field;
        if(this.billingForm.hasOwnProperty(field_name)){
          if(key){
            // Checking specific field
            if(key === field_name){
              field = config.billing[field_name];
              if(field.show && field.is_required){
                let value = this.billingForm[field_name];
                if(!value){
                  this.errors.billingForm[field_name] = true;
                } else {
                  delete this.errors.billingForm[field_name];
                }
              }
            }
          } else {
            // Checking for all field
            field = config.billing[field_name];
            if(field.show && field.is_required){            
              let value = this.billingForm[field_name];
              if(!value){
                this.errors.billingForm[field_name] = true;
              } else {
                delete this.errors.billingForm[field_name];
              }
            }
          }
        }
      }

      delete this.errors.billingForm.address;
    },
    checkShippingValidation: function(key=''){
      const config = this.site_specific?.confg?.checkout;

      // key address = city/state/zipcode/country
      if(config.fulfillment.address.show && config.fulfillment.address.is_required){
        config.fulfillment.city = {show: true, is_required: true};
        config.fulfillment.state = {show: true, is_required: true};
        config.fulfillment.state = {show: true, is_required: true};
        config.fulfillment.zipcode = {show: true, is_required: true};
        config.fulfillment.country = {show: true, is_required: true};
        config.fulfillment.address1 = {show: true, is_required: true};
      }
      config.fulfillment.mobile = {show: true, is_required: true};

      let clonedConfig = JSON.parse(JSON.stringify(config.fulfillment));
      
      // Set 'shipping_' for each key in config.fulfillment
      Object.entries(clonedConfig).forEach(entry => {
        let [ key, value ] = entry;    
        if(!key.startsWith('shipping_')){
          config.fulfillment[`shipping_${key}`] = clonedConfig[key];
        }
      })
      
      for(const field_name in config.fulfillment){
        let field;
        if(this.shippingForm.hasOwnProperty(field_name)){
          if(key){
            // Checking specific field
            if(key === field_name){
              field = config.fulfillment[field_name];
              if(field.show && field.is_required){
                let value = this.shippingForm[field_name];
                if(!value){
                  this.errors.shippingForm[field_name] = true;
                } else {
                  delete this.errors.shippingForm[field_name];
                }
              }
            }
          } else {
            // Checking for all field 
            this.shipping_method = sessionStorage.getItem('online_fullfilment_option');
            field = config.fulfillment[field_name];            
            if(field.show && field.is_required){            
              let value = this.shippingForm[field_name];              
              if(!value){
                this.errors.shippingForm[field_name] = true;
              } else {
                delete this.errors.shippingForm[field_name];
              }
              if(this.shipping_method === 'pickup'){
                if(this.common.pickup) delete this.errors.shippingForm['pickup_location_id'];
                else this.errors.shippingForm['pickup_location_id'] = true;
              }
              if(this.shipping_method !== 'billing' && this.shipping_method !== 'shipping'){
                delete this.errors.shippingForm[field_name];
              }
            }            
          }
          delete this.errors.shippingForm.address;
        }
      }

      if(this.shippingForm.shipping_email){
        if(!this.isValidEmail(this.shippingForm.shipping_email)){
          this.shippingForm.shipping_email = ''
          Toaster().error("Email address is invalid");
        } 
      }
    },
    checkCustomFieldsValidation: function(key=''){
      let customFields = checkoutStore.customFields;
      if(customFields?.length){
        const fields = checkoutStore.customFields.filter(field => [0, 1, 2].includes(+field?.field_type) );
        fields.forEach(field => {  
          const { field_name } = field;
          const value = this.custom_fields[field_name];
          if(key){
            if(key === field_name){
              if(field.field_is_required && !value){
                this.errors.customFields[field_name] = true;
              } else {
                delete this.errors.customFields[field_name];
              }
            }
          } else {
            if(field.field_is_required && !value){
              this.errors.customFields[field_name] = true;
            } else {
              delete this.errors.customFields[field_name];
            }
          }        
        });        
      }      
    },
    isValidateAll(){
      this.checkBillingValidation();
      this.checkCustomFieldsValidation();
      if(!this.common.address_id){
        this.checkShippingValidation();
      }else{
        this.errors.shippingForm = {};
      }
      const { billingForm, shippingForm, customFields } = this.errors;
      if(this.isEmpty(billingForm) && this.isEmpty(shippingForm) && this.isEmpty(customFields)){
        return true
      } else {
        return false;
      }
    },
    backToWishList(){
      wishlistStore.goToWishListPage()
    },
  },
});
</script>

<template>
  <teleport :to="wrapper">
    <checkoutComponent></checkoutComponent>
  </teleport>

  <CustomerLoginModal v-model="showLoginModal" :reloadAfterLogin="true"></CustomerLoginModal>
</template>