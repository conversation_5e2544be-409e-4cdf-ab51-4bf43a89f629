import { createComponentHTML } from "@utils/functions/withComponent";


export const GenerateCompoenentHtml = function(wrapper, allowDyanmicContent, printSelector){
    const site_specific = JSON.parse( localStorage.getItem("rentmy_contents") )?.site_specific;
    
    const filters = {
        additionaTitle: RentMyEvent.apply_filters('Checkout:AdditionalInformation:Title', site_specific?.checkout_info?.lbl_special_comments),
        specialCommentLabel: RentMyEvent.apply_filters('Checkout:SpecialComments:Label', site_specific?.checkout_info?.lbl_special_comments),
        specialRequestLabel: RentMyEvent.apply_filters('Checkout:SpecialRequest:Label', site_specific?.checkout_info?.lbl_special_request),
        drivingLicenceLabel: RentMyEvent.apply_filters('Checkout:DrivingLicence:Label', site_specific?.checkout_info?.lbl_driving_license),

        step_one: RentMyEvent.apply_filters('Checkout:Billing:label:step_one', site_specific.checkout_info.step_one),
        step_two: RentMyEvent.apply_filters('Checkout:Billing:label:step_two', site_specific.checkout_info.step_two),
        step_three: RentMyEvent.apply_filters('Checkout:Billing:label:step_three', site_specific.checkout_info.step_three),
        step_four: RentMyEvent.apply_filters('Checkout:Billing:label:step_four', site_specific.checkout_info.step_four),
        title: RentMyEvent.apply_filters('Checkout:Billing:label:title', site_specific.checkout_info.title),
        page_breadcrumb: RentMyEvent.apply_filters('Checkout:Billing:label:page_breadcrumb', site_specific.checkout_info.page_breadcrumb),
        title_contact: RentMyEvent.apply_filters('Checkout:Billing:label:title_contact', site_specific.checkout_info.title_contact),
        btn_back_to_cart: RentMyEvent.apply_filters('Checkout:Billing:label:btn_back_to_cart', site_specific.checkout_info.btn_back_to_cart),
        btn_back: RentMyEvent.apply_filters('Checkout:Billing:label:btn_back', site_specific.checkout_info.btn_back),
        lbl_first_name: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_first_name', site_specific.checkout_info.lbl_first_name),
        lbl_lastname: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_lastname', site_specific.checkout_info.lbl_lastname),
        lbl_mobile: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_mobile', site_specific.checkout_info.lbl_mobile),
        lbl_email: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_email', site_specific.checkout_info.lbl_email),
        title_billing: RentMyEvent.apply_filters('Checkout:Billing:label:title_billing', site_specific.checkout_info.title_billing),
        lbl_address_line_1: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_address_line_1', site_specific.checkout_info.lbl_address_line_1),
        lbl_address_line_2: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_address_line_2', site_specific.checkout_info.lbl_address_line_2),
        lbl_company_name: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_company_name', site_specific.checkout_info.lbl_company_name),
        lbl_city: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_city', site_specific.checkout_info.lbl_city),
        lbl_state: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_state', site_specific.checkout_info.lbl_state),
        lbl_zipcode: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_zipcode', site_specific.checkout_info.lbl_zipcode),
        lbl_country: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_country', site_specific.checkout_info.lbl_country),
        lbl_special_comments: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_special_comments', site_specific.checkout_info.lbl_special_comments),
        lbl_special_request: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_special_request', site_specific.checkout_info.lbl_special_request),
        title_shipping: RentMyEvent.apply_filters('Checkout:Billing:label:title_shipping', site_specific.checkout_info.title_shipping),
        title_pickup_option: RentMyEvent.apply_filters('Checkout:Billing:label:title_pickup_option', site_specific.checkout_info.title_pickup_option),
        title_delivery_option: RentMyEvent.apply_filters('Checkout:Billing:label:title_delivery_option', site_specific.checkout_info.title_delivery_option),
        title_shipping_option: RentMyEvent.apply_filters('Checkout:Billing:label:title_shipping_option', site_specific.checkout_info.title_shipping_option),
        lbl_shipping_name: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_shipping_name', site_specific.checkout_info.lbl_shipping_name),
        lbl_shipping_first_name: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_shipping_first_name', site_specific.checkout_info.lbl_shipping_first_name),
        lbl_shipping_last_name: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_shipping_last_name', site_specific.checkout_info.lbl_shipping_last_name),
        lbl_shipping_mobile: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_shipping_mobile', site_specific.checkout_info.lbl_shipping_mobile),
        lbl_shipping_address_line_1: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_shipping_address_line_1', site_specific.checkout_info.lbl_shipping_address_line_1),
        lbl_shipping_address_line_2: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_shipping_address_line_2', site_specific.checkout_info.lbl_shipping_address_line_2),
        lbl_shipping_city: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_shipping_city', site_specific.checkout_info.lbl_shipping_city),
        lbl_shipping_zipcode: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_shipping_zipcode', site_specific.checkout_info.lbl_shipping_zipcode),
        lbl_same_as_billing: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_same_as_billing', site_specific.checkout_info.lbl_same_as_billing),
        title_additional: RentMyEvent.apply_filters('Checkout:Billing:label:title_additional', site_specific.checkout_info.title_additional),
        title_custom_checkout: RentMyEvent.apply_filters('Checkout:Billing:label:title_custom_checkout', site_specific.checkout_info.title_custom_checkout),
        lbl_driving_license: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_driving_license', site_specific.checkout_info.lbl_driving_license),
        lbl_alt_mobile: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_alt_mobile', site_specific.checkout_info.lbl_alt_mobile),
        lbl_select_shipping: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_select_shipping', site_specific.checkout_info.lbl_select_shipping),
        lbl_select_location: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_select_location', site_specific.checkout_info.lbl_select_location),
        lbl_signature: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_signature', site_specific.checkout_info.lbl_signature),
        lbl_required: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_required', site_specific.checkout_info.lbl_required),
        lbl_clear: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_clear', site_specific.checkout_info.lbl_clear),
        lbl_undo: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_undo', site_specific.checkout_info.lbl_undo),
        terms_and_condition: RentMyEvent.apply_filters('Checkout:Billing:label:terms_and_condition', site_specific.checkout_info.terms_and_condition),
        terms_and_condition_link_label: RentMyEvent.apply_filters('Checkout:Billing:label:terms_and_condition_link_label', site_specific.checkout_info.terms_and_condition_link_label),
        btn_continue: RentMyEvent.apply_filters('Checkout:Billing:label:btn_continue', site_specific.checkout_info.btn_continue),
        btn_get_delivery_cost: RentMyEvent.apply_filters('Checkout:Billing:label:btn_get_delivery_cost', site_specific.checkout_info.btn_get_delivery_cost),
        btn_get_shipping_method: RentMyEvent.apply_filters('Checkout:Billing:label:btn_get_shipping_method', site_specific.checkout_info.btn_get_shipping_method),
        title_order_summary: RentMyEvent.apply_filters('Checkout:Billing:label:title_order_summary', site_specific.checkout_info.title_order_summary),
        title_select_shipping_method: RentMyEvent.apply_filters('Checkout:Billing:label:title_select_shipping_method', site_specific.checkout_info.title_select_shipping_method),
        btn_quote_accept: RentMyEvent.apply_filters('Checkout:Billing:label:btn_quote_accept', site_specific.checkout_info.btn_quote_accept),
        lbl_welcome_to_login: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_welcome_to_login', site_specific.checkout_info.lbl_welcome_to_login),
        lbl_delivery_cost: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_delivery_cost', site_specific.checkout_info.lbl_delivery_cost),
        lbl_google_pay_success: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_google_pay_success', site_specific.checkout_info.lbl_google_pay_success),
        lbl_checkout_text: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_checkout_text', site_specific.checkout_info.lbl_checkout_text),
        lbl_global_checkout_error_msg: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_global_checkout_error_msg', site_specific.checkout_info.lbl_global_checkout_error_msg),
        lbl_customer_name: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_customer_name', site_specific.checkout_info.lbl_customer_name),
        lbl_billing_address: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_billing_address', site_specific.checkout_info.lbl_billing_address),
        lbl_status: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_status', site_specific.checkout_info.lbl_status),
        lbl_address: RentMyEvent.apply_filters('Checkout:Billing:label:lbl_address', site_specific.checkout_info.lbl_address), 
        
        // with checkout payment
        placeOrder_buttonLabel: RentMyEvent.apply_filters('Checkout:payment:placeOrderButton:label', site_specific.checkout_payment.btn_place_order), 
        btn_back_to_cart: RentMyEvent.apply_filters('Checkout:payment:backToCartButton:label', site_specific.checkout_payment.btn_back_to_cart), 
        
        lbl_shipping: RentMyEvent.apply_filters('Checkout:cart:lbl_shipping:label', site_specific.cart.lbl_shipping), 
        lbl_delivery_tax: RentMyEvent.apply_filters('Checkout:cart:lbl_deliveryTax:label', site_specific.cart.lbl_delivery_tax), 
        
    }
    
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:checkout_page', [         
        /* -------------------------------------------------------------------------- */
        /*                                 Popup Login                                */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[SignInPopupArea]",
            template: true,
            attr: { 'v-if': '!isAuthenticated && is_show_signin' },
        },   
        {
            selector: "[SignInPopupArea] [StoreName]", 
            text: `{{ customer_guest_checkout ? urlToLabel(store_name) : 'There as customer has to log in first' }}`,
        },   
        {
            selector: "[SignInPopupArea] [SignIn]",
            attr: { '@click.prevent': 'showLoginModal=true' },
        }, 
        {
            selector: "[SignInPopupArea] [WelcomeText]",
            attr: {
                'v-if': 'customer_guest_checkout'
            },
            text: `{{ allowDyanmicContent ? site_specific?.checkout_info?.txt_welcome_to : '__EXISTING_HTML__' }}`
        }, 
        {
            selector: "[SignInPopupArea] [SignIn]",
            text: `{{ allowDyanmicContent ? site_specific?.checkout_info?.txt_sign_in : '__EXISTING_HTML__' }}`
        }, 
        // End

        /* -------------------------------------------------------------------------- */
        /*                               Billing Border                                 */
        /* -------------------------------------------------------------------------- */
        
        // first_name
        {
            selector: "[BillingBorder]",
            attr: { ':class': '{BillingDetailsErrorBorder : !isEmpty(errors.billingForm) || !isEmpty(errors.customFields)}' },
        },
        {
            selector: ".BillingCheckoutTitle",
            text: `{{ allowDyanmicContent ? site_specific?.checkout_info?.title_billing : '__EXISTING_HTML__' }}`
        }, 
        /* -------------------------------------------------------------------------- */
        /*                               Billing Form                                 */
        /* -------------------------------------------------------------------------- */
        
        // first_name
        {
            selector: "[BillingGeneralInfo] [FirstNameDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.billing?.first_name?.show' },
        }, 
        {
            selector: "[BillingGeneralInfo] [FirstNameDiv] label",
            text: ` ${allowDyanmicContent ? filters.lbl_first_name: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.billing?.first_name?.is_required">*</sup>`
        }, 
        {
            selector: "[BillingGeneralInfo] [FirstNameDiv] [FirstName]",
            attr: { 
                'v-model': 'billingForm.first_name', 
                '@change' : 'shippingFormMakeSameAsBilling(sameAsAbove)', 
                '@focusout': 'checkBillingValidation("first_name")',
                ':required': 'errors.billingForm?.first_name',
            },
        }, 

        // last_name
        {
            selector: "[BillingGeneralInfo] [LastNameDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.billing?.last_name?.show' },
        }, 
        {
            selector: "[BillingGeneralInfo] [LastNameDiv] label",
            text: ` ${allowDyanmicContent ? filters.lbl_lastname: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.billing?.last_name?.is_required">*</sup>`
        }, 
        {
            selector: "[BillingGeneralInfo] [LastNameDiv] [LastName]",
            attr: { 
                'v-model': 'billingForm.last_name', 
                '@change' : 'shippingFormMakeSameAsBilling(sameAsAbove)', 
                '@focusout': 'checkBillingValidation("last_name")',
                ':required': 'errors.billingForm?.last_name',
            },
        },  

        // mobile
        {
            selector: "[BillingGeneralInfo] [MobileDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.billing?.mobile?.show' },
        }, 
        {
            selector: "[BillingGeneralInfo] [MobileDiv] label",
            text: ` ${allowDyanmicContent ? filters.lbl_mobile: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.billing?.mobile?.is_required">*</sup>`
        }, 
        {
            selector: "[BillingGeneralInfo] [MobileDiv] [Mobile]",
            attr: { 
                'v-model': 'billingForm.mobile', '@change' : 'shippingFormMakeSameAsBilling(sameAsAbove)', 
                '@focusout': 'checkBillingValidation("mobile")',
                ':required': 'errors.billingForm?.mobile',
            },
        }, 

        // email
        {
            selector: "[BillingGeneralInfo] [EmailDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.billing?.email?.show' },
        }, 
        {
            selector: "[BillingGeneralInfo] [EmailDiv] label",
            text: ` ${allowDyanmicContent ? filters.lbl_email: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.billing?.email?.is_required">*</sup>`
        }, 
        {
            selector: "[BillingGeneralInfo] [EmailDiv] [Email]",
            attr: { 
                'v-model': 'billingForm.email', 
                '@change' : 'shippingFormMakeSameAsBilling(sameAsAbove)', 
                '@focusout': 'checkBillingValidation("email")',
                ':required': 'errors.billingForm?.email',
                ':disabled': 'isAuthenticated',
            },
        }, 

        // company              
        {
            selector: "[BillingGeneralInfo] [CompanyDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.billing?.company?.show' },
        }, 
        {
            selector: "[BillingGeneralInfo] [CompanyDiv] label",
            text: `${allowDyanmicContent ? filters.lbl_company_name: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.billing?.company?.is_required">*</sup>`
        }, 
        {
            selector: "[BillingGeneralInfo] [Company]",
            attr: { 
                'v-model': 'billingForm.company', 
                '@change' : 'shippingFormMakeSameAsBilling(sameAsAbove)', 
                '@focusout': 'checkBillingValidation("company")',
                ':required': 'errors.billingForm?.company',
            },
        },

        // country
        {
            selector: "[BillingGeneralInfo] select[Country]",
            attr: { 
                'v-model': 'billingForm.country', 
                '@change': `(e) => {
                    onChangeCountry("billing");
                    shippingFormMakeSameAsBilling(sameAsAbove);
                    initAutocomplete($refs.billing_addressLine1, {countryCode: e.target.value}); 
                }`,
                '@focusout': 'checkBillingValidation("country")',  // Fixed closing quote here
                ':required': 'errors.billingForm?.country',
            },
            
        }, 
        {
            selector: "[BillingGeneralInfo] select[Country] option",
            attr: { 'v-for': '(country, index) in countries', ':key': 'index', ':value': 'country.code' },
            text: `{{ country.name }}`,
        },
        {
            selector: "[BillingGeneralInfo] [CountryLabel]",
            text: `{{ allowDyanmicContent ? site_specific?.checkout_info?.lbl_country : '__EXISTING_HTML__' }}`
        },
        
        // address line 1
        {
            selector: "[BillingGeneralInfo] [AddressLine1Div]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.billing?.address?.show' },
        },
        {
            selector: "[BillingGeneralInfo] [AddressLine1Div] label",
            text: `${allowDyanmicContent ? filters.lbl_address_line_1: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.billing?.address?.is_required">*</sup>`,
        },
        {
            selector: "[BillingGeneralInfo] [AddressLine1]",
            attr: { 
                'v-model': 'billingForm.address_line1', 
                '@click' : 'tyipingForm = "billingForm"', 
                '@focusout': 'checkBillingValidation("address_line1")',
                ':required': 'errors.billingForm?.address_line1',
                'ref': 'billing_addressLine1',
            },
        }, 

        // address line 2
        {
            selector: "[BillingGeneralInfo] [AddressLine2Div]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.billing?.address?.show' },
        },
        {
            selector: "[BillingGeneralInfo] [AddressLine2Div] label",
            text: `${allowDyanmicContent ? filters.lbl_address_line_2: '__EXISTING_HTML__'}`
        },
        {
            selector: "[BillingGeneralInfo] [AddressLine2]",
            attr: { 'v-model': 'billingForm.address_line2', '@change' : 'shippingFormMakeSameAsBilling(sameAsAbove)' },
        },

        // city
        {
            selector: "[BillingGeneralInfo] [CityDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.billing?.address?.show' },
        }, 
        {
            selector: "[BillingGeneralInfo] [CityDiv] label",
            text: `${allowDyanmicContent ? filters.lbl_city: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.billing?.address?.is_required">*</sup>`
        },
        {
            selector: "[BillingGeneralInfo] [City]",
            attr: { 
                'v-model': 'billingForm.city', 
                '@change' : 'shippingFormMakeSameAsBilling(sameAsAbove)', 
                '@focusout': 'checkBillingValidation("city")',
                ':required': 'errors.billingForm?.city',
            },
        }, 

        // state
        {
            selector: "[BillingGeneralInfo] [StateDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.billing?.address?.show' },
        }, 
        {
            selector: "[BillingGeneralInfo] [StateDiv] label",
            text: `${allowDyanmicContent ? filters.lbl_state: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.billing?.address?.is_required">*</sup>`
        },
        {
            selector: "[BillingGeneralInfo] [State]",
            attr: { 
                'v-model': 'billingForm.state', 
                '@change' : 'shippingFormMakeSameAsBilling(sameAsAbove)', 
                '@focusout': 'checkBillingValidation("state")',
                ':required': 'errors.billingForm?.state',
            },
        }, 

        // zipcode
        {
            selector: "[BillingGeneralInfo] [ZipCodeDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.billing?.address?.show' },
        }, 
        {
            selector: "[BillingGeneralInfo] [ZipCodeDiv] label",
            text: `${allowDyanmicContent ? filters.lbl_zipcode: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.billing?.address?.is_required">*</sup>`
        },
        {
            selector: "[BillingGeneralInfo] [ZipCode]",
            attr: { 
                'v-model': 'billingForm.zipcode', 
                '@change' : 'shippingFormMakeSameAsBilling(sameAsAbove)', 
                '@focusout': 'checkBillingValidation("zipcode")',
                ':required': 'errors.billingForm?.zipcode',
            },
        }, 
        /* -------------------------------------------------------------------------- */
        /*                             With Save Locations                            */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[AllSavedBililingAddress]",
            attr: { 'v-if': 'isAuthenticated' },
        }, 
        {
            selector: "[AllSavedBililingAddress] [SingleAddress]",
            attr: { 
                'v-for': '(addr, index) in checkoutStore.primaryAddressList', ':key': 'index', 
                '@click': 'onChangeBillingAddress(addr)' ,
                ':id': '"address_" + addr?.id', 
            },
            text: `
                {{addr?.full_address}}
                <input type="radio" name="select_address" value="rent" :checked="addr?.id == common.address_id" :id='"address_" + addr?.id'> 
                <span></span>
            `,
        }, 
        {
            selector: "[AllSavedBililingAddress] [CreateAddress]",
            attr: { 
                'v-if': 'checkoutStore.primaryAddressList?.length', 
                '@click': 'onClickNewBillingAddress()' 
            },
            text: `
                Create New
                <input type="radio" name="select_address" value="rent" :checked="!common.address_id"> 
                <span></span>
            `,
        }, 
        /* -------------------------------------------------------------------------- */
        /*                           showAdditionalField                              */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[BillingAdditionalInfo]",
            attr: { 'v-if': 'showAdditionalField' },
        }, 
        {
            selector: "[BillingAdditionalInfo] [Title]",
            text: `${filters.additionaTitle}`,
        }, 
        {
            selector: "[BillingAdditionalInfo] [SpecialComments] input",
            attr: { 'v-model': 'common.special_instructions' },
        }, 
        {
            selector: "[BillingAdditionalInfo] [SpecialComments] label",
            text: `${filters.specialCommentLabel}`,
        }, 
        {
            selector: "[BillingAdditionalInfo] [SpecialRequest] input",
            attr: { 'v-model': 'common.special_requests' },
        }, 
        {
            selector: "[BillingAdditionalInfo] [SpecialRequest] label",
            text: `${filters.specialRequestLabel}`,
        }, 
        {
            selector: "[BillingAdditionalInfo] [DrivingLicence] input",
            attr: { 'v-model': 'common.driving_license' },
        }, 
        {
            selector: "[BillingAdditionalInfo] [DrivingLicence] label",
            text: `${filters.drivingLicenceLabel}`,
        }, 

        /* -------------------------------------------------------------------------- */
        /*                       Custom Checkout Information                          */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[BillingCustomCheckoutInfo]",
            attr: { 'v-if': 'customFields?.length' },
            child: [
                {
                    selector: '[title_custom_checkout]',
                    text: `${allowDyanmicContent ? filters.title_custom_checkout: '__EXISTING_HTML__'}`
                }
            ]
        }, 
        {
            selector: "[CustomField]",
            template: true,
            attr: { 'v-for': '(field, index) in customFields', ':key': 'index' },
            text: `
                <template v-if="field.field_type == 0">
                    <label for="file">{{ field.field_label }} <sup v-if="field.field_is_required">*</sup></label>
                    <input type="text" class="RentMyInputField" v-model="custom_fields[field.field_name]" 
                    v-on:focusout="checkCustomFieldsValidation(field.field_name)"
                    :required="errors.customFields?.[field.field_name]"
                    >
                    <small v-if="field.input_hints">{{ field.input_hints }}</small>           
                </template>
                <template v-else-if="field.field_type == 1">
                    <label for="file">{{ field.field_label }} <sup v-if="field.field_is_required">*</sup></label>
                    <select class="RentMyInputField" v-model="custom_fields[field.field_name]"
                    v-on:focusout="checkCustomFieldsValidation(field.field_name)"
                    :required="errors.customFields?.[field.field_name]"
                    >
                        <option :value="null">--Select--</option>
                        <option v-for="(value, i) in field?.field_values.split(';')" :value="value"> {{ value }} </option>
                    </select> 
                    <small v-if="field.input_hints">{{ field.input_hints }}</small>       
                </template>
                <template v-else-if="field.field_type == 2">
                    <label for="file">{{ field.field_label }} <sup v-if="field.field_is_required">*</sup></label> 
                    <div class="InputGroup InputFileGroup" :class="{ 'RentMyUploadFieldError' :errors.customFields?.[field.field_name]}">                  
                        <input type="file" class="RentMyInputField" v-on:change="onChangeUploadFile($event, field.field_name)"
                        v-on:click="checkCustomFieldsValidation(field.field_name)" 
                        >
                        <div class="IconAndFileName">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span FileName>{{ custom_fields?.[field.field_name] }}</span>
                        </div>
                    </div>
                    <small v-if="field.input_hints">{{ field.input_hints }}</small>  
                </template>
            `,
        }, 
        
        /* -------------------------------------------------------------------------- */
        /*                            Fullfillment Border                               */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[FullfillmentBorder]",
            attr: { ':class': '{BillingDetailsErrorBorder : !isEmpty(errors.shippingForm)}' },
        },

        /* -------------------------------------------------------------------------- */
        /*                            With Fullfillment                               */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[FullfillmentArea]",
            attr: { 
                'v-if': '[false, undefined].includes(site_specific?.confg?.checkout?.hide_fulfillment)', 
                ':class': '{BillingDetailsErrorBorder : !isEmpty(errors.shippingForm)}' 
            },
        },
        {
            selector: "[FullfillmentArea] [Title]",
            text: `${allowDyanmicContent ? filters.title_shipping: '__EXISTING_HTML__'}`
        },
        
        /* -------------------------------------------------------------------------- */
        /*                            Shipping Tab section                            */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[FullfillmentArea] [PickupTab]",
            attr: { 'v-if': 'isActivePickup()' },
            text: `
                <a href="#Pickup" :class="{TabActive: !shipping_method || shipping_method == 'pickup'}" @click.prevent="onClickShippingTab('pickup')">
                    <div class="fullfilment-btn">
                        <img :src="RENTMY_GLOBAL?.images?.pickup_image" alt="" class="icon" />
                        <img :src="RENTMY_GLOBAL?.images?.pickup_white_image" alt="" class="icon-active" />
                    </div>
                    <h5>${allowDyanmicContent ? filters.title_pickup_option: 'Pickup'}</h5>
                </a>
            `,
        },
        {
            selector: "[FullfillmentArea] [ShippingTab]",
            attr: { 'v-if': 'isActiveShipping()' },
            text: `
                <a href="#Pickup" :class="{TabActive: shipping_method == 'shipping'}" @click.prevent="onClickShippingTab('shipping')">
                    <div class="fullfilment-btn">
                        <img :src="RENTMY_GLOBAL?.images?.shipping_image" alt="" class="icon" />
                        <img :src="RENTMY_GLOBAL?.images?.shipping_white_image" alt="" class="icon-active" />
                    </div>
                    <h5>${allowDyanmicContent ? filters.title_shipping_option: 'Shipping'}</h5>
                </a>
            `,
        },
        {
            selector: "[FullfillmentArea] [DeliveryTab]",
            attr: { 'v-if': 'isActiveDelivery()' },
            text: `
                <a href="#Pickup" :class="{TabActive: shipping_method == 'delivery'}" @click.prevent="onClickShippingTab('delivery')">
                    <div class="fullfilment-btn">
                        <img :src="RENTMY_GLOBAL?.images?.delivery_image" alt="" class="icon" />
                        <img :src="RENTMY_GLOBAL?.images?.delivery_white_image" alt="" class="icon-active" />
                    </div>
                     <h5>${allowDyanmicContent ? filters.title_delivery_option: 'Delivery'}</h5>
                </a>
            `,
        },

        /* -------------------------------------------------------------------------- */
        /*                                 tab: Pickup Locations                      */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[FullfillmentArea] [PickupLocations]",
            attr: { 'v-if': 'isActivePickup() && globalStore.locationList?.length && (!shipping_method || shipping_method=="pickup")' },
        },
        {
            selector: "[FullfillmentArea] [PickupLocations] [Location]",
            attr: { 'v-for': '(location, index) in globalStore.locationList', ':key' : 'index' },
            text: `
                <label class="RentMyRadio">
                    <input type="radio" name="RentalType" value="rent" :checked="location.id == this.common.pickup" @click="onClickPickupAddress(location)"> 
                      <b>{{ location.name }}</b> ({{ location.location }})
                    <span></span>
                </label>
            `,
        },

        /* -------------------------------------------------------------------------- */
        /*                        Shipping And Delivery Form                          */
        /* -------------------------------------------------------------------------- */
        
        // Check Admin Config to display shipping and deliver
        {
            selector: "[ShipAndDelivery]",
            attr: { 'v-if': 'isActiveShipping() || isActiveDelivery()' },
        }, 
        // sameAsAbove
        {
            selector: "[ShipAndDelivery] [SameAsAboveArea]",
            attr: { 'v-if': '["shipping", "delivery"].includes(shipping_method)' },
        },
        ...printSelector({
            selector: "[ShipAndDelivery] [SameAsAboveArea] .RentMyCheckbox",
            target: { prev_tag: 'input', next_tag: 'span' },
            text: `${filters.lbl_same_as_billing}`
        }),
        {
            // selector: "[ShipAndDelivery] [SameAsAbove]",
            selector: "[ShipAndDelivery] label",
            attr: { 
                ':checked': 'sameAsAbove', 
                '@click.stop.prevent' : `()=>{
                    sameAsAbove = !sameAsAbove; 
                    tyipingForm = "shippingForm";
                    showShippingForm = !sameAsAbove;

                    shippingFormMakeSameAsBilling(true);

                    if(sameAsAbove == false){
                        delay(()=>initAutocomplete($refs.shipping_addressLine1, {countryCode: shippingForm.shipping_country}), 200);
                    }
                }`,                
            },
            child: [
                {
                    selector: 'input',
                    attr: {
                        ':checked': `sameAsAbove`,
                        ':checked2': `sameAsAbove`,
                    }
                }
            ]
        }, 

        /* -------------------------------------------- */
        /*               Shipping Address List          */
        /* -------------------------------------------- */

        // --------- address list
        {
            selector: "[ShipAndDelivery] [ShippingAddressList]",
            attr: { 'v-if': 'isAuthenticated && checkoutStore.shippingAddressList?.length && !isActivePickup()' },
        }, 
        {
            selector: "[ShipAndDelivery] [ShippingAddressList] [Address]",
            attr: { 'v-for': '(address, index) in checkoutStore.shippingAddressList', ':key' : 'index' },
            text: `
                <label class="RentMyRadio">
                    <input type="radio" name="RentalType" value="rent" v-on:click="onClickShippingAddress(address)" :checked="address.id == selected_shipping_address?.id"> 
                        {{ address.full_address }}
                    <span></span>
                </label>
            `,
        }, 
        {
            selector: "[ShipAndDelivery] [ShippingAddressList] [CreateNew]",
            attr: { 'v-if': 'shipping_method != "pickup"' },
        }, 
        {
            selector: "[ShipAndDelivery] [ShippingAddressList] [CreateNew] input",
            attr: { '@click': 'onClickShippingCreateNew()', ':checked': 'selected_shipping_address === null && shipping_method != "pickup"' },
        }, 
        

        // Form
        {
            selector: "[ShipAndDelivery] form",
            // attr: { '@submit.prevent': 'false', 'v-if': '!sameAsAbove && showShippingForm' },
            attr: { '@submit.prevent': 'false', 'v-if': '["shipping", "delivery"].includes(shipping_method)' },
        }, 
        
        // first_name
        {
            selector: "[ShipAndDelivery] [FirstNameDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.fulfillment?.first_name?.show' },
        }, 
        {
            selector: "[ShipAndDelivery] [FirstNameDiv] label",
            text: ` ${allowDyanmicContent ? filters.lbl_shipping_first_name: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.fulfillment?.first_name?.is_required">*</sup>`
        }, 
        {
            selector: "[ShipAndDelivery] [FirstNameDiv] [FirstName]",
            attr: { 
                'v-model': 'shippingForm.shipping_first_name', 
                '@focusout': 'checkShippingValidation("shipping_first_name")',
                ':required': 'errors.shippingForm?.shipping_first_name',
            },
        }, 

        // last_name
        {
            selector: "[ShipAndDelivery] [LastNameDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.fulfillment?.last_name?.show' },
        }, 
        {
            selector: "[ShipAndDelivery] [LastNameDiv] label",
            text: ` ${allowDyanmicContent ? filters.lbl_shipping_last_name: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.fulfillment?.last_name?.is_required">*</sup>`
        }, 
        {
            selector: "[ShipAndDelivery] [LastNameDiv] [LastName]",
            attr: { 
                'v-model': 'shippingForm.shipping_last_name', 
                '@focusout': 'checkShippingValidation("shipping_last_name")',
                ':required': 'errors.shippingForm?.shipping_last_name',
            },
        },  

        // mobile
        {
            selector: "[ShipAndDelivery] [MobileDiv] label",
            text: ` ${allowDyanmicContent ? filters.lbl_shipping_mobile: '__EXISTING_HTML__'} <sup>*</sup>`
        }, 
        {
            selector: "[ShipAndDelivery] [MobileDiv] [Mobile]",
            attr: { 
                'v-model': 'shippingForm.shipping_mobile', 
                '@focusout': 'checkShippingValidation("shipping_mobile")',
                ':required': 'errors.shippingForm?.shipping_mobile',
            },
        }, 

        // email
        {
            selector: "[ShipAndDelivery] [EmailDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.fulfillment?.email?.show' },
        }, 
        {
            selector: "[ShipAndDelivery] [EmailDiv] label",
            text: ` ${allowDyanmicContent ? filters.lbl_email: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.fulfillment?.email?.is_required">*</sup>`
        }, 
        {
            selector: "[ShipAndDelivery] [EmailDiv] [Email]",
            attr: { 
                'v-model': 'shippingForm.shipping_email', 
                '@focusout': 'checkShippingValidation("shipping_email")',
                ':required': 'errors.shippingForm?.shipping_email',
            },
        }, 

        // company
        {
            selector: "[ShipAndDelivery] [CompanyDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.fulfillment?.company?.show' },
        }, 
        {
            selector: "[ShipAndDelivery] [CompanyDiv] label",
            text: ` ${allowDyanmicContent ? filters.lbl_company_name: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.fulfillment?.company?.is_required">*</sup>`
        }, 
        {
            selector: "[ShipAndDelivery] [CompanyDiv] [Company]",
            attr: { 
                'v-model': 'shippingForm.shipping_company', 
                '@focusout': 'checkShippingValidation("shipping_company")',
                ':required': 'errors.shippingForm?.shipping_company',
            },
        }, 

        // country   
        {
            selector: "[ShipAndDelivery] [CountryDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.fulfillment?.address?.show' },
        }, 
        {
            selector: "[ShipAndDelivery] [CountryLabel]",
            text: ` ${allowDyanmicContent ? filters.lbl_country: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.fulfillment?.address?.is_required">*</sup>`
        }, 
        {
            selector: "[ShipAndDelivery] select[Country]",
            attr: { 
                'v-model': 'shippingForm.shipping_country', 
                '@change' : `(e)=>{
                    onChangeCountry("shipping");
                    initAutocomplete($refs.shipping_addressLine1, {countryCode: e.target.value}); 
                }`,
                '@focusout': 'checkShippingValidation("shipping_country")',
                ':required': 'errors.shippingForm?.shipping_country',
            },
        }, 
        {
            selector: "[ShipAndDelivery] select[Country] option",
            attr: { 'v-for': '(country, index) in countries', ':key': 'index', ':value': 'country.code' },
            text: `{{ country.name }}`,
        }, 

        // address line 1
        {
            selector: "[ShipAndDelivery] [AddressLine1Div]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.fulfillment?.address?.show' },
        }, 
        {        
            selector: "[ShipAndDelivery] [AddressLine1Div] label",
            text: `${allowDyanmicContent ? filters.lbl_shipping_address_line_1: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.fulfillment?.address?.is_required">*</sup>`
        }, 
        {
            selector: "[ShipAndDelivery] [AddressLine1]",
            attr: { 
                'v-model': 'shippingForm.shipping_address1', 
                '@click' : 'tyipingForm = "shippingForm"', 
                '@focusout': 'checkShippingValidation("shipping_address1")',
                ':required': 'errors.shippingForm?.shipping_address1',
                'ref': 'shipping_addressLine1',
            },
        },
        
        // address line 2
        {
            selector: "[ShipAndDelivery] [AddressLine2Div]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.fulfillment?.address?.show' },
        },
        {        
            selector: "[ShipAndDelivery] [AddressLine2Div] label",
            text: `${allowDyanmicContent ? filters.lbl_shipping_address_line_2: '__EXISTING_HTML__'}`
        },
        {
            selector: "[ShipAndDelivery] [AddressLine2]",
            attr: { 
                'v-model': 'shippingForm.shipping_address2', 
                '@focusout': 'checkShippingValidation("shipping_address2")',
                ':required': 'errors.shippingForm?.shipping_address2',
            },
        },

        // city
        {
            selector: "[ShipAndDelivery] [CityDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.fulfillment?.address?.show' },
        },
        {        
            selector: "[ShipAndDelivery] [CityDiv] label",
            text: `${allowDyanmicContent ? filters.lbl_shipping_city: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.fulfillment?.address?.is_required">*</sup>`
        }, 
        {
            selector: "[ShipAndDelivery] [City]",
            attr: { 
                'v-model': 'shippingForm.shipping_city', 
                '@focusout': 'checkShippingValidation("shipping_city")',
                ':required': 'errors.shippingForm?.shipping_city',
            },
        }, 

        // state
        {
            selector: "[ShipAndDelivery] [StateDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.fulfillment?.address?.show' },
        },
        {        
            selector: "[ShipAndDelivery] [StateDiv] label",
            text: `${allowDyanmicContent ? filters.lbl_state: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.fulfillment?.address?.is_required">*</sup>`
        }, 
        {
            selector: "[ShipAndDelivery] [State]",
            attr: { 
                'v-model': 'shippingForm.shipping_state', 
                '@focusout': 'checkShippingValidation("shipping_state")',
                ':required': 'errors.shippingForm?.shipping_state',
            },
        }, 

        // zipcode
        {
            selector: "[ShipAndDelivery] [ZipCodeDiv]",
            attr: { 'v-if': 'site_specific?.confg?.checkout?.fulfillment?.address?.show' },
        },
        {        
            selector: "[ShipAndDelivery] [ZipCodeDiv] label",
            text: `${allowDyanmicContent ? filters.lbl_shipping_zipcode: '__EXISTING_HTML__'} <sup v-if="site_specific?.confg?.checkout?.fulfillment?.address?.is_required">*</sup>`
        },
        {
            selector: "[ShipAndDelivery] [ZipCode]",
            attr: { 
                'v-model': 'shippingForm.shipping_zipcode', 
                '@focusout': 'checkShippingValidation("shipping_zipcode")',
                ':required': 'errors.shippingForm?.shipping_zipcode',
            },
        },
        
        
        /* -------------------------------------------------------------------------- */
        /*                              Shipping Methods                              */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[ShipAndDelivery] [ShippingMethods]",
            attr: { 
                'v-if': 'shipping_method == "shipping" && shipping_methods && !checkoutStore.isFreeShipping && !showWishListSummary',
                ':class': '{ShippingMethodsHighter: highlight_shipping_method && shipping_method == "shipping" && shipping_methods?.length}' 
            },
            child: [
                {
                    selector: "[AllMethods]",
                    text: `           
                        <template v-for="(method, key) in shipping_methods" :key="key">
                            <template v-if="Array.isArray(method)">
                                <template v-for="(cost, key2) in method" :key="key2">
                                  <template v-if="cost?.charge">
                                     <div class="PickupLocationList" Method>
                                           <label class="RentMyRadio">
                                               <input type="radio" name="shipping_methods" value="rent" v-on:click="onClickShppingMethodOrDeliveryCost(shipping_method === 'shipping' ? cost : method)" style="position: absolute !important;"> 
                                                   {{ cost?.service_name }}  
                                                   <b>{{ currency.format(cost?.charge || 0, currencyConfig) }}</b>                                     
                                               <span></span>
                                           </label>
                                     </div>
                                  </template>
                                </template>
                            </template>
                            <template v-else>
                                <div class="PickupLocationList" Method>
                                    <label class="RentMyRadio">
                                        <input type="radio" name="shipping_methods" value="rent" v-on:click="onClickShppingMethodOrDeliveryCost(method)"> 
                                            {{ method.carrier_code }}
                                            <b>{{ currency.format(method?.charge || 0, currencyConfig) }}</b>                             
                                        <span></span>
                                    </label>
                                </div>
                            </template>
                        </template>
                    `,
                },
            ]
        },        
        
        /* -------------------------------------------------------------------------- */
        /*                              Delivery Costs                              */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[ShipAndDelivery] [DeliveryCosts]",
            attr: { 'v-if': 'shipping_method == "delivery" && delivery_costs && !is_delivery_outside_of_area && !showWishListSummary' },
        },
        {
            selector: "[ShipAndDelivery] [DeliveryCosts] [Cost]",
            attr: { 'v-for': '(cost, key) in delivery_costs', ':key' : 'key' },
            text: `
            <template v-if="!isActiveDelivery()">
                <label v-if="!cost?.hideMe" class="RentMyRadio"  >
                      <input type="radio" name="delivery_cost" value="rent" :checked="cost?.selectMe" v-on:click="onClickShppingMethodOrDeliveryCost(cost)" v-on:dblclick="log(cost)"> 
                          {{ cost?.name || (is_delivery_by_zone === false ? 'Delivery Cost' : cost?.name) }}
                          <b>{{ currency.format(cost?.charge, currencyConfig) }}</b>
                        <span></span>
                </label>
            </template>
            <template v-else>
                <label><b>{{ currency.format(cost?.charge, currencyConfig) }}</b></label>
            </template>
            `,
        },


        /* -------------------------------------------------------------------------- */
        /*                         Get Shipping Mehtods Button                        */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[ShipAndDelivery] [GetShippingMethodsBtn]",
            attr: { 
                'v-if': 'shipping_method=="shipping" && !checkoutStore.isFreeShipping && !showWishListSummary',
                '@click': 'getShippingMethods()', 
            },
            text: `${filters.btn_get_shipping_method.toUpperCase()}`
        }, 
        {
            selector: "[ShipAndDelivery] [GetDeliveryCostBtn]",
            attr: { '@click': 'getDeliveryCosts()', 'v-if': 'shipping_method=="delivery" ' }, 
        }, 
        {
            selector: "[ShipAndDelivery] [GetDeliveryCostBtn]", 
            text: `${filters.btn_get_delivery_cost}`,
        }, 

        /* -------------------------------------------------------------------------- */
        /*                           Delivery Error Messages                          */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[ShipAndDelivery] [DeliveryOutsideAreaMsg]",
            attr: { 'v-if': 'is_delivery_outside_of_area && shipping_method=="delivery" && !showWishListSummary' },
        },
        {
            selector: "[ShipAndDelivery] [DeliveryAddressErrorMsg]",
            attr: { 'v-if': 'deliveryAddressErrorMsg && shipping_method=="delivery"' },
            text: `{{ deliveryAddressErrorMsg }}`,
        },

        /* -------------------------------------------------------------------------- */
        /* -------------------------------------------------------------------------- */
        /* -------------------------------------------------------------------------- */
        /* -------------------------------------------------------------------------- */
        /*                                Order Summary                               */
        /* -------------------------------------------------------------------------- */

        {
            selector: "[OrderSummary]",
            attr: { 
                'v-if': '!showWishListSummary', 
            }, 
        },
        // CheckoutDatetime
        {
            selector: "[OrderSummary] [CheckoutDatetime]",
            attr: { 
                'v-if': 'site_specific?.confg?.show_start_date || site_specific?.confg?.show_end_date',
                '@contextmenu': 'log(site_specific?.confg)'
            },
            text: `
            {{ (site_specific?.confg.show_start_date) ? datePipe(cartData?.rent_start, 'rental') : '' }}
            {{ (site_specific?.confg.show_start_date && site_specific?.confg.show_end_date && cartData?.rent_end) ? '-' : '' }}
            {{ (site_specific?.confg.show_end_date) ? datePipe(cartData?.rent_end, 'rental') : '' }}
            `,
        },

        // Cart items
        {
            selector: "[OrderSummary] .OrderReviewTitle",
            text: `${allowDyanmicContent ? filters.title_order_summary: '__EXISTING_HTML__'}`
        },
        {
            selector: "[OrderSummary] [CartItems]",
            attr: { 'v-if': 'cartData?.cart_items?.length' },
        },
        {
            selector: "[OrderSummary] [CartItems] [Item]",
            attr: { 'v-for': '(item, index) in cartData?.cart_items', ':key': 'index' },
        },
        {
            selector: "[OrderSummary] [CartItems] [Item] img",
            attr: { ':src': 'helper.getProductImage(item)' },
        },
        {
            selector: "[OrderSummary] [CartItems] [Item] [ProductName]",
            text: `{{ item?.product?.name }}`,
        },
        {
            selector: "[OrderSummary] [CartItems] [Item] [ProductQuantity]",
            attr: { 'v-for': '(order, index) in orders', ':key': 'index' },
            text: `{{ order }}`,
        },
        {
            selector: "[OrderSummary] [CartItems] [Item] [ProductVaraint]",
            attr: { 'v-if': 'item?.product?.variant_chain !== "Unassigned: Unassigned"' },
            text: `{{ item?.product?.variant_chain }}`,
        },
        {
            selector: "[OrderSummary] [CartItems] [Item] [ProductPrice]",
            text: `Quantity: {{ item.quantity }} Price: {{ currency.format(item.price) }}`,
        },
        
        // Order summary table
        {
            selector: "[OrderSummary] [Subtotal]",
            text: `{{ currency.format(cartData?.sub_total || 0) }}`,
        },
        {
            selector: "[OrderSummary] [SubtotalLabel]",
            text: `${allowDyanmicContent ? site_specific?.cart?.th_subtotal: '__EXISTING_HTML__'}`
        },
        {
            selector: "[OrderSummary] [OptionalServiceCheckoutRow]",
            attr: { 'v-if': 'site_specific.cart.lbl_additional_charge && !site_specific?.confg?.checkout?.combine_additional_charges_with_taxes && checkoutStore?.additionalCharges?.length > 0' }
        },
        {
            selector: "[OrderSummary] [OptionalServiceCheckoutRow] th",
            text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_additional_charge : '__EXISTING_HTML__' }}`
        },
        {
            selector: "[OrderSummary] [OptionalService]",
            text: `{{ currency.format(+cartData?.additional_charge || 0) }}`,
        },
        {
            selector: "[OrderSummary] [TaxesFeesLabel]",
            attr: { 
                    'v-if': 'site_specific?.confg?.checkout?.combine_additional_charges_with_taxes'
                  }
        },
        {
            selector: "[OrderSummary] [TaxesFeesLabel] th",
             text: `${allowDyanmicContent ? site_specific?.cart?.lbl_taxes_and_fees: '__EXISTING_HTML__'}`
        },
        {
            selector: "[OrderSummary] [TaxesFees]",
            text: '{{ currency.format((cartData?.additional_charge || 0) + (+(cartData?.tax?.total) || 0)) }}',
        },
        {
            selector: "[OrderSummary] [DepositAmountCheckoutRow]",
            attr: { 'v-if': 'site_specific?.cart?.lbl_total_deposite || cartData?.deposit_amount' }
        },
        {
            selector: "[OrderSummary] [DepositAmountLabel]",
            text: `${allowDyanmicContent ? site_specific?.cart?.lbl_total_deposite: '__EXISTING_HTML__'}`
        },
        {
            selector: "[OrderSummary] [DepositAmount]",
            text: `{{ currency.format(cartData?.deposit_amount || 0) }}`,
        },
        {
            selector: "[OrderSummary] [TaxAmountLabel]",
            attr: { 'v-if': '!site_specific?.confg?.checkout?.combine_additional_charges_with_taxes' }
        },
        {
            selector: "[OrderSummary] [TaxAmountLabel]",
            attr: { 'v-if': 'site_specific?.cart?.lbl_tax' }
        },
        {
            selector: "[OrderSummary] [TaxAmountLabel] th",
             text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_tax : '__EXISTING_HTML__' }}`
        },
        {
            selector: "[OrderSummary] [TaxAmount]",
            text: `{{ currency.format((+cartData?.tax?.total || 0) ) }}`,
        },
        // {
        //     selector: "[OrderSummary] [ShippingTwoWayEstimate]",
        //     text: `{{ currency.format((+cartData?.delivery_charge || 0) ) }}`,
        // },
        {
            template: true,
            selector: "[OrderSummary] tr:has([lbl_shipping])",
            // attr: { 'v-if': 'isActiveShipping() && site_specific?.confg?.tax?.exempt_delivery_tax === false' }, 
            attr: { 'v-if': 'site_specific?.cart?.lbl_shipping' }, 
            child: [
                {
                    selector: "[lbl_shipping]", 
                    text: `${filters.lbl_shipping}`,
                },
                {
                    selector: "[ShippingCharge]", 
                    // text: `{{ currency.format(cartData?.delivery_charge || 0) }}`,
                    text: `{{ cartData?.shipping_method ? currency.format(cartData?.delivery_charge || 0) : (site_specific?.order?.txt_calculated_in_next_step || 'Calculated in next step') }}`
                },
            ]
        },  
        {
            template: true,
            selector: "[OrderSummary] tr:has([LblDeliveryTax])",
            attr: { 'v-if': 'isActiveShipping() && site_specific?.confg?.tax?.exempt_delivery_tax === false && site_specific?.cart?.lbl_delivery_tax' },  // Delivery tax calculating depend on shipping charge, so, shipping active is required
            child: [
                {
                    selector: "[LblDeliveryTax]",       
                    text: `${filters.lbl_delivery_tax || 'Delivery Tax'}`,
                },
                {
                    selector: "[DeliveryTax]",
                    text: `{{ currency.format(cartData?.delivery_tax || 0) }}`,
                },
            ]
        },  
        {
            selector: "[OrderSummary] [TotalAmount]",
            text: `{{ currency.format(cartData?.total || 0) }}`,
        },
        {
            selector: "[OrderSummary] [LblTotal]",
            text: `${allowDyanmicContent ? site_specific?.cart?.lbl_total: '__EXISTING_HTML__'}`
        },

        /* -------------------------------------------------------------------------- */
        /*                             Additional Charges                             */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[OrderSummary] [AdditionalCharges] .RentMyAdditionalChargeTitle",
            text: `${allowDyanmicContent ? site_specific?.cart?.lbl_consider_optional_services: '__EXISTING_HTML__'}` 
        },
        {
            selector: "[OrderSummary] [AdditionalCharges]",
            attr: { 'v-if': 'checkoutStore.additionalCharges?.length && !isQuote' },
            child: [
                // Title
                {   
                    selector: "[SingleCharge]",
                    attr: { 'v-for': '(charge, index) in checkoutStore.additionalCharges', ':key': 'index' },
                    child: [
                        {
                            selector: "[Title]",
                            text: `{{ charge?.description }}`,
                            target: { prev_tag: 'input', next_tag: 'span'}
                        },
                        {
                            selector: "[Title] input[type=checkbox]",
                            attr: { 
                                ':checked': 'charge?.is_selected === true || charge?.is_required',
                                ':disabled': 'charge?.is_required',
                                '@change': '(e)=>{ e.target.checked = !e.target.checked; onClickAdditionalCheckboxOrLabel(charge)}',
                             },
                            target: { prev_tag: 'input', next_tag: 'span'}
                        },
                        {
                            selector: "[FeeAmountsAndShowInputAmout]",
                            attr: { 'v-if': 'charge?.fee?.amounts?.length || charge?.input_custom' },
                            child: [
                                {
                                    selector: "[FeeAmounts]",
                                    template: true,
                                    attr: { 'v-if': 'charge?.fee?.amounts?.length' },
                                    child: [
                                        {
                                            selector: "__SELF__",
                                            attr: { 
                                                'v-for': '(amount, index2) in charge?.fee?.amounts', ':key': 'index2',
                                                '@click.stop': 'charge?.fee?.amounts?.length > 1 ? onClickAdditionalAmount(charge, amount) : false',
                                                ':class': '{"BtnActive" : charge?.is_selected && charge?.existing?.amount == amount || (charge?.is_required)}',
                                                ':style': 'charge?.fee?.amounts?.length === 1 ? "border: transparent;background: transparent;padding-left: 0;color: #555;cursor:unset;" : ""',
                                            },
                                            text: `{{ charge?.fee?.type == "percentage" ? (amount + "%") : currency.format(amount) }}`,
                                        },
                                    ]
                                },
                            ]
                        },                        
                        {
                            selector: "[ShowInputAmount]",
                            attr: { 
                                'v-if': 'charge?.input_custom', '@click': 'charge.show_input_custom = true',
                                ':class': `{"BtnActive" : charge?.existing?.config?.user_entered && !charge?.fee?.amounts?.includes(Number(charge?.existing?.config?.user_entered)) }`,
                            },
                        },
                        {
                            selector: "[SelectOptions]",
                            attr: { 
                                'v-if': 'charge?.options',
                                '@change': '(e)=>{charge.selected_option = e.target.value}',
                             },
                            child: [
                                {
                                    selector: "option",
                                    attr: { 
                                        'v-for': '(option_val, index3) in charge?.options?.split(";")', ':key': 'index3', 
                                        ':value': 'option_val',
                                        ':selected': 'charge?.existing?.config?.selected_option == option_val',
                                    },
                                    text: `{{ option_val }}`,
                                },
                            ]
                        },
                        {
                            selector: "[InputAmountArea]",
                            attr: { 'v-if': 'charge?.show_input_custom' },
                            child: [
                                {
                                    selector: "[InputAmount]",
                                    attr: { 
                                        ':value': 'charge?.existing?.config?.user_entered', 
                                        '@input': '(e)=>{charge.user_inputed_value = e.target.value}', 
                                    },
                                },
                                {
                                    selector: "[OkButton]",
                                    attr: { '@click': 'OnSubmitOptionChargeInputedAmount(charge, charge.user_inputed_value)' },
                                },
                                {
                                    selector: "[CancelButton]",
                                    attr: { '@click': 'OnCancelOptionChargeInputedAmount(charge)' },
                                },
                            ]
                        },
                   
                    ]
                },
            ]
        },

        /* -------------------------------------------------------------------------- */
        /*                            IsCustomerAccount                               */
        /* -------------------------------------------------------------------------- */        
        {
            selector: "[IsCustomerAccountDiv]",
            attr: { 
                'v-if': '!isAuthenticated && site_specific?.confg?.customer?.active && !isQuote',
             },
            child: [
                {
                    selector: "[IsCustomerAccount]",
                    attr: { 
                        ':checked': 'common.is_customer_account',
                        '@click.stop': 'common.is_customer_account = !common.is_customer_account',
                     },                     
                },
            ]

        },
        /* -------------------------------------------------------------------------- */
        /*                               Extra Checkbox                               */
        /*                     (first Implemented for proparazi)                      */
        /* -------------------------------------------------------------------------- */       
        {
            selector: "[CheckoutExtracCheckbox]",
            attr: { 
                'v-if': 'checkout_extraCheckboxText && !isQuote',
            },
            child: [
                {
                    selector: "input",
                    attr: { 
                        ':checked': 'isChecked_extraCheckbox',
                        '@click.stop': `({target}) => {
                            isChecked_extraCheckbox = target.checked;
                        }`,
                    },            
                },
                {
                    selector: '[TextArea]',
                    text: '{{ checkout_extraCheckboxText }}',
                },
            ]          
        },
        
         
        /* -------------------------------------------------------------------------- */
        /*                            Terms And Conditions                            */
        /* -------------------------------------------------------------------------- */        
        {
            selector: "[TermsConditions] input",
            attr: { 
                ':checked': 'accepted_term_and_conditions',
                '@click.stop': `onClickTermsAndConditions($event);`,
             },
        },
        {     
            selector: "[TermsConditions] [ShowPopup]",
            attr: { 
                '@click.stop': `() => {
                    showModal = true;
                    if(!checkoutStore.termAndConditions?.contents?.content){
                        let showToaster = RentMyEvent.apply_filters('cdn:checkout_page:t&c:no_content:toast_message', 'Terms & condition not added');
                        if(showToaster){
                            Toaster().warning(showToaster);
                        }
                    }
                }`,
            },
        },

        
        /* -------------------------------------------------------------------------- */
        /*                               Signature Area                               */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[SingnatureArea]",
            attr: { 'v-if': 'show_signature_pad' },
            child: [
                {
                    selector: "[Clear]",
                    attr: { '@click.stop': 'signaturePad.clear()' },
                },
                {
                    selector: "[Undo]",
                    attr: { '@click.stop': 'signatureUndo()' },
                },
            ]
        },  

        /* -------------------------------------------------------------------------- */
        /*                    Back to cart and place order button                     */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[BackToCartBtn]",
            attr: { ':href': 'RENTMY_GLOBAL?.page?.cart' }
        }, 
        {
            selector: "[BackToCartBtnLabel]",
            text: `${allowDyanmicContent ? filters.btn_back_to_cart: '__EXISTING_HTML__'}`
        }, 
        {
            selector: "[PlaceOrderBtn]",
            attr: { '@click.stop': 'onClickPlaceOrder' }, 
        },  
        {
            selector: "[PlaceOrderBtnLabel]", 
            text: `{{ allowDyanmicContent ? ( isQuote ? site_specific.checkout_payment.btn_place_quote : site_specific.checkout_payment.btn_place_order) : ( isQuote ? 'Place Quote' : '__EXISTING_HTML__') }}`
        }, 
        
        
        /* -------------------------------------------------------------------------- */
        /*                               Wish list Summary                            */
        /* -------------------------------------------------------------------------- */
        {
            selector: "[OrderWisthListSummary]",
            attr: { 
                'v-if': 'showWishListSummary', 
            }, 
            child: [ 
                {
                    selector: "[WisthListItems]",
                    attr: { 'v-if': 'wishlistStore.listof_wishlist?.wish_list_items?.length' },
                    child: [
                        {
                            selector: "[WisthListItems] [WisthListItem]",
                            attr: { 'v-for': '(item, index) in wishlistStore.listof_wishlist?.wish_list_items', ':key': 'index' },
                            child: [
                                {
                                    selector: "img",
                                    attr: { ':src': 'helper.getProductImage(item)' },
                                },
                                {
                                    selector: "[ProductName]",
                                    text: `{{ item?.product?.name }}`,
                                },
                            ]
                        },
                       
                    ]
                },
                {
                    selector: '[BackToWishListBtn]',
                    attr: {
                        '@click.stop': 'backToWishList()'
                    }
                },
                {
                    selector: '[BackToWishListBtnLabel]',
                },
                {
                    selector: '[PlaceQuoteOrderBtn]',
                    attr: { '@click.stop': 'onClickPlaceQuoteOrder_forWishList' }, 
                },
            ]
        },

    ]));
}