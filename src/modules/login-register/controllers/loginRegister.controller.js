import { defineStore } from 'pinia'
import { reactive } from 'vue'
import logRegService from '../services/loginRegister.service'
import { Toaster, emitter } from '@/import-hub';
import helper from '@utils/helper';
import cookie from '@utils/cookie';


export const useLoginRegisterStore = defineStore('logingRegister', () => {

  const state = reactive({
    customer_profile: null,
  })

  const login = async (payload, from_modal=false, forPartner=false) => {

    let wishlist_token = localStorage.getItem('wishlist_token')

    const response = await logRegService.login({...payload, ...(wishlist_token ? {wishlist_token} : {})}, forPartner)
    if(response.status == 'OK' && response?.result?.data){
      let data = response?.result?.data 
      cookie.setCookie('rentmy_customer_info', data);
      
      if(from_modal){
          Toaster().success(response?.result?.message);          
          emitter.emit('forModal:loginSuccess', response?.result?.data);
      } else {      
        Toaster().success(response?.result?.message);
        helper.redirect('customer_order_history');      
      }        
    }else{
        emitter.emit('alertMessage', response?.result?.message);     
    }
  }
  
  const register = async (payload, forPartner=false) => {
    let wishlist_token = localStorage.getItem('wishlist_token')
     
    const response = await logRegService.register({...payload, ...(wishlist_token ? {wishlist_token} : {})}, forPartner);
    if(response.status == 'OK' && response?.result?.data){
      Toaster().success('Registration successful');
      if(forPartner) helper.redirect('partner_login');
      else helper.redirect('login');
    }else{
      emitter.emit('alertMessage', response?.result?.message);
      return false
    }
  }
  
  
  return {
    login,
    register,
  }
})