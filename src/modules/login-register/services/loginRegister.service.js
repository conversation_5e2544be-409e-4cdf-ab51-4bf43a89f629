import http from '@utils/http'

export default {
  async login(payload, forPartner=false) {
    let extra = forPartner ? {is_client: true} : {}
    const response = await http.post('customers/login', {...payload, ...extra})
    return response.data
  }, 
  async register(payload, forPartner=false) {
    let api_url = forPartner ? '/client/register' : '/customers/register'
    const response = await http.post(api_url, payload)
    return response.data
  },
}