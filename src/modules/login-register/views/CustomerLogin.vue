<script setup>
import { ref, inject, onMounted, defineProps } from 'vue';
import { useLoginRegisterStore } from '@modules/login-register/controllers/loginRegister.controller';
import { Toaster, emitter } from '@/import-hub';
import Loader from '@components/Loader.vue';
let setWraperIsReady = inject('setWraperIsReady');
let { wrapper, forPartner } = defineProps(['wrapper', 'forPartner']);
const usingInModal= wrapper.classList.contains('usingInModal');

const customerPortalHTML = inject('customerPortalHTML')
const after_login_action = inject('after_login_action', 'reload') // 'reload' | 'goto_checkout_page' | 'goto_cart_page'

let logRegStore = useLoginRegisterStore();
let logingPayload = ref({
    email: null,
    password: null,
})
let showLoader = ref(false)
const login = async function(logingPayload, { reload=false }={}){
    showLoader.value = true;
    try {
        await logRegStore.login(logingPayload, usingInModal, forPartner);
        showLoader.value = false;
        if(reload){
            if(after_login_action.value === 'goto_checkout_page'){
                window.open(RENTMY_GLOBAL.page.checkout, '_self'); 
            }else{
                window.location.reload()
            }
            after_login_action.value = 'reload'

            
        }
    } catch (error) {
        showLoader.value = false;
    }
}

const site_specific = JSON.parse( localStorage.getItem("rentmy_contents") )?.site_specific;
const customer_portal_activated = site_specific?.confg?.customer?.active;  

const form = wrapper.querySelector('#RentMyCustomerLoginForm');
let alertMessage = ref(null)
const AlertDiv = wrapper.querySelector('.RentMyAlertMessage');

if(usingInModal){
    const firstDiv = wrapper.querySelector('div');
    if(firstDiv) firstDiv.addEventListener('click', (event) => {
        event.stopPropagation();
        // event.preventDefault();
    })
}

if(form){
    form.addEventListener('submit', submitForm);
}else{
    Toaster().error('Login form not found');
}

function submitForm(event){
    event.stopPropagation();
    event.preventDefault(); 
    
    const keys = Object.keys(logingPayload.value);
    keys?.forEach(key => {
        logingPayload.value[key] = form.querySelector(`[name=${key}]`)?.value || null;
    })
    let is_realod = form.getAttribute('data-isreload')
    login(logingPayload.value, {reload: is_realod})
}

const submitButton = form.querySelector('#RentMyCustomerLoginForm button[type=submit]');
submitButton.addEventListener('click', submitForm);

onMounted(() => {    
    emitter.on('alertMessage', (msg) => {
        alertMessage.value = msg
    })
    setWraperIsReady(wrapper);   
    if(!customer_portal_activated){
        wrapper.innerHTML = customerPortalHTML;  
    } 
})


</script>

<template>
    <template v-if="customer_portal_activated">
        <template v-if="submitButton && showLoader">
            <teleport :to="submitButton">
                <Loader margin="0px 5px"></Loader>
            </teleport>
        </template>
        <template v-if="AlertDiv && alertMessage">
            <teleport :to="AlertDiv">
                <div @click.stop="alertMessage=null"> {{ alertMessage }} </div>
            </teleport>
        </template>
    </template>
</template>