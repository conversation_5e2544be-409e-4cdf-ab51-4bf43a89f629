<script setup>
import { ref, onMounted, inject, defineProps } from 'vue';
import { useLoginRegisterStore } from '@modules/login-register/controllers/loginRegister.controller';
import { useGlobalStore } from '@stores/global';
import { Toaster, emitter } from '@/import-hub';
import initAutocomplete from "@/googleMap";
import Loader from '@components/Loader.vue';
let globalStore = useGlobalStore();
let setWraperIsReady = inject('setWraperIsReady');
let { wrapper, forPartner } = defineProps(['wrapper', 'forPartner'])


const customerPortalHTML = inject('customerPortalHTML');

const confg = JSON.parse(localStorage.getItem('rentmy_contents'))?.site_specific?.confg;
const customer_portal_activated = confg?.customer?.active; 


let logRegStore = useLoginRegisterStore();


let registerPayload = ref({
    first_name: null,
    last_name: null,
    email: null,
    company_name: null,
    contact: null,
    password: null,
    confirm_password: null,
    address_line1: null,
    address_line2: null,
    country: null,
    city: null,
    state: null,
    zipcode: null,
})

let form = document.querySelector('#RentMyCustomerRegistrationForm')
let alertMessage = ref(null)
const AlertDiv = document.querySelector('.RentMyAlertMessage');

function autoFillAddress(address){
    form.address_line1.value = address.location;
    form.country.value = address.country_short_name;
    form.city.value = address.city;
    form.state.value = address.state;
    form.zipcode.value = address.zipcode;
}

onMounted(async ()=>{    

    await globalStore.getCountries();

    setTimeout(() => {
        initAutocomplete(form.address_line1);
    }, 1000);
    emitter.on('auto_fill_address', autoFillAddress);
    emitter.on('alertMessage', (msg) => {
        alertMessage.value = msg;
    })

    let countrySelectionField = form?.querySelector('select[name=country]') || null;
    if(countrySelectionField){
        let optionsHTML = '';
        globalStore.countries?.forEach((country, index) => {
            if(index == 0) optionsHTML += 'option value=""> Country </option>';
            optionsHTML += `<option value="${country?.code}"> ${country?.name} </option>` ;
                      
        })
        countrySelectionField.innerHTML = optionsHTML;
    }else{
        Toaster().error('Country select field not found');
    }
    setWraperIsReady(wrapper);
    if(!customer_portal_activated){
        wrapper.innerHTML = customerPortalHTML
    }
})

let showLoader = ref(false)

const registration = async function(registerPayload){
    showLoader.value = true
    await logRegStore.register(registerPayload, forPartner);
    showLoader.value = false;
}

if(form){
    form.addEventListener('submit', submitForm)
}else{
    Toaster().error('Registration form not found')
}

function submitForm(event){
    event.preventDefault();
    const keys = Object.keys(registerPayload.value);
    keys?.forEach(key => {
        registerPayload.value[key] = form?.querySelector(`[name=${key}]`)?.value || null
    })
    registration(registerPayload.value)
}
 
const submitButton = form.querySelector('button[type=submit]');

</script>

<template>
    <template v-if="submitButton && showLoader">
        <teleport :to="submitButton">
            <Loader margin="0px 5px"></Loader>
        </teleport>
    </template>

    <template v-if="AlertDiv && alertMessage">
        <teleport :to="AlertDiv">
            <div @click.stop="alertMessage=null"> {{ alertMessage }} </div>
        </teleport>
    </template>
</template>