import http from '@utils/http'
import { GET_OnlineStoreCartToken, getOnlineStoreCartDate } from '@utils/functions'

const locationId = window.RENTMY_GLOBAL.locationId;
export default {

  async getCustomFieldList(product_id) {
    const response = await http.get(`products/custom-fields/values/${product_id}`);
    return response.data
  },
  async getAddonProductListById(id) {
    const response = await http.get("products/" + id + "/addons?required=true&location=" + locationId);
    return response.data
  },
  async getPriceValue(cart) {
    const response = await http.post('get-package-price', cart);
    return response.data
  },
  async getDatesFromDuration(data) {
    let endPoint = `/product/get_dates_from_duration`
    const response = await http.post(endPoint, data);
    return response.data
  },
  async getProductDetails(id, { view_type='' }={}) {
    const timestamp = new Date().getTimezoneOffset();
    const timezone_offset_minutes = timestamp === 0 ? 0 : -timestamp;
    const cartToken = GET_OnlineStoreCartToken();
    let api_url = "";
    const cartDateObj = getOnlineStoreCartDate();

    if (cartDateObj != null) {
      api_url = "package-details/" + id + "?location=" + locationId + "&token=" + cartToken + "&start_date=" + cartDateObj.startDate + "&end_date=" + cartDateObj.endDate;
    } else {
      api_url = "package-details/" + id + "/" + timezone_offset_minutes + "?token=" + cartToken;
    }
    response = await http.get(api_url, {params: { view_type }});
    return response.data

  },
  async getDeliveryOptionList() {
    const response = await http.get(`multi-store-delivery/configs`)
    return response.data;
  },
  async getCalenderData(id, params, v_p_id, location_id) {
    const response = await http.get(`calendar/available?location=${location_id}&product_id=${id}&variants_products_id=${v_p_id}&start_date=${params.start_date
      }&end_date=${params.end_date}&source=checkout`)
    return response.data;
  },
  async getHourlyRentalData(id, params, v_p_id, location_id) {
    const response = await http.get(`calendar/available-in-hour?location=${location_id
      }&source=checkout&product_id=${id}&variants_products_id=${v_p_id}&view_date=${params.start_date
      }${params.time ? `&time= ${params.time}` : ''}`)
    return response.data;
  },
  //
  hasActiveMultiDistance() {
    if (localStorage.getItem('rentmy_contents')) {
      let multiDistance = false;
      let content = JSON.parse(localStorage.getItem('rentmy_contents'));
      multiDistance = content.site_specific?.confg?.delivery?.multi_distance?.active ? true : false;
      return multiDistance;
    }
  },
  hasSameDateBooking() {
    if (localStorage.getItem('rentmy_contents')) {
      let content = JSON.parse(localStorage.getItem('rentmy_contents'));
      if (content.site_specific?.confg?.checkout.hasOwnProperty('same_date_booking')) {
        return content.site_specific?.confg?.checkout?.same_date_booking || false;
      } else {
        return true;
      }
    }
  },
  hasActiveRecurringPayment() {
    let st = false;
    let contents = JSON.parse(localStorage.getItem("rentmy_contents"));
    if (contents?.site_specific && contents?.site_specific?.confg && contents?.site_specific?.confg?.arb) {
      let arb = contents?.site_specific?.confg?.arb;
      if (arb?.active && arb?.store_active != "standard") {
        st = true;
      }
    }
    return st;
  },
  async getCustomerInfo() {
    const response = await http.get(`customers/profile`);
    return response.data;
  },
  saveDeliveryFlow(deliveryFlow) {
    if (!localStorage.getItem("deliveryFlow")) {
      localStorage.setItem("deliveryFlow", JSON.stringify(deliveryFlow));
    }
    //Use for order coplete page delivery option charge label show    
    localStorage.setItem("deliveryFlowLabel", JSON.stringify(deliveryFlow));

  },
  async addtoCart(data) {
    let offset = moment.parseZone(Date.now()).utcOffset() / 60;
    const timestamp = new Date().getTimezoneOffset();
    const timezone_offset_minutes = (timestamp === 0) ? 0 : -timestamp;
    data.zone = timezone_offset_minutes;
    const response = await http.post("carts/add-to-cart", data);
    return response.data;
  },
  saveCartsInlocalStorage(data) {
    localStorage.setItem("user_cart", JSON.stringify(data));
  },
  //
  hasActiveMultiDistance() {
    if (localStorage.getItem('rentmy_contents')) {
      let multiDistance = false;
      let content = JSON.parse(localStorage.getItem('rentmy_contents'));
      multiDistance = content.site_specific?.confg?.delivery?.multi_distance?.active ? true : false;
      return multiDistance;
    }
  },
  hasSameDateBooking() {
    if (localStorage.getItem('rentmy_contents')) {
      let content = JSON.parse(localStorage.getItem('rentmy_contents'));
      if (content.site_specific?.confg?.checkout.hasOwnProperty('same_date_booking')) {
        return content.site_specific?.confg?.checkout?.same_date_booking || false;
      } else {
        return true;
      }
    }
  },
  hasActiveRecurringPayment() {
    let st = false;
    let contents = JSON.parse(localStorage.getItem("rentmy_contents"));
    if (contents?.site_specific && contents?.site_specific?.confg && contents?.site_specific?.confg?.arb) {
      let arb = contents?.site_specific?.confg?.arb;
      if (arb?.active && arb?.store_active != "standard") {
        st = true;
      }
    }
    return st;
  },
  async getCustomerInfo() {
    const response = await http.get(`customers/profile`);
    return response.data;
  },
  saveDeliveryFlow(deliveryFlow) {
    if (!localStorage.getItem("deliveryFlow")) {
      localStorage.setItem("deliveryFlow", JSON.stringify(deliveryFlow));
    }
    //Use for order coplete page delivery option charge label show    
    localStorage.setItem("deliveryFlowLabel", JSON.stringify(deliveryFlow));

  },
  async addtoCart(data) {
    let offset = moment.parseZone(Date.now()).utcOffset() / 60;
    const timestamp = new Date().getTimezoneOffset();
    const timezone_offset_minutes = (timestamp === 0) ? 0 : -timestamp;
    data.zone = timezone_offset_minutes;
    const response = await http.post("carts/add-to-cart", data);
    return response.data;
  },
  saveCartsInlocalStorage(data) {
    localStorage.setItem("user_cart", JSON.stringify(data));
  },
  async getData(url) {
    const response = await http.get(url);
    return response.data;
  },
  async postData(url,data) {
    const response = await http.post(url, data);
    return response.data;
  },
  async addtoCart(data) {
    let offset = moment.parseZone(Date.now()).utcOffset() / 60;
    const timestamp = new Date().getTimezoneOffset();
    const timezone_offset_minutes = (timestamp === 0) ? 0 : -timestamp;
    data.zone = timezone_offset_minutes;
    const response = await http.post("carts/add-to-cart", data);
    return response.data;
  },
  async addtoCartPackageBuy(data) {
    const timestamp = new Date().getTimezoneOffset();
    const timezone_offset_minutes = (timestamp === 0) ? 0 : -timestamp;
    data.zone = timezone_offset_minutes;
    const response = await http.post("package/add-to-cart/buy", data);
    return response.data;
  },
  async addtoCartPackageRent(data) {
    const timestamp = new Date().getTimezoneOffset();
    const timezone_offset_minutes = (timestamp === 0) ? 0 : -timestamp;
    data.zone = timezone_offset_minutes;
    const response = await http.post("package/add-to-cart", data);
    return response.data;
  },
  async addtoCartPackage(data) {
    const timestamp = new Date().getTimezoneOffset();
    const timezone_offset_minutes = (timestamp === 0) ? 0 : -timestamp;
    data.zone = timezone_offset_minutes;
    const response = await http.post("package/add-to-cart", data);
    return response.data;
  },
  async getCalenderPackageData(data) {
    const response = await http.post(`calendar/package/available`, data);
    return response.data;
  },
  async getExactTimes(product_id, start_date) { 
    const response = await http.get(`/product/${product_id}/get-exact-time?start_date=${start_date}`);
    return response.data;
  },


}