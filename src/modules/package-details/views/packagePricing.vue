<script setup>
import {
    GET_OnlineStoreCartToken,
    getQtyFromCartList,
    getQtyFromProductList,
    formateRentType,
    getOnlineStoreCartDate,
    isCartDateExist, checkSameDatetime, datePipe, formatPriceDuration
} from '@utils/functions';
import { createComponentHTML } from '@utils/functions/withComponent';
import { defineProps, defineAsyncComponent, ref, reactive, inject, watch } from 'vue';
import packageService from '../services/packageDetails.service';
import currency from '@utils/currency';
import { Toaster } from '@/import-hub';
import EmDateTimePicker from '@components/em-datetimepicker/EmDateTimePicker.vue';
import TimePicker from '@components/TimePicker.vue';
let { helper } = inject("utils");
let globalLoader = inject('globalLoader');
let { domElement } = inject('utils');
const packageDetailsStore = inject('packageDetailsStore');
import LocationNoticeModal from "@components/modal/LocationNotice.vue";
const log = console.log;
let allowDyanmicContent = inject('allowDyanmicContent');
let emitter = inject('emitter');



import { useWishlistStore } from "@stores/wishlist";
const wishlistStore = useWishlistStore()

const site_specific = JSON.parse(localStorage.getItem('rentmy_contents'))?.site_specific;
const is_active_wish_list = site_specific?.confg?.inventory?.wishlist?.active
 

/* -------------------------------------------------------------------------- */
/*                                  VARIABLES                                 */
/* -------------------------------------------------------------------------- */

const storeContent = localStorage.getItem("rentmy_contents")
    ? JSON.parse(localStorage.getItem("rentmy_contents"))
    : null;

let props = defineProps({
    details: {},
    wrapper: null
})
const isMounted = ref(false)
const loaded = ref(false)
let wrapper = props.wrapper;
let details = props.details;
let product = props.details;
const price_type = ref(null);
const cartLoad = ref(null);
const packageProduct = ref(null);
const packageVariant = ref(null);
const locationId = ref(null);
const is_show_addtoCart_btn = ref(null);
const cartItemList = ref([]);
const addonsProductList = ref([]);
const customFieldList = ref([]);
const deliveryOptionList = ref([]);
const customSelectFieldList = ref([]);
const unitType = ref(null);
let store_config = reactive({});
const currencySymbol = ref(null);
const customFieldPrices = ref(null);
const rentalEndDate = ref(null);
const startDateFlag = ref('');
const endDateFlag = ref('');
let priceLimitShowFirst = ref(RENTMY_GLOBAL?.detailsPage_priceLimitShowFirst || 6);
const isRentalDateShowAll = ref(false);
const isShowPricingForExactTimeItem = true;
const addonslabel = ref(null);
const enable_due_date = ref(false);
const is_showCustomDatetime_label = ref(false);
const is_show_not_available_message = ref(false);
let selected_exact_duration = reactive({});
let exact_times = reactive({});
const isModernLayout = ref(false);
let deliveryFlow = reactive({});
const selectedDeliveryFlow = ref(null);
const isActiveMultiDistance = ref(false);
const isShowFulfilmentOption = ref(false);
const fulfilmentOptionList = ref([]);
const fullfilment_type = ref(0);
const fullfilment_option = ref('in-store');
const deliveryLimitErrorLbl = ref('');
const deliveryLimitWarningLbl = ref('');
const isDisabledFulfillmentOption = ref(false);
const isHideFulfillment = ref(false);
const isSameDateBooking = true;
const isAddToCartLoading = ref(false);
let currentLocation = reactive({
    id: null,
});
let onlineStore = reactive({});
const selectedLocation = ref(null);
const isDisplayMiniCart = ref(true);
const initialLoad = ref(false);
// const details = reactive({});
const rentEndDate = ref(null);
const rentStartDate = ref(null);
const min_date = ref(null);
const store = ref(null);
const images = ref([]);
const featureImage = ref(null);
const defaultImage = ref("");
const baseRadio = ref(1);
const available = ref(null);
const rentelPriceId = ref(null);
const diabledAddtoCarts = ref(true);
const packageAvailableMessage = ref(false);
const isDisableDatePicker = ref(false);
const termFlag = ref(false);
const selectedCustomFields = ref([]);
const productOptionValueCheck = ref(false);
const extact_durations = ref([]);
const extact_times = ref([]);
const extact_times_values = ref([]);
const selected_exact_time = ref(null);
const selectedDuration = ref(null);
const enable_exact_time = ref(false);
const selectedPriceObj = ref(null);
const recurringPriceId = ref(null);
const endDateErrorMessage = ref('');
const isShowToday = ref(false);
const isShowTomorrow = ref(false);
const is_recurring_product = ref(false);
const isApply = ref(false);
const isSeparateDate = ref(false);
const startTime = ref("");
let selectedRecurringPrice = reactive({});
const isShowRecurrignWarn = ref(false);
const isActiveRecurring = ref(false);
const isNonrecurring = ref(false);
const unavailableWhenExactStartTime = ref(null);
const isAutoSelectEarliestDate = ref(false);
const autoSelectStartDate = ref(null);
const autoSelectEndDate = ref(null);
const isUpdateExactTime = ref(false);
const cartDataFound = ref(false);
const unavailableForRent = ref(null);
const availableQ = ref(null);
const actualAvailableQty = ref(null);
const temp_rent_price = ref(0);
const temp_qty = ref(0);
const tempAvailableList = ref([]);
const availableList = ref([]);
const invalid = ref([]);
const tempInvalid = ref([]);
const package_products = ref([]);
const product_list = ref([]);
const isVariantsFound = ref(false);
const variants_products_id = ref(null);
const isRentalDateOrTimeChanged = ref(true);
let isEnableExactTime = product.exact_time_with_days
let isSelectedExactTime = ref(false)
let show_checkout_availability_text = storeContent?.site_specific?.confg?.show_checkout_availability_text === true 

let widgetDates = function(){
  let using_inPageCartWidget = document.querySelector('.RentMyWrapperInpageCartWidget');
  let startDate = sessionStorage.getItem('online_inagecart_widget_startDate');
  let endDate = sessionStorage.getItem('online_inagecart_widget_endDate');
  if(using_inPageCartWidget && startDate && endDate){
      return {startDate, endDate};
  } else {
      return null;
  }
}

// const product = reactive({});
let contents = reactive({});
let delivery_settings = reactive({});
let prices = reactive({
    buy: { type: false, price: 0, id: null },
    rent: { type: false, price: [] }
});
let total = reactive({
    qty: 1,
    price: 0,
    term: ""
});
let cart = reactive({
    price: 0,
    package_id: 0,
    quantity: 0,
    variants_products_id: null,
    location: null,
    rent_start: "",
    rent_end: "",
    rental_duration: 0,
    rental_type: "",
    fullfilment_option: "",
    term: "",
    token: "",
    price_id: null,
    products: null,
    custom_fields: []
});
let daterangeConfig = reactive({
    pages: 1,
    hideActionBtn: false,
    showRangeLabels: true
})

const asset_url = RENTMY_GLOBAL?.env?.ASSET_RUL || import.meta.env.VITE_ASSET_URL;
const product_image = asset_url + "products/";
const model_msg = ref('');
const isShowModel = ref(false);

/* -------------------------------------------------------------------------- */
/*                                  FUNCTIONS                                 */
/* -------------------------------------------------------------------------- */

onInit();
async function onInit() {
    isActiveMultiDistance.value = packageService.hasActiveMultiDistance();
    isSameDateBooking.value = packageService.hasSameDateBooking();
    if (!isSameDateBooking.value) {
        let date = new Date();
        date.setDate(date.getDate() + 1);
        rentStartDate.value = formatDate(date);
    }
    contents = JSON.parse(localStorage.getItem('rentmy_contents'));
    locationId.value = RENTMY_GLOBAL.locationId;
    if (isActiveMultiDistance.value) {
        disabledDeliveryFlow();
    }

    isModernLayout.value = contents?.site_specific?.confg?.inventory?.display_mode == 'modern' ? true : false;
    // we are skipping isSeparateDate.value config, forefully by requirement
  
    isSeparateDate.value = false ?? contents?.site_specific?.confg?.inventory?.seperate_datetime_picker == true;
    onlineStore = sessionStorage.getItem("online_store") ? JSON.parse(sessionStorage.getItem("online_store")) : {};
    store.value = window.RENTMY_GLOBAL.store_id;

    if(localStorage.getItem('current_location')){

        currentLocation.id = JSON.parse(localStorage.getItem('current_location'));
        selectedLocation.value = currentLocation.id;
    }
    isDisplayMiniCart.value = contents.hasOwnProperty('site_specific')
        && contents.site_specific.hasOwnProperty('confg')
        && contents.site_specific.confg.hasOwnProperty('checkout')
        && contents.site_specific.confg.checkout.hasOwnProperty('view_mini_cart')
        ? contents.site_specific.confg.checkout.view_mini_cart
        : true;
    customFieldPrices.value = 0
    unitType.value = "%";
    store_config = localStorage.getItem("currency") ? localStorage.getItem("currency") : null;
    currencySymbol.value = (store_config && store_config.symbol) ? store_config.symbol : "$";
    cartItemList.value = localStorage.getItem("user_cart")
        ? JSON.parse(localStorage.getItem("user_cart")).cart_items
        : null;
    details = product;
    packageProduct.value = product.products;
    if (details.term) {
        availableQ.value = details.term == 1 ? details.term : (details.term - total.qty);
        actualAvailableQty.value = details.term;
    } else {
        availableQ.value = 0;
        actualAvailableQty.value = 0;
    }
    if (product.rent_end) {
        const date = datePipe(product.rent_end);
        rentalEndDate.value = date.split(' ').slice(0, 3).join(' ');
    }

    is_recurring_product.value = details?.recurring_prices && details?.enduring_rental && details.recurring_prices.length > 0;
    if (is_recurring_product.value) {
        details.recurring_prices.map(p => {
            p['recurring'] = true;
        })
    }

    
    if (details) {
        await load();
        checkAvailabe();
    }

    enable_due_date.value = !!(details?.exact_date && details.exact_date);
    if (details.hasOwnProperty('exact_time') && details.exact_time) {
        enable_exact_time.value = true;
        extact_durations.value = product.extact_durations.durations;
        extact_times.value = product.extact_durations.times;
        unavailableWhenExactStartTime.value = true;
    } else {
        enable_exact_time.value = false;
    }

    if (
        contents.site_specific.confg &&
        contents.site_specific.confg.checkout &&
        contents.site_specific.confg.checkout.hasOwnProperty(
            "online_order"
        ) && contents.site_specific.confg.checkout.online_order == false
    ) {
        is_show_addtoCart_btn.value = false;
    } else {
        is_show_addtoCart_btn.value = true;
    }

    if (isActiveMultiDistance.value && !delivery_settings?.charge_by_zone) {
        await getDeliveryOptionList();
    }

    await getAddonsProductList();
    await getCustomFieldList();
    isActiveRecurring.value = packageService.hasActiveRecurringPayment() && details?.enduring_rental;
    if (isActiveRecurring.value) {
        hasRecurringPrice();
    }

    await checkFulfilmentOption();
    if (checkEarliestDateActive()) {
        if (baseRadio.value !== 1) {
            await _setRentalDate();
        }
    }

    await buyCartGetDuration();
    selectedLocation.value = RENTMY_GLOBAL.locationId;
    loaded.value = true;
}

async function load() {
    initialLoad.value = true;
    // imageFormat();
    formatVariant();
    let byRentPrices = formatBuyRent(details.price);
    prices.buy = byRentPrices.buy;
    prices.rent = byRentPrices.rent;

    isDisableDatePicker.value = isCartDateExist();
    initPrice();
    getPackageProductVariant();
    if (checkEarliestDateActive()) {
        _getRentalDate();
    }
    updateVariantData(details);
    getAvailableList();

    if(widgetDates()){
        let dates = widgetDates();
        selectstartDateTime(dates.startDate);
        selectendDateTime(dates.endDate);
        checkPrice();
        
        setTimeout(() => {
            //startDatePicker
            if(startDatePicker.value?.[0]?.setDate){
                if(startDate_useRangePicker){
                startDatePicker.value?.[0]?.setDate(dates.startDate, dates.endDate);
                startDatePicker.value?.[0]?.setTime(dates.startDate, dates.endDate);
                } else {
                startDatePicker.value?.[0]?.setDate(dates.startDate);
                startDatePicker.value?.[0]?.setTime(dates.startDate);
                }
            }
            // endDatePicker
            if(endDatePicker.value?.[0]?.setDate){
                if(endDate_useRangePicker){
                endDatePicker.value?.[0]?.setDate(dates.startDate, dates.endDate);
                endDatePicker.value?.[0]?.setTime(dates.startDate, dates.endDate);
                } else {
                endDatePicker.value?.[0]?.setDate(dates.startDate);
                endDatePicker.value?.[0]?.setTime(dates.startDate);
                }
            }
        }, 1000);
    }
    isMounted.value = true;
    globalLoader.hide();
    domElement.setWraperIsReady(wrapper);     

}


function checkAvailabe() {
    const dqty = details.available;
    const a = dqty - checkAvailableForType();
    available.value = a < 0 ? 0 : a;
    total.qty = available.value > -1 ? 1 : 1;
    setTotalPrice();
}


function currencyConvert(amount) {
    return currency.currencyConvert(amount);
}


function customPricingDataFormat(item) {
    let text = item.value;
    if (item.price_amount && item.display_price) {
        text = text + " ("; // + amount + "" + type + ")";
        text = (item.price_amount < 0) ? (text + "-") : (text + "+");
        if (item.price_type == 1) { // type percentage
            text = text + Math.abs(item.price_amount) + "%)";
        } else {
            const priceWithSymbol = currencyConvert(item.price_amount);
            text = text + priceWithSymbol + ")";
        }
    }
    return text;
}

async function getDeliveryOptionList() {
    const res = await packageService.getDeliveryOptionList()
    if (res.status == 'OK') {
        deliveryOptionList.value = res.result.data;
        if (deliveryOptionList.value.length > 0) {
            onClickSelectDeliveryFlow(deliveryOptionList.value[0]);
        }
    } else {
        deliveryOptionList.value = [];
    }

}

function onClickSelectDeliveryFlow(item) {
    if (isDisabledDeliveryFlow) return;
    deliveryFlow = item;
    selectedDeliveryFlow.value = item.delivery_flow;
}

function onClickSelectFulfilment(item) {
    if (isDisabledFulfillmentOption.value) return;
    fullfilment_type.value = item.id;
    fullfilment_option.value = item.value;
}
function isDisabledDeliveryFlow() {
    let status = false;
    let userCart = localStorage.getItem("user_cart") ? JSON.parse(localStorage.getItem("user_cart")) : null;
    let deliveryFlow = JSON.parse(localStorage.getItem("deliveryFlow"));
    if (deliveryFlow) {
        status = deliveryFlow.delivery_flow && userCart?.cart_items?.length > 0 ? true : false;
        selectedDeliveryFlow.value = deliveryFlow.delivery_flow;
    }
    return status;
}
function disabledDeliveryFlow() {
    if (localStorage.getItem("deliveryFlow") && localStorage.getItem("deliveryFlow") != 'undefined') {
        if (localStorage.getItem("user_cart")) {
            let userCart = localStorage.getItem("user_cart") ? JSON.parse(localStorage.getItem("user_cart")) : null;
            if (userCart.hasOwnProperty('cart_items')) {
                if (userCart.cart_items.length == 0) {
                    localStorage.removeItem('deliveryFlow')
                    localStorage.removeItem('deliveryFlowLabel')
                }
            }
        }
    }
}


function saveDeliveryFlow() {
    packageService.saveDeliveryFlow(deliveryFlow);
    disabledDeliveryFlow();
}

async function getCustomFieldList() {
    const res = await packageService
        .getCustomFieldList(details.id)

    if (res.status == "OK") {
        customFieldList.value = res.result.data;
        if (customFieldList.value.length) {
            customFieldList.value.forEach(elem => {
                if (['select', 'predefined_radio', 'rich_text_box'].includes(elem?.type)) {
                    elem.selectedValue = '';
                    elem.product_field_value.forEach(item => {
                        item.showValue = customPricingDataFormat(item); // re-format price amount and price type to show in dropdown
                        item.active = false; // if display format is button, then on click it will turn to true for UI purpose
                    });
                    const activeCount = elem.product_field_value.filter(x => x.active == false);
                    elem.activeCount = activeCount.length; // need to display none intitally
                    let isExist = customSelectFieldList.value.findIndex(item => item.id == elem.id) > -1;
                    if(!isExist) {
                        if(elem?.type === 'rich_text_box'){
                        if(elem?.field_value){
                            customSelectFieldList.value.push(elem);
                        }
                        }
                        else if(elem.product_field_value?.length){
                            customSelectFieldList.value.push(elem);
                        }
                    }
                }
            });
        }
    } else {
        customFieldList.value = [];
    }
    customFieldSelector(true);

}

function predefineRadioCustomfieldSeleted() {
    let predefinedRadioCustomField = customFieldList.value.find(el => el.is_ && el.type == 'predefined_radio');
    if (predefinedRadioCustomField?.applicable_for == 1 ||
        (predefinedRadioCustomField?.applicable_for == 2 && baseRadio.value == 2) ||
        (predefinedRadioCustomField?.applicable_for == 3 && baseRadio.value == 1)) {

        if (predefinedRadioCustomField) {
            let elObj = predefinedRadioCustomField.product_field_value[0];
            _addToSelectedCustomField(elObj, predefinedRadioCustomField);
        }
    }
}

function customFieldSelector(isInitial = false) {

    selectedCustomFields.value = []; // empty selected fields

    customSelectFieldList.value.forEach(field => {
        if (field.applicable_for == 1 ||
            (field.applicable_for == 2 && baseRadio.value == 2) ||
            (field.applicable_for == 3 && baseRadio.value == 1)) {

            if (!field.is_ && field.product_field_value.length && field.type !== 'rich_text_box') {
                if (field.display_format === 'button') {
                    let elObj = field.product_field_value[0];
                    onFieldValueChangeForDefault(elObj.id, field, 'button');

                } else {
                    let elObj = field.product_field_value[0];
                    field.selectedValue = elObj.id;
                    onFieldValueChangeForDefault(elObj.id, field);
                }
            }

        }
    });
    predefineRadioCustomfieldSeleted();
    if (isInitial || baseRadio.value == 1) {
        checkPrice(true);
    }

}

function onFieldValueChangeForDefault(fieldValue, field, format) {
    productOptionValueCheck.value = true;
    const value = field.product_field_value.find(v => v.id == fieldValue);
    if (format === 'button') {

        field.product_field_value.forEach(item => {
            item.active = item.id == fieldValue;
        });

        const activeCount = field.product_field_value.filter(x => x.active == false);
        field.activeCount = activeCount.length;
        if (value.active) {
            _addToSelectedCustomField(value, field);
        }

    } else {
        _addToSelectedCustomField(value, field);
    }

}

function _addToSelectedCustomField(value, field) {

    selectedCustomFields.value.push({
        id: value.id,
        value: value.value,
        name: value.name,
        label: value.label,
        amount: value.price_amount,
        type: value.price_type,
        applicable_for: field.applicable_for,
        is_private: field.is_private
    });

}

function onFieldValueChange(fieldValue, field, format) {
    productOptionValueCheck.value = true;
    if (fieldValue !== "") {
        if (selectedCustomFields.value.length) {
            selectedCustomFields.value = selectedCustomFields.value.filter(cf => cf.name !== field.name);
        }
        const value = field.product_field_value.find(v => v.id == fieldValue);
        if (format === 'button') {

            field.product_field_value.forEach(item => {
                item.active = item.id == fieldValue ? !item.active : false;
            });

            const activeCount = field.product_field_value.filter(x => x.active == false);
            field.activeCount = activeCount.length;
            if (value.active) {
                selectField('push', value, field);
            } else {
                selectField('pop', field);
            }
        } else {
            selectField('push', value, field);
        }
    } else {
        if (format === 'button') {

            field.product_field_value.forEach(item => {
                item.active = item.id == fieldValue ? !item.active : false;
            });

            const activeCount = field.product_field_value.filter(x => x.active == false);
            field.activeCount = activeCount.length;
        }
        selectField('pop', field);
    }
}

function selectField(type, value, field) {
    if (type === 'push') {
        selectedCustomFields.value.push({
            id: value.id,
            value: value.value,
            name: value.name,
            label: value.label,
            amount: value.price_amount,
            type: value.price_type,
            applicable_for: field.applicable_for,
        });
    } else {
        const name = value.name;
        selectedCustomFields.value = selectedCustomFields.value.filter(f => f.name !== name);
    }
    if (enable_exact_time.value) {
        if ((selectedDuration.value && selected_exact_time.value) || cartDataFound.value) checkPrice();
    } else {
        checkPrice(true);
    }
}

async function getAddonsProductList() {
    const res = await packageService.getAddonProductListById(details.id)

    if (res.status == "OK") {
        addonsProductList.value = res.result.data;
        addonslabel.value = res.result.label;
        addonsProductList.value.map(prod => {
            let index = 0;
            if (prod.image) {
                prod[
                    "img"
                ] = `${product_image}${store.value}/${prod.id}/${prod.image}`;
            } else {
                prod["img"] = RENTMY_GLOBAL?.images?.default_product_image;
            }
            prod.variants.map(v => {
                if (index == 0) {
                    v["min_qty"] = prod.min_quantity;
                } else {
                    v["min_qty"] = 0;
                }
                index++;
            });
            return prod;
        });
    } else {
        addonsProductList.value = [];
    }

}


function onVariantQtyKeyup(quantity, variant_id, product_id) {

    if (!isNaN(parseInt(quantity))) {
        addonsProductList.value.map(p => {
            if (p.id == product_id) {
                p.variants.map(v => {
                    if (v.id == variant_id) {
                        v["min_qty"] = parseInt(quantity);
                    }
                });
            }
        });
    }

}


function showPricingOption() {

    if (
        contents.site_specific.confg &&
        contents.site_specific.confg.hasOwnProperty("rental_price_option")
    ) {
        return contents.site_specific.confg.rental_price_option;
    }
    return true;
}

function removingUnAssignedValue(item) {
    // console.log(item)
    let products_varients = [];
    products_varients = item;
    products_varients = products_varients.filter(
        v => v.variant_chain !== "Unassigned: Unassigned"
    );
    return products_varients;
}

function isAddonsProductCombinationOk() {
    let requiredAddonsQty = 0;
    let result = true;
    let sumOfVariantQty = 0;

    if (addonsProductList.value.length > 0) {
        addonsProductList.value.map(p => {
            requiredAddonsQty = requiredAddonsQty + total.qty * p.min_quantity;

            p.variants.map(v => {
                sumOfVariantQty = sumOfVariantQty + v.min_qty;
            });
        });

        if (sumOfVariantQty > requiredAddonsQty) {
            result = false;
        }
    }

    return result;
}

function onDurationChange(duration_value) {
    selectedDuration.value = duration_value;
    selected_exact_time.value = null;
    unavailableWhenExactStartTime.value = true;

    if (!isDisableDatePicker.value) {
        if (duration_value == "null") duration_value = null;
        selectedDuration.value = duration_value;
        if (duration_value) {
            const duration = duration_value.split(",");
            selected_exact_duration = { value: duration[0], type: duration[1], id: duration[2] };
            const selectedDurationObject = extact_durations.value.find(
                d => d.value === duration[0] && d.type === duration[1]
            );
            extact_times.value = selectedDurationObject.times;
            extact_times_values.value = selectedDurationObject.values;
            total.term = selected_exact_duration.value + " " +
                (selected_exact_duration.value > 1
                    ? selected_exact_duration.type + "s" : selected_exact_duration.type);
            // if (selected_exact_time.value) {
            //   changTime(selected_exact_time.value);
            // }
            if (!isShowStartTimeSelection) {
                getChangTime(null, selected_exact_duration);
            }
        }
    }
}

function changTime(value) {
    if (!isDisableDatePicker.value) {
        if (value == "null") value = null;
        selected_exact_time.value = value;
        if (extact_times_values.value) {
            extact_times_values.value.forEach((val) => {
                if (val.value == value) {
                    selected_exact_duration.id = val.id;
                }
            })
        }
        if (value) {
            getChangTime(selected_exact_time.value, selected_exact_duration);
        }
    }
}

function isShowStartTimeSelection() {
    let show_start_time;
    if (contents.site_specific.confg && contents.site_specific.confg.hasOwnProperty("show_start_time")) {
        show_start_time = contents.site_specific.confg.show_start_time;
    }
    if (enable_exact_time.value && show_start_time) {
        return true;
    }
    return false;
}

async function getChangTime(time, duration) {

    const loc = locationId.value;
    const cart = addCartObj();
    const products = getPackageProductVariant().map(e => {
        return { product_id: e['product_id'], variants_products_id: e['variants_products_id'] }
    });

    // ready it for used in add to cart API param
    exact_times = {
        id: duration?.id,
        date: cart.rent_start ? cart.rent_start.split(" ")[0] : null,
        time: time
    }
    localStorage.setItem("exact_times_option", JSON.stringify(exact_times));

    const sendDate = {
        product_id: details.id,
        variants_products_id: details.variants_products_id,
        start_date: cart.rent_start ? cart.rent_start.split(" ")[0] : null,
        start_time: time,
        duration: parseInt(duration.value),
        type: duration.type,
        exact_times_id: duration.id,
        location_id: loc,
        products: products
    };

    if (!sendDate.start_date || sendDate.start_date == 'null') return;

    const res = await packageService.postData("product/get_dates_from_duration", sendDate)
    if (res.status == "OK") {
        const data = res.result.data;
        cart.rent_start = data.start_date;
        rentStartDate.value = data.start_date;
        cart.rent_end = data.end_date;
        rentEndDate.value = data.end_date;
        unavailableWhenExactStartTime.value = false;
        getPriceValue(cart);
        // if product option field value change then need to call
        if (productOptionValueCheck.value || selectedCustomFields.value.length) {
            productOptionValueCheck.value = true;
        }

        if (data.hasOwnProperty("available")) {
            availableQ.value = data.available == 1 ? data.available : data.available - total.qty;
            actualAvailableQty.value = data.available;
        }
    } else if (res.status == "NOK" && res.result['message']) {
        unavailableWhenExactStartTime.value = true;
        Toaster().error(
            res.result?.message
        );
    }



}

async function addTocart() {
    isAddToCartLoading.value = true;
    if(isMounted.value) globalLoader.show();
    if (!isAddonsProductCombinationOk()) {
        Toaster().error(
            "Select product add-on quantities"
        );
        isAddToCartLoading.value = false;
        globalLoader.hide();
        return
    }
    const cart = addCartObj();
    cart.price = total.price;
    cart.custom_fields = selectedCustomFields.value;

    if (addonsProductList.value.length > 0) {
        let required_addons = [];
        let variant = [];

        addonsProductList.value.map(p => {
            variant = p.variants.filter(v => v.min_qty > 0);
            if (variant.length != 0) {
                for (let i = 0; i < variant.length; i++) {
                    required_addons.push({
                        product_id: p.id,
                        variants_products_id: variant[i].id,
                        quantity_id: variant[i].quantity_id,
                        quantity: variant[i].min_qty
                    });
                }
            }
        });
        cart["required_addons"] = required_addons;
    }

    if (enable_due_date.value) {
        cart.rent_start = product.rent_start;
        cart.rent_end = product.rent_end;
    }

    if (isShowFulfilmentOption.value) {
        cart.fullfilment_option = fullfilment_option.value;
    }
    if (isActiveRecurring.value) {
        cart["recurring"] = true;
    }

    if (isActiveRecurring.value && Object.keys(selectedRecurringPrice).length > 0) {
        cart["recurring"] = true;
        cart["price_id"] = selectedRecurringPrice.id;
    }
    // Set exact_times if is enable exact time
    if (exact_times
        && Object.keys(exact_times).length > 0
        && enable_exact_time.value
        && extact_times.value.length > 0) {
        cart['exact_times'] = exact_times;
    }

    // if (diabledAddtoCart) {
    cartLoad.value = true;
    if (baseRadio.value === 1) {
        const res = await packageService.addtoCartPackageBuy(cart)
        if (res.status === "OK") {
            afterAddToCart(res, cart);
            if(!res?.result?.error && !res?.result?.warning){
                if(res.result?.data?.token) localStorage.setItem('token', res.result?.data?.token);  
                Toaster().success('Package added to cart');
                redirectAddToCart();
            }
            isAddToCartLoading.value = false;
            globalLoader.hide();
        } else {
            globalLoader.hide();
            cartLoad.value = false;
            Toaster().error(
                "Product has not been added to cart"
            );
            isAddToCartLoading.value = false;
            globalLoader.hide();
            return
        }
    } else {
        cart['deposit_amount'] = details?.deposit_amount ? details?.deposit_amount : 0;
        const res = await packageService.addtoCartPackageRent(cart)
        if (res.status === "OK") { 
            afterAddToCart(res, cart);
            if(!res?.result?.error && !res?.result?.warning){
                Toaster().success('Package added to cart');
                if(document.querySelector('.RentMyWrapperInpageCartWidget')){
                    emitter.emit('open_inCartWidget', true);
                } else {
                    redirectAddToCart(); // =========================== Redirecting....
                }
            }
            isAddToCartLoading.value = false;
            globalLoader.hide();
        } else {
            cartLoad.value = false;
            Toaster().error(
                "Product has not been added to cart"
            );
            isAddToCartLoading.value = false;
            globalLoader.hide();
        }

    }

    // } else {
    //     isAddToCartLoading.value = false;
    //     globalLoader.hide();
    //     Toaster().error(
    //         "Required options are not selected"
    //     );
    // }

}

function redirectAddToCart() {
  if(RENTMY_GLOBAL?.using_in_cli_project){
          RentMyEvent.emit('cdn:goto:cart_page', RENTMY_GLOBAL.page.cart);
  } else {
    if(RENTMY_GLOBAL?.after_add_to_cart_redirecto_cart === false){
      windowLocation().reload();
    } else {
      window.open(RENTMY_GLOBAL?.page?.cart, '_self');    
    }
  }
}

function afterAddToCart(res, cart) {
    if (res.status === "OK") {
        
        if (res.result.data || res.result.hasOwnProperty('data')) {
            actualAvailableQty.value = actualAvailableQty.value - cart.quantity;
            packageService.saveCartsInlocalStorage(res.result.data);
            if (isActiveMultiDistance.value && !delivery_settings?.charge_by_zone) {
                saveDeliveryFlow();
            }

            /* if (isDisplayMiniCart.value) {
                packageService.cartReload.next({
                    reload: true,
                    items: res.result.data.cart_items,
                    open: true
                });
            } else {
                router.navigateByUrl("/cart/" + res.result.data.token);
            } */



            localStorage.setItem("token", res.result.data.token);


            if (
                isCartDateExist() == false &&
                cart.rent_start &&
                cart.rent_end
            ) {
                sessionStorage.setItem(
                    "online_packageServicetartDate",
                    JSON.stringify(cart.rent_start)
                );
                sessionStorage.setItem(
                    "online_cartEndDate",
                    JSON.stringify(cart.rent_end)
                );
                if (isShowFulfilmentOption.value) {
                    sessionStorage.setItem(
                        "online_fullfilment_option",
                        fullfilment_option.value
                    );
                }

            }

            total.qty = 1;
            if (enable_exact_time.value) {
                isDisableDatePicker.value = true;
                is_showCustomDatetime_label.value = true;
                rentStartDate.value = cart.rent_start;
                rentEndDate.value = cart.rent_end;
                isShowPricingForExactTimeItem.value = false;
            } else {
                isDisableDatePicker.value = true;
            }
            localStorage.setItem('user_cart', JSON.stringify(res.result.data));      
            let RentMyProductImg = document.querySelector('[RentMyProductImage]');
            let RentMyMiniCart = document.querySelector('.RentMyMiniCart');   
            if(RentMyProductImg && RentMyMiniCart) {
                // domElement.dropInCart(RentMyProductImg.parentNode, RentMyMiniCart);
            }        
        }

        cartLoad.value = false;
    }
}

function variantsChange(variants_products_id, product_id) {
    let data = {
        variants_products_id: variants_products_id,
        product_id: product_id
    }
    const i = package_products.value.findIndex(e => e.product_id === product_id);
    if (i > -1) {
        package_products.value[i] = data;
    } else {
        package_products.value.push(data);
    }

    // console.log(package_products.value);
    getAvailableList();

}

async function showVarientQuantiy(varient_id, id, index) {

    if (enable_due_date.value) {
        // if due date found then use product start_date & end_date as default 
        rentStartDate.value = product.rent_start;
        rentEndDate.value = product.rent_end;
    }

    let selectedVarient;
    details.products.forEach(item => {
        if (item.id === id) {
            selectedVarient = item.variants.find(v => v.id === Number(varient_id));
        }
    });

    const variants = [...cart.products];
    variants.forEach(item => {
        if (item.product_index === index) {
            item.variants_products_id = selectedVarient.id;
        }
    });
    const selectedVariantsArr = variants.map(v => v.variants_products_id);
    const location_id = locationId.value;
    const timestamp = new Date().getTimezoneOffset();
    const timezone_offset_minutes = timestamp === 0 ? 0 : -timestamp;
    let qs = "start_date=" + rentStartDate.value + "&end_date=" + rentEndDate.value + "&location=" + location_id;
    if (selectedVariantsArr.length > 0) {
        selectedVariantsArr.forEach(v => { qs = qs + "&variants[]=" + v; });
    }

    variantsChange(varient_id, id);


    const res = await packageService.getData("package/" + details.uid + "/term/" + timezone_offset_minutes + "?" + qs +
        "&token=" + GET_OnlineStoreCartToken())

    if (res.status === "OK") {
        if (res.result.error) {
            return;
        }
        else {
            total.qty = 1;
            availableQ.value = res.result.data == 1 ? res.result.data : res.result.data - total.qty;
            actualAvailableQty.value = res.result.data;
        }

        changeVariantPackage(selectedVarient, id, index);
    }



}

function changeVariant(pos, id) {
    const data = changeVariantCall(pos, id);
    if (data) {
        if ("pos" in data) {
            variantChain(data);
        } else {
            getLastvariantChain(data);
        }
    }
}

async function variantChain(data) {
    const timestamp = new Date().getTime();
    const m = await packageService.getData(
        `variant-chain?product_id=${product.id}&variant_id=${data.id}&variant_chain=${data.chain}&t=${timestamp}`)
    const res = m.result.data;
    variantChainRes(res, data);
}

function showEndDate() {
    if (enable_exact_time.value && !isDisableDatePicker.value) {
        return false;
    } else if (
        contents.site_specific.confg &&
        contents.site_specific.confg.hasOwnProperty("show_end_date")
    ) {
        return contents.site_specific.confg.show_end_date;
    }
    return true;
}
function showEndTime() {
    if (enable_exact_time.value && !isDisableDatePicker.value) {
        return false;
    } else if (contents.site_specific.confg && contents.site_specific.confg.hasOwnProperty("show_end_time")) {
        return contents.site_specific.confg.show_end_time;
    }
    return true;
}

function showStartTime() {
    if (enable_exact_time.value && !isDisableDatePicker.value) {
        return false;
    } else if (contents.site_specific.confg && contents.site_specific.confg.hasOwnProperty("show_start_time")) {
        return contents.site_specific.confg.show_start_time;
    }
    return true;
}
function showStartDate() {
    if (
        contents.site_specific.confg &&
        contents.site_specific.confg.hasOwnProperty("show_start_date")
    ) {
        return contents.site_specific.confg.show_start_date;
    }
    return true;
}
async function getLastvariantChain(data) {
    const timestamp = new Date().getTimezoneOffset();
    const timezone_offset_minutes = timestamp === 0 ? 0 : -timestamp;
    const m = await packageService.getData(
        `get-path-of-chain?product_id=${product.id}&variant_id=${data.id
        }&variant_chain=${data.chain}&t=${timezone_offset_minutes}`
    )
    const res = m.result.data
    packageDetailsStore.setVariantSet___toUpdateImageView(res);
    getLastvariantChainCall(res);
}

function validateQuantity(e) {

    preventInputAlphabets(e);

}

function onQuantityKeyup(value) {
    if (!isNaN(parseInt(value))) {
        availableQ.value = actualAvailableQty.value - parseInt(value);

        if (availableQ.value < actualAvailableQty.value) {
            unavailableForRent.value = false;
        }

        modifyAddonsProductVariantQuantity(total.qty);
    } else if (value == "") {
        total.qty = 1;
        availableQ.value = actualAvailableQty.value - total.qty;
        modifyAddonsProductVariantQuantity(total.qty);
    }
}


function decreaseQty() {
    if (total.qty > 1) {
        if (actualAvailableQty.value <= total.qty) {
            total.qty--;
            if (total.qty < actualAvailableQty.value) {
                availableQ.value = 1;
                unavailableForRent.value = false;
            }
        } else {
            total.qty--;
            availableQ.value = availableQ.value + 1;
        }

        unavailableForRent.value = false;
        modifyAddonsProductVariantQuantity(total.qty);
        // setTotalPrice();
    }
}

function increaseQty() {
    if (parseInt(total.qty.toString()) < actualAvailableQty.value) {
        total.qty++;
        availableQ.value = actualAvailableQty.value - total.qty;

        if (availableQ.value == 0) {
            is_show_not_available_message.value = false;
        }

        modifyAddonsProductVariantQuantity(total.qty);
        // setTotalPrice();
    } else {
        total.qty++;
        availableQ.value = 0;
        unavailableForRent.value = true;
    }
}



function modifyAddonsProductVariantQuantity(quantity) {
    addonsProductList.value = addonsProductList.value.map(p => {
        let index = 0;
        p.variants.map(v => {
            if (index == 0) {
                v["min_qty"] = quantity * p.min_quantity;
            } else {
                v["min_qty"] = 0;
            }

            index++;

            return v;
        });

        return p;
    });

}

function isExactStartDate() {
    const contents = localStorage.getItem('rentmy_contents')
        ? JSON.parse(localStorage.getItem('rentmy_contents'))
        : null;
    if (
        contents.site_specific.confg &&
        contents.site_specific.confg.datetime &&
        contents.site_specific.confg.datetime.hasOwnProperty(
            "exact_start_date"
        ) &&
        contents.site_specific.confg.datetime.exact_start_date
    ) {
        return true;
    }
    return false;
}

function formatPriceDurationTerm(terms) {
    if (!terms || baseRadio.value == 1) {
        return '';
    }
    const labels = JSON.parse(localStorage.getItem('rentmy_contents')).site_specific.others;
    const lbl_for = labels.product_list_for ? (labels.product_list_for + ' ') : 'for ';
    const termsArr = terms.split(' ');
    const term_duration = termsArr[0];
    if (termsArr.length == 1) {
        return lbl_for + term_duration
    }
    const term_unit = termsArr[1];
    let unit = '';
    switch (Number(term_duration) > 1 ? term_unit.toLowerCase() + 's' : term_unit.toLowerCase()) {
        case "hour":
            unit = labels.lbl_hour ? labels.lbl_hour : 'hour';
            return lbl_for + term_duration + ' ' + unit;
        case "hours":
            unit = labels.lbl_hours ? labels.lbl_hours : 'hours';
            return lbl_for + term_duration + ' ' + unit;
        case "day":
            unit = labels.lbl_day ? labels.lbl_day : 'day';
            return lbl_for + term_duration + ' ' + unit;
        case "days":
            unit = labels.lbl_days ? labels.lbl_days : 'days';
            return lbl_for + term_duration + ' ' + unit;
        case "week":
            unit = labels.lbl_week ? labels.lbl_week : 'week';
            return lbl_for + term_duration + ' ' + unit;
        case "weeks":
            unit = labels.lbl_weeks ? labels.lbl_weeks : 'weeks';
            return lbl_for + term_duration + ' ' + unit;
        case "month":
            unit = labels.lbl_month ? labels.lbl_month : 'month';
            return lbl_for + term_duration + ' ' + unit;
        case "months":
            unit = labels.lbl_months ? labels.lbl_months : 'months';
            return lbl_for + term_duration + ' ' + unit;
    }
}

function getDurationUnit(type) {
    return formatPriceDuration(type);
}

function onChangeBuyRent(v) {
    if (v === "buy") {
        const available_quantity = details.available;
        availableQ.value = (available_quantity == 1) ? available_quantity : (available_quantity - total.qty);
        actualAvailableQty.value = available_quantity;
    } else {
        availableQ.value = (details.available == 1) ? details.available : details.available - total.qty;
        actualAvailableQty.value = details.available;
        if (hasBuyCartData) {
            isDisableDatePicker.value = false;
            buyCartGetDuration();
        }
    }
    changeBuyRent(v, is_recurring_product.value ? true : false);
}

function onChangeBuyTypeSwitch(event) {

    baseRadio.value = 1;
    let type = 'buy';
    if (event.target.checked) {
        baseRadio.value = 2;
        type = 'rent';
    }
    customFieldSelector();
    onChangeBuyRent(type);
    // }
}

function toggleRentalPriceRangePicker(priceId, price) {
  try {
    if (!isDisableDatePicker.value) {
      changeRent(priceId);
    }
  } catch (error) {
    console.warning('toggleRentalPriceRangePicker()', error);
  }
}

let startDatePicker = ref(null);
let endDatePicker = ref(null);


function toggleStartDatePicker(isOpen = false, type) {
    if (!isDisableDatePicker.value) {
        startDateFlag.value = type;

        if (isOpen) {     
            if(type == 'pick_start'){
                this.startDatePicker[0].toggle();
            }
        }

        if (type !== 'pick_start') {

            if (!rentStartDate.value && isAutoSelectEarliestDate.value) {
                rentStartDate.value = autoSelectStartDate.value
            }
            isApply.value = true;
            let date;
            date = new Date();
            if (type == 'tomorrow') date.setDate(date.getDate() + 1);

            // safari doesn't support dash(-) format
            if (rentStartDate.value.split(' ').length > 1) {
                rentStartDate.value = rentStartDate.value.split('-').join('/');
            }

            // separating time
            const dateTime = new Date(rentStartDate.value);
            date.setHours(dateTime.getHours());
            date.setMinutes(dateTime.getMinutes());

            rentStartDate.value = formatDate(date);
            selectstartDateTime(rentStartDate.value);
            callInitPrice(rentelPriceId.value);
        }

    }
}

function onClickOpenTimePicker(type) {
    // startDateFlag.value = type;
    const picker = document.querySelector('#time_picker');
    if (picker) {
        this.startDatePicker[0].toggle();
    }
}

function toISOLocal(d) {
    let offset;
    const dat = n => ('0' + n).slice(-2);
    const sec = n => ('00' + n).slice(-3);
    offset = d.getTimezoneOffset();
    const sign = offset > 0 ? '-' : '+';
    offset = Math.abs(offset);

    return d.getFullYear() + '-' + dat(d.getMonth() + 1) + '-' + dat(d.getDate()) + 'T' +
        dat(d.getHours()) + ':' +
        dat(d.getMinutes()) + ':' +
        dat(d.getSeconds()) + '.' +
        sec(d.getMilliseconds()) +
        sign + dat(offset / 60 | 0) + ':' + dat(offset % 60);
}

function toggleEndDatePicker(isOpen = false, type) {
    endDatePicker.value?.[0].toggle();
    if (!isDisableDatePicker.value) {
        endDateFlag.value = type;
    }
}

function checkRentTime(rentDate, type) {
    rentDate = datePipe(rentDate, type, { booking: product.booking });
    if (rentDate == 'Invalid date') {
        return '';
    }
    return rentDate;
}

function onClickRentalDateShowAll(list = []) {
    if (list.length < priceLimitShowFirst.value) return;
    isRentalDateShowAll.value = !isRentalDateShowAll.value;
}

function formatDate(d) {
    const dformat = [
        d.getFullYear(),
        ("0" + (d.getMonth() + 1)).slice(-2),
        ("0" + d.getDate()).slice(-2),
    ].join('-')
        + ' ' +
        [
            ("0" + d.getHours()).slice(-2),
            ("0" + d.getMinutes()).slice(-2)
        ].join(':');
    return dformat;
}
function isStandardPrpduct() {
    return contents?.site_specific?.confg?.arb?.store_active == 'standard' || !details?.enduring_rental;
}
function hasRecurringPrice() {
    if (localStorage.getItem('user_cart')) {
        let userCart = {};
        userCart = JSON.parse(localStorage.getItem('user_cart'));
        if ((userCart.options == null && !userCart.options?.recurring) && userCart.cart_items?.length) {
            isDisableDatePicker.value = false;
            isNonrecurring.value = true;
        }
        if (userCart.options && userCart.options.recurring) {
            let recurringObj = userCart.options.recurring;
            let priceObj = {};
            if (recurringObj && userCart.options.recurring) {
                priceObj = details.recurring_prices.find(el => el['duration_type'] == recurringObj['duration_type']);
                selectedRecurringPrice = priceObj || {};
                if (priceObj && Object.keys(priceObj).length > 0) {
                    isShowRecurrignWarn.value = false;
                } else {
                    isShowRecurrignWarn.value = true;
                }
            }
        }
    }
}

function checkFulfilmentOption() {
    isHideFulfillment.value = contents.site_specific?.confg?.checkout?.hide_fulfillment || false;

    // check is delivery option is enabled or not
    let isDeliveryActive = delivery_settings
        && delivery_settings.hasOwnProperty('delivery')
        && delivery_settings.delivery
        && contents?.site_specific?.confg?.delivery?.active;

    if (!isHideFulfillment.value && isDeliveryActive) {
        if (delivery_settings
            && delivery_settings.hasOwnProperty('max_delivery')
            && delivery_settings.hasOwnProperty('delivery_limit')
            && parseInt(delivery_settings.max_delivery) > 0
        ) {
            isShowFulfilmentOption.value = true;
            fulfilmentOptionList.value = [

                { id: 0, name: contents.site_specific.checkout_info.title_delivery_option || 'Delivery', value: "delivery" },
                { id: 1, name: contents.site_specific.checkout_info.title_pickup_option || 'Pickup', value: "in-store" },
                { id: 2, name: contents.site_specific.checkout_info.title_shipping_option || 'Shipping', value: "shipping" }

            ];

            if (delivery_settings
                && delivery_settings.hasOwnProperty('disable_instore_pickup')
                && delivery_settings.disable_instore_pickup
            ) {
                let index = fulfilmentOptionList.value.findIndex(el => el.value == 'in-store');
                fulfilmentOptionList.value.splice(index, 1);
            }

            if (delivery_settings
                && delivery_settings.hasOwnProperty('shipping')
                && !delivery_settings.shipping
                || !contents?.site_specific?.confg?.shipping?.active
            ) {
                let index = fulfilmentOptionList.value.findIndex(el => el.value == 'shipping');
                fulfilmentOptionList.value.splice(index, 1);
            }


            if (delivery_settings
                && delivery_settings.hasOwnProperty('delivery')
                && !delivery_settings.delivery
                || !contents?.site_specific?.confg?.delivery?.active
            ) {
                let index = fulfilmentOptionList.value.findIndex(el => el.value == 'delivery');
                fulfilmentOptionList.value.splice(index, 1);
            }

            if (fulfilmentOptionList.value.length) {
                onClickSelectFulfilment(fulfilmentOptionList.value[0]);
            }

            disabledFulfillmentOption();

            deliveryLimitWarningLbl.value = contents.site_specific.cart['delivery_limit_msg_warning'];
            deliveryLimitErrorLbl.value = contents.site_specific.cart['delivery_limit_msg_error']

        }
    }
}

function disabledFulfillmentOption() {
    let fulfilmentOption = sessionStorage.getItem('online_fullfilment_option');
    if (fulfilmentOption) {
        let index = fulfilmentOptionList.value.findIndex(el => el.value == fulfilmentOption);
        onClickSelectFulfilment(fulfilmentOptionList.value[index]);
        isDisabledFulfillmentOption.value = true;
    }

}

function selectstartDateTime(startDate) {
    if (isSeparateDate.value && !startTime.value && startDate) { // separate time but no time selected yet
        let dateTime = _getDateForNoTime(startDate);
        cart.rent_start = dateTime;
        rentStartDate.value = dateTime;
    } else {
        cart.rent_start = startDate;
        rentStartDate.value = startDate;
    }
    if (isAutoSelectEarliestDate.value || showStartTime) {
        if (isSeparateDate.value && !startTime.value && startDate) { // separate time but no time selected yet
            let dateTime = _getDateForNoTime(startDate);
            autoSelectStartDate.value = dateTime;
        } else {
            autoSelectStartDate.value = startDate;
        }
        if (extact_durations.value.length) {
            onDurationChange(extact_durations.value[0].value + ',' + extact_durations.value[0].type + ',' + extact_durations.value[0].id);
            if (extact_durations.value[0].hasOwnProperty('times') && extact_durations.value[0]['times'].length) {
                changTime(extact_durations.value[0]['times'][0])
            }
        } else {
            // if duration found then not calling no end
            _getPriceDurationWhenNoEnd();
        }
    }

}


function _setRentalDate(value = null) {

    autoSelectStartDate.value = rentStartDate.value ? rentStartDate.value : formatDate(new Date(), 'yyyy/MM/dd', 'en');;
    autoSelectEndDate.value = rentEndDate.value ? rentEndDate.value : formatDate(new Date(), 'yyyy/MM/dd', 'en');

    rentStartDate.value = value;
    rentEndDate.value = value;
    const cart = addCartObj();
    cart.rent_start = value;
    cart.rent_end = value;


    // if cart found then use cart date
    const cartList = localStorage.getItem("user_cart")
        ? JSON.parse(localStorage.getItem("user_cart"))
        : null;

    if (cartList && cartList?.cart_items && cartList?.cart_items?.length) {
        // rentStartDate.value = cartList?.rent_start;
        // rentEndDate.value = cartList?.rent_end;
        selectstartDateTime(cartList?.rent_start);
        selectendDateTime(cartList?.rent_end);

        cart.rent_start = cartList?.rent_start;
        cart.rent_end = cartList?.rent_end;
    }

    if (extact_durations.value.length) {
        onDurationChange(extact_durations.value[0].value + ',' + extact_durations.value[0].type + ',' + extact_durations.value[0].id);
        if (extact_durations.value[0].hasOwnProperty('times') && extact_durations.value[0]['times'].length) {
            changTime(extact_durations.value[0]['times'][0])
        }
    }
}

function isRentalDateNull() {
    let value = false;
    if (baseRadio.value == 2) {
        if (!rentStartDate.value || !rentEndDate.value) {
            value = true;
        }
        if (value && enable_due_date.value) {
            // if due date found then use product start_date & end_date as default 
            value = false;
        }
        let cartData = localStorage.getItem("user_cart")
            ? JSON.parse(localStorage.getItem("user_cart"))
            : null;
        if (value && cartData && cartData?.cart_items && cartData?.rent_start) {
            // if cart products found then we use cart start date
            value = false;
        }
    }
    return value;
}

function onSelectLoc(loc) { 
  let cart = JSON.parse(localStorage.getItem('user_cart'));
  if (cart?.cart_items?.length > 0) {
    showLocationAlert();
    return
  }
  let urlWithLocationId = helper.withURL.setQuery({rentmy_location: loc.id},  window.location.href, true)
  window.location.replace(urlWithLocationId)  
}

function onChangeLocation(location_id) {
    let cart = JSON.parse(localStorage.getItem('user_cart'));
    if (cart?.cart_items?.length > 0) {
        return showLocationAlert();
    }
    let locationData;
    onlineStore?.locations.forEach((location) => {
        if (location?.id == location_id) {
            locationData = location;
        }
    });
    if (locationData) {
        if (JSON.stringify(locationData) == JSON.stringify(onlineStore['location'])) {
            return;
        } else {
            onlineStore['location'] = locationData;
            localStorage.setItem('current_location', JSON.stringify(locationData));
            localStorage.setItem('current_location_id', locationData.id);
            sessionStorage.setItem('online_store', JSON.stringify(onlineStore));
            windowLocation().reload();
        }
    }
}

function showLocationAlert() {
    let cart = JSON.parse(localStorage.getItem('user_cart'));
    if (cart?.cart_items?.length > 0) {
        model_msg.value = `Finish the order before changing locations`;
        isShowModel.value = true;
    }
}

function changeFeatureImage(i) {
    featureImage.value = images.value[i].lg_feature;
}

function getPackageProductVariant() {
    const product = {
        product_id: 0,
        quantity: 0,
        variants_products_id: 0,
        product_index: 0
    };
    const InitialCartProducts = details?.products?.map((res, index) => {
        return {
            variants_products_id: res.variants[0].id,
            quantity: res.quantity,
            product_id: res.id,
            available: 1,
            product_index: index
        };
    });
    cart.products = InitialCartProducts;
    // diabledAddtoCarts.value = cart.products.every(item => item.available >= (item.quantity * total.qty));
    diabledAddtoCarts.value = details.term > 0 ? true : false;
    if (diabledAddtoCarts.value) {
        packageAvailableMessage.value = false;
    } else {
        packageAvailableMessage.value = true;
    }
    // console.log(cart);
    return cart.products;
}

function changeVariantPackage(variant, product_id, index) {

    cart.products.forEach(item => {
        if (item.product_index === index) {
            item.variants_products_id = variant.id;
        }
    });
}

function changeBuyRent(v, recurring) {
    if (v === "buy") {
        baseRadio.value = 1;
        if (!rentStartDate.value) {
            temp_rent_price.value = temp_rent_price.value ? temp_rent_price.value : total.price;
            temp_qty.value = temp_qty.value ? temp_qty.value : availableQ.value
        }
        changeOnBuy();
    } else {
        baseRadio.value = 2;
        const price = {
            id: recurring ? details.recurring_prices[0].id : prices.rent.price[0].id,
            obj: recurring ? details.recurring_prices[0] : null
        };
        changeRent(price.id, price.obj);
        selectedDuration.value = null;
        if (!rentStartDate.value) {
            // switch from buy - use previous price qty
            total.price = temp_rent_price.value ? temp_rent_price.value : total.price;
            availableQ.value = temp_qty.value ? temp_qty.value : availableQ.value;
        }
    }
}

function changeRent(v, obj) {
    termFlag.value = true; // after changing rent price option convert true to show term text
    if (details.recurring_prices && details.recurring_prices.length > 0) {
        selectedPriceObj.value = obj;
    }
    if (selectedPriceObj.value) {
        recurringPriceId.value = v;
        rentelPriceId.value = null;
    } else {
        rentelPriceId.value = v;
        recurringPriceId.value = recurringPriceId.value || null;
    }

    rentelPriceId.value = v;
    selectedCustomFields.value = selectedCustomFields.value.filter(elem => elem.applicable_for != 3);
    changeOnRent(v);
    calculateRentDate(v);
}

function calculateRentDate(v) {
    const obj = prices.rent.price.find(item => item.id === v);
    if (isDisableDatePicker.value || (!rentStartDate.value && checkEarliestDateActive)) {
        const cartDateObj = getOnlineStoreCartDate();
        if (cartDateObj?.startDate) {
            rentStartDate.value = cartDateObj.startDate;
        }
        if (cartDateObj?.endDate) {
            rentEndDate.value = cartDateObj.endDate;
        }
        let userCart = JSON.parse(localStorage.getItem('user_cart'));
        if ((userCart && userCart?.options == null && !userCart.options?.recurring)
            && userCart.cart_items?.length
            && isActiveRecurring.value
            && !isDisableDatePicker.value
            || cartDataFound.value
        ) {
            checkPrice();
        }
    } else {
        // prices.rent.price.
        const loc = locationId.value;
      
        rentStartDate.value = rentStartDate.value || obj.rent_start;
        rentEndDate.value = rentEndDate.value || obj.rent_end;



        if (!enable_exact_time.value) {
            let userCart = JSON.parse(localStorage.getItem('user_cart'));
            if ((userCart && userCart?.options == null && !userCart.options?.recurring)
                && userCart.cart_items?.length
                && isActiveRecurring.value
                && !isDisableDatePicker.value
            ) {
                checkPrice();
            } else {
                getRentDate(obj.id, loc, rentStartDate.value ? rentStartDate.value : obj.rent_start);
            }
        }        
    }
    
    min_date.value = obj?.min_date || '';
    
}

async function getRentDate(price_id, locan_id, start_date) {
    let initStartTime; // if initial load, then send false, otherwise start time
    if (initialLoad.value && !checkEarliestDateActive) {
        initStartTime = false;
    } else {
        const times = start_date.split(' ');
        initStartTime = times[1];
    }
    const contents = localStorage.getItem('rentmy_contents') ? JSON.parse(localStorage.getItem('rentmy_contents')) : null;
    if (isRentalDateOrTimeChanged.value) {
        const res = await packageService.getData(`product/get_dates_price_duration?start_date=${start_date}&start_time=${initStartTime}&price_id=${price_id}&location=${locan_id}`)
        if (res.status == "OK") {
            const data = res.result.data;
            rentStartDate.value = data.start_date;
            rentEndDate.value = data.end_date;
            termFlag.value = true; // after changing rent price option convert true to show term text
            if (initialLoad.value) {
                checkTodayTomorrow(rentStartDate.value); // check to show today/tomorrow button
            }
            if (!initialLoad.value) {
                checkPrice();
            }
            if (initialLoad.value && isAutoSelectEarliestDate.value) {
                rentStartDate.value = null;
            }
            initialLoad.value = false;
        }
    }

    if (!isRentalDateOrTimeChanged.value) {
        isRentalDateOrTimeChanged.value = true;
    }

}

function _getOnlyDate(date) {
    if (date) {
        const dateArr = date.split(' ');
        return dateArr[0];
    }
}

function checkTodayTomorrow(rentStartDate) {

    let stDate = moment(_getOnlyDate(rentStartDate), "YYYY-MM-DD");
    let today = moment(moment().format("YYYY-MM-DD"), "YYYY-MM-DD");

    if (stDate > today) {
        isShowToday.value = false;
    } else {
        isShowToday.value = true;
    }

    let tomorrow = moment(moment().add(1, 'days').format("YYYY-MM-DD"), "YYYY-MM-DD");


    if (stDate > tomorrow) {
        isShowTomorrow.value = false;
    } else {
        isShowTomorrow.value = true;
    }

    if (isAutoSelectEarliestDate.value) {
        isShowToday.value = false;
        isShowTomorrow.value = false;
    }

}

async function getRentDatesForDisableEndDate(price_id, locan_id, start_date) {
    // const result = await packageService.getData(
    //     `product/get_dates_price_duration?start_date=${start_date}&price_id=${price_id}&location=${locan_id}`
    // )
    // return result;
}


async function checkPrice(e, isApply) {
    const cart = addCartObj();
    cart['is_apply'] = false;
    if (isApply == true || isApply == true) {
        cart['is_apply'] = true;
        isApply = false;
    }
    const contents = localStorage.getItem('rentmy_contents') ? JSON.parse(localStorage.getItem('rentmy_contents')) : null;
    const showPricingOption = (contents?.site_specific?.confg?.rental_price_option) ? contents.site_specific.confg.rental_price_option : true;
    const showEndDate = getShowEndDate();
    if (showPricingOption && !showEndDate && !enable_exact_time.value) {
        const priceId = rentelPriceId.value ? rentelPriceId.value : recurringPriceId.value;
        const obj = prices.rent.price.find(item => item.id === rentelPriceId.value);
        const loc = locationId.value;
        // here check, select duration and seslect time is selected or not.
        // if (selectedDuration.value && selected_exact_time.value) {
        //     const res = await getRentDatesForDisableEndDate(obj.id, loc, rentStartDate.value)
        //     if (res.status == "OK") {
        //         const data = res.result.data;
        //         rentStartDate.value = data.start_date;
        //         rentEndDate.value = data.end_date;

        //         // if product option field value change then need to call
        //         if (productOptionValueCheck.value || selectedCustomFields.value.length) {
        //             cart.rent_start = data.start_date;
        //             cart.rent_end = data.end_date;
        //             productOptionValueCheck.value = true;
        //             getPriceValue(cart);
        //         }
        //     }
        // }
    }

    if ((enable_exact_time.value && !productOptionValueCheck.value) && !checkEarliestDateActive) {
        const sendData = {
            start_date: cart.rent_start ? cart.rent_start.split(" ")[0] : null
        };
        const res = await packageService.postData("product/get_exact_duration", sendData)
        if (res.status === "OK") {
            if (res.result.error) {
                Toaster().error(res.result.error);
                return;
            }

            if (res.result.data) {
                const exact_durations = res.result.data;
                extact_durations.value = exact_durations.durations;
                extact_times.value = exact_durations.times;
                // Est options reset
                selected_exact_time.value = null;
                selectedDuration.value = null;
                unavailableWhenExactStartTime.value = true;
            } else {
                unavailableForRent.value = true;
                extact_times.value = [];
            }
        }

    } else {
        const showStartTime = (enable_exact_time.value && !isDisableDatePicker.value) ? false :
            (contents?.site_specific?.confg?.show_start_time ? contents.site_specific.confg.show_start_time : true);
        let result = checkSameDatetime(showStartTime, cart.rent_start, cart.rent_end);
        result = false;
        if (result) {
            diabledAddtoCarts.value = false;
            Toaster().error("Please select correct end date");
        } else {
            if (cart.rental_type == "rent" && (!cart.rent_start || cart.rent_start == 'null')) return;
            const rentEnd = cart.rent_start.split(" ");
            rentEnd[1] = "23:59";
            const rentalEndFordisabledEndDate = rentEnd.join(" ");
            let rent_end_date;
            if (!showEndDate) {
                rent_end_date = showPricingOption ? rentEndDate.value : rentalEndFordisabledEndDate;
            }
            cart.rent_end = showEndDate ? cart.rent_end : rent_end_date;
            getPriceValue(cart);
        }
    }
}

async function getPriceValue(cart) {
    cart.custom_fields = selectedCustomFields.value;
    if (isActiveRecurring.value && Object.keys(selectedRecurringPrice).length > 0) {
        cart["price_id"] = selectedRecurringPrice.id;
    }
    const priceId = cart.price_id;
    if(isMounted.value) globalLoader.show();
    // rentEndDate.value = cart.rent_end;
    baseRadio.value == 1 ? cart['rental_type'] = 'buy' : cart['rental_type'] = 'rent';
    if (cart.rental_type == "rent" && ((!cart.rent_start || cart.rent_start == null || cart.rent_start == 'null') || !cart.products || (!cart.rent_end || cart.rent_end == null))) return;
    const res = await packageService.postData("get-package-price", cart)
    if (res.status === "OK") {
        globalLoader.hide();
        if (res.result.errors) {
            Toaster().error(res.result.errors);
            unavailableForRent.value = true;
            // total.price = 0;
            availableQ.value = 0;
            actualAvailableQty.value = 0;
            return;
        }
        total.term = '';
        // update term after price value
        if (termFlag.value || productOptionValueCheck.value) {
            const rentPrice = prices.rent.price.find(item => item.id === priceId);
            if (rentPrice?.duration && rentPrice?.label && !selectedDuration.value && !selected_exact_time.value) {
                total.term = `${rentPrice.duration} ${rentPrice.label} `;
            }
            termFlag.value = false;
            productOptionValueCheck.value = false;
        }

        details.term = res.result.term;
        availableQ.value = (res.result.term == 1) ? res.result.term : res.result.term - total.qty;
        actualAvailableQty.value = res.result.term;

        diabledAddtoCarts.value = availableQ.value > 0 ? true : false;
        packageAvailableMessage.value = diabledAddtoCarts.value ? false : true;

        details.products = res.result.products;
        total.price = res.result.data;

        unavailableForRent.value = (availableQ.value <= 0) ? true : false;
        const cart = addCartObj();
        cart.price = total.price;
        // (availableQ.value <= 0 && unavailableForRent.value) || !diabledAddtoCarts.value || rentEndDate.value ==''"

        // following 3 conditions are: if start/end time is before/after open/closed time
        // end_date, start_date and message; for force end date
        if (res.result?.start_date) {
            cart.rent_start = res.result.start_date;
            rentStartDate.value = res.result.start_date;
        }
        if (res.result?.end_date) {
            cart.rent_end = res.result.end_date;
            rentEndDate.value = res.result.end_date;
        }
        endDateErrorMessage.value = '';
        if (res.result?.message && typeof res.result.message == 'string') {
            endDateErrorMessage.value = res.result.message;
        }
        globalLoader.hide();
    }

}

function getShowEndDate() {
    const contents = localStorage.getItem('rentmy_contents') ? JSON.parse(localStorage.getItem('rentmy_contents')) : null;
    if (enable_exact_time.value && !isDisableDatePicker.value) {
        return false;
    } else if (contents.site_specific.confg && contents.site_specific.confg.hasOwnProperty("show_end_date")) {
        return contents.site_specific.confg.show_end_date;
    }
    return true;
}

function getQtyFromCart(date, duration) {

    const cartList = localStorage.getItem("user_cart")
        ? JSON.parse(localStorage.getItem("user_cart"))
        : null;
    const cartQty = cartList
        ? getQtyFromCartList(cartList.cart_items, details, date, duration)
        : 0;
    const availablesQty = details.products_availabilities
        ? getQtyFromProductList(
            details.products_availabilities,
            date,
            duration
        )
        : 0;
    return cartQty + availablesQty;


}

function checkAvailableForType() {
    if (cart.rental_type == "hourly") {
        return getQtyFromCart(cart.rent_start, 1);
    } else if (cart.rental_type == "weekly") {
        return getQtyFromCart(cart.rent_start, +cart.term * 7);
    } else if (cart.rental_type == "daily") {
        return getQtyFromCart(cart.rent_start, +cart.term);
    }
    return getQtyFromCart(new Date(), 14);
}


function callInitPrice(rentalPriceid) {
    initPrice(rentalPriceid);
}

function initPrice(priceId = null) {
    if (prices && Object.keys(prices).length > 0) {
        if (prices.rent.type) {
            baseRadio.value = 2;
            if (!recurringPriceId.value) {
                const recurring = is_recurring_product.value ? true : false;
                const price = {
                    id: recurring ? details.recurring_prices[0].id : prices.rent.price[0].id,
                    obj: recurring ? details.recurring_prices[0] : null
                };
                priceId = priceId ? priceId : price.id;
                changeRent(price.id, price.obj);
            } else {
                priceId = priceId ? priceId : prices.rent.price[0].id;
                changeRent(priceId);
            }

            // Est options reset, if is exact time enable
            if (details.hasOwnProperty('exact_time') && details.exact_time && !isAutoSelectEarliestDate.value) {
                selectedDuration.value = null;
                selected_exact_time.value = null;
                unavailableWhenExactStartTime.value = true;
            }

        } else if (prices.buy.type) {
            if (baseRadio.value && baseRadio.value === 2 && prices.rent.type) {
                baseRadio.value = 2;
                changeRent(prices.rent.price[0].id);
            } else {
                baseRadio.value = 1;
                changeOnBuy();
            }
        }
    } else {
        total.qty = 0;
    }
}

function imageFormat() {
    if (details && details.images.length > 0) {
        images.value = details.images
            .sort((a, b) => {
                return b.status - a.status;
            })
            .map(m => {
                m["lg_feature"] = `${product_image}${store.value}/${details.id}/${m.image_large
                    }`;
                m["sm_feature"] = `${product_image}${store.value}/${details.id}/${m.image_small
                    }`;
                return m;
            });

        featureImage.value = images.value[0].lg_feature;
    }
}

function setTotalPrice() {
    if (isDisableDatePicker.value && !cartDataFound.value) {
        total.price = details.rental_price;
        total.term = '';
    }
    else {
        total.price = cart.price;
        // total.term = cart.term;
    }
}

function SelectRentalDate(e) {
    // console.log(e)
    cart.rent_start = e;
    checkAvailabe();
}
function SelectRentaEndlDate(e) {
    // console.log('p'+e);
    cart.rent_end = e;
}

function getTime() {
    let now = new Date();
    let hour = ("0" + now.getHours()).slice(-2);
    let min = ("0" + now.getMinutes()).slice(-2);
    return " " + hour + ":" + min + ":00";
}

function changeOnRent(id) {
    try {
        const rentObj = prices.rent.price.find(f => f.id === id);
        if (rentObj.duration && rentObj.label) {
            total.term = `${rentObj.duration} ${rentObj.label} `;
        }
    
        cart.price_id = rentObj.id;
    
        if (!selectedCustomFields.value.length) { // ignore updating price here when custom field selected
            cart.price = rentObj.price;
        }
    
        // cart.rental_type = rentObj.type;
        cart.rental_type = 'rent';
        cart.term = rentObj.duration;
        cart.rent_start = rentObj.rent_start;
        cart.rental_duration = 1;
        // cart.deposit_amount = details.deposit_amount;
        // cart.deposite_tax = details.deposite_tax;
        // cart.driving_license_required = details.driving_license;
    
        // rentStartDate.value=cart.rent_start;
        checkAvailabe();
        
    } catch (error) {
        console.warn('line:2117', error);
        
    }
}

function changeOnBuy() {
    cart.price_id = prices.buy.id;

    if (!selectedCustomFields.value.length) { // ignore updating price here when custom field selected
        cart.price = prices.buy.price;
    }

    cart.rent_start = "";
    cart.rent_end = "";
    cart.rental_duration = 0;
    cart.rental_type = "buy";
    cart.term = "";
    selectedCustomFields.value = selectedCustomFields.value.filter(elem => elem.applicable_for != 2);
    checkAvailabe();
    total.term = "";
    getPriceValue(cart);
}

function formatBuyRent(data = []) {
    if (data?.length > 0) {
        let prices = data[0];
        let obj = {
            buy: { type: false, price: 0, id: null },
            rent: { type: false, price: [] }
        };
        let rent = ["hourly", "daily", "weekly", "monthly"];
        if (prices.base.price > 0) {
            obj.buy["type"] = true;
            obj.buy["price"] = prices.base.price;
            obj.buy["id"] = prices.base.id;
        }
        const rentPrices = data[0];

        if (rentPrices.fixed) {
            const fp = {
                type: "",
                price: rentPrices.fixed.price,
                id: rentPrices.fixed.id,
                label: "",
                rent_start: rentPrices.fixed.rent_start,
                rent_end: rentPrices.fixed.rent_end
            };
            obj.rent["price"].push(fp);
        } else {
            for (let c in rentPrices) {
                for (let i = 0; i < rentPrices[c].length; i++) {
                    rentPrices[c][i]["type"] = rentPrices[c][i].label;
                    obj.rent["price"].push(rentPrices[c][i]);
                }
            }
        }
        if (obj.rent["price"].length > 0) {
            obj.rent["type"] = true;
        }
        // console.log(obj);
        return obj;
    }
    return prices;
}

function getType(key, t) {
    return formateRentType(key, t);
}

function changeVariantCall(pos, id) {
    let chain = "";
    if (parseInt(id) > 0) {
        if (parseInt(pos) > 0) {
            chain = $(".select-variant .variant-chain-select")
                .map(function () {
                    return jQuery(this).val();
                })
                .get()
                .slice(0, -1)
                .join(",");
        }
        // console.log(id, chain)
        for (let a of details.variant_set_list[pos]["variants"]) {
            if (a.id == parseInt(id)) {
                a.selected = true;
            } else {
                a.selected = false;
            }
        }
        if (pos + 1 === details.variant_set_list.length) {
            return { id: id, chain: chain };
        } else {
            return { id: id, chain: chain, pos: pos };
        }
    }
}

function formatVariant() {
    if (details.variant_set_list) {
        if (details.variant_set_list.length > 0) {
            details.variant_set_list = details.variant_set_list.map(
                function (f) {
                    f["variants"] = [];
                    return f;
                }
            );
            details.variant_list.sort((a, b) => (a.selected ? -1 : 0));
            formatVariantSetList(details.variant_list);
        }
    }
}

function formatVariantSetList(vValue) {
    for (const i of vValue) {
        const ind = details.variant_set_list.findIndex(
            f => f.id === i.variant_set_id
        );
        if (ind > -1) {
            details.variant_set_list[ind]["variants"].push(i);
        }
    }
}

function formatVariantList(pos) {
    for (let j = 0; j < details.variant_set_list.length; j++) {
        if (j > pos) {
            details.variant_set_list[j]["variants"] = [];
        }
    }
}

function variantChainRes(res, data) {
    formatVariantList(data.pos);
    // res.splice(0, 0, {
    //   id: 0,
    //   name: "-Select One-",
    //   variant_set_id: details.variant_set_list[data.pos + 1].id
    // });
    formatVariantSetList(res);
}

function getLastvariantChainCall(res) {
    details.images = res.images;
    details.prices = res.prices;
    details.default_variant = res.variant;
    details.default_variant.variants_products_id = res.variant.id
        ? res.variant.id
        : details.default_variant.variants_products_id;
    details.products_availabilities = res.products_availabilities;
    // imageFormat();
    let byRentPrices = formatBuyRent(details.price);
    prices.buy = byRentPrices.buy;
    prices.rent = byRentPrices.rent;
    initPrice();
}

function addCartObj() {

    if (cart.rental_type !== "buy") {
        cart.rental_type = "rent";
        cart.rent_start = rentStartDate.value;
        cart.rent_end = rentEndDate.value;
    }
    cart.package_id = details.id;
    cart.quantity = total.qty;
    cart.location = locationId.value;

    // if(product.booking){
    //     cart.booking = true
    //     cart.quantity = actualAvailableQtyForBooking
    //     cart.exact_times = productDetailsStore?.exactTimes?.times?.length ? productDetailsStore.exactTimes.times.filter(time => time.isActive)?.[0] : null
    // } else {
    //     delete cart.exact_times 
    //     cart.booking = false
    //     cart.quantity = total.qty;
    // }
    
    const token = localStorage.getItem("token");
    cart.token = token ? token : "";
    if (cart.token === "") {
        localStorage.removeItem("user_cart");
    }
    cart.variants_products_id = details.variants_products_id;
    return cart;

}

function getDate() {
    let now = new Date();
    let day = ("0" + now.getDate()).slice(-2);
    let month = ("0" + (now.getMonth() + 1)).slice(-2);

    let today = now.getFullYear() + "-" + month + "-" + day + getTime();
    return today;
}

function diabledAddtoCart() {
    const chain = $(".select-variant .variant-chain-select")
        .map(function () {
            return jQuery(this).val();
        })
        .get();

    if (!chain.includes("0") && total.qty > 0) {
        return true;
    }
    return false;
}

function getTimeFromManualChange(e, startDateFlag = "") {
    if (e.startTime) {
        startTime.value = e.startTime;
        getDateFromManualChange(e, startDateFlag);
    }
}
function getDateFromManualChange(e, startDateFlag = "") {
    if (e.isSingleEndDate) {
        selectendDateTime(e.startDate);
    } else {
        if (e.startDate) {

            if (isSeparateDate.value && startTime.value) {
                let date = e.startDate.split(' ')[0]; //only date
                e.startDate = date + ' ' + startTime.value;
            } else if (isSeparateDate.value && !startTime.value) {
                e.startDate = _getDateForNoTime(e.startDate);
            }
            selectstartDateTime(e.startDate);
            // if new product design pick start date
            if (startDateFlag.value) {
                isApply.value = true;
                initPrice(rentelPriceId.value);
            }
        } else if (e.endDate) {
            selectendDateTime(e.endDate);
        }
    }
}

// function selectstartDateTime(e) {
//     if (isSeparateDate.value && !startTime.value && e) { // separate time but no time selected yet
//         let dateTime = _getDateForNoTime(e);
//         cart.rent_start = dateTime;
//         rentStartDate.value = dateTime;
//     } else {
//         cart.rent_start = e;
//         rentStartDate.value = e;
//     }

// }

function _getPriceDurationWhenNoEnd() {
    const priceId = rentelPriceId.value ? rentelPriceId.value : recurringPriceId.value;
    const obj = prices.rent.price.find(item => item.id === priceId);
    const loc = locationId.value;
    // isRentalDateOrTimeChanged.value = false;
    getRentDate(obj.id, loc, rentStartDate.value);

}

function _getDateForNoTime(e) {
    let time = rentStartDate.value ? rentStartDate.value.split(' ')[1] : '00:00';
    if (isAutoSelectEarliestDate.value) time = autoSelectStartDate.value ? autoSelectStartDate.value.split(' ')[1] : '00:00';
    let date = e.split(' ')[0];
    return date + ' ' + time;
}

function selectendDateTime(endDate) {
    if (!enable_exact_time.value || isDisableDatePicker.value) {
        cart.rent_end = endDate;
        rentEndDate.value = endDate;
    }
    else {
        rentEndDate.value = '';
    }


}
function checkEarliestDateActive() {
    const storeContent = localStorage.getItem('rentmy_contents') ? JSON.parse(localStorage.getItem('rentmy_contents')) : null;
    const isModernLayout = storeContent?.site_specific?.confg?.inventory?.display_mode == 'modern' ? true : false;
    if (isModernLayout) {
        if (storeContent?.site_specific?.confg?.hasOwnProperty('show_earliest_start_date')) {
            if (!storeContent.site_specific.confg.show_earliest_start_date) {
                isAutoSelectEarliestDate.value = true;
            } else {
                isAutoSelectEarliestDate.value = false;
            }
        } else {
            isAutoSelectEarliestDate.value = false;
        }

        return isAutoSelectEarliestDate.value;
    }
    return false;
}
function _getRentalDate(value = null) {
    autoSelectStartDate.value = rentStartDate.value ? rentStartDate.value : new Date();
    autoSelectEndDate.value = rentEndDate.value ? rentEndDate.value : new Date();

    rentStartDate.value = value;
    rentEndDate.value = value;
    const cart = addCartObj();
    cart.rent_start = value;
    cart.rent_end = value;

    // if cart found then use cart date
    const cartList = localStorage.getItem("user_cart")
        ? JSON.parse(localStorage.getItem("user_cart"))
        : null;

    if (cartList && cartList?.cart_items && cartList?.cart_items?.length) {
        rentStartDate.value = cartList?.rent_start;
        rentEndDate.value = cartList?.rent_end;
        cart.rent_start = cartList?.rent_start;
        cart.rent_end = cartList?.rent_end;
    }
}

function getAvailableList(date) {

    // if super admin config and admin config
    const storeContent = localStorage.getItem('rentmy_contents') ? JSON.parse(localStorage.getItem('rentmy_contents')) : null;
    const show_daily_availability_super = storeContent?.site_specific?.confg?.inventory?.availability_calendar?.package?.active ? true : false;
    const show_daily_availability = storeContent?.site_specific?.confg?.inventory?.availability_calendar?.package?.daily ? true : false;
    if (!show_daily_availability_super || !show_daily_availability) return;

    // if date not from calender then init this month
    if (!date) {
        date = {};
        date['start_date'] = moment(min_date.value ? new Date(min_date.value) : new Date()).format('YYYY-MM-DD');
        date['end_date'] = moment(min_date.value ? new Date(min_date.value) : new Date()).endOf('month').format('YYYY-MM-DD');
    } else {
        date['start_date'] = moment(date['start_date']).format('YYYY-MM-DD');
        date['end_date'] = moment(date['end_date']).format('YYYY-MM-DD');
    }

    const location_id = locationId.value;
    const product_id = details.id;
    let data = {
        location: location_id,
        package_id: product_id,
        package_products: package_products.value,
        variants_products_id: variants_products_id,
        start_date: date.start_date,
        end_date: date.end_date
    }
    availableList.value = [];
    if(isMounted.value) globalLoader.show();
    getAllPackageData(data, getAllEndDate(date));

}

function getAllEndDate(date) {
    let all_endDate = [];
    const days = 7;
    let start_date = new Date(date.start_date);
    let end_date = new Date(date.end_date);
    let mainEnd = new Date(date.end_date);
    while (start_date < mainEnd) {
        end_date = new Date(start_date.setDate(start_date.getDate() + (days - 1)));
        if (end_date > mainEnd) {
            end_date = mainEnd
        }
        all_endDate.push(moment(end_date).format('YYYY-MM-DD'));
    }
    return all_endDate;
}

async function getAllPackageData(info, dates = []) {
    info['end_date'] = dates[0];
    if (dates.length) {
        dates.shift();
        const res = await packageService.getCalenderPackageData(info)
        if (res.status == 'OK') {
            res.result.data.available.forEach((data) => {
                tempAvailableList.value.push({
                    date: new Date(data.date),
                    color: data.available > 0 ? '#a7ffa4' : '#fff',
                    title: data.available
                });
                if (data.available == 0) {
                    tempInvalid.value.push(data.date);
                }
            });
            info['start_date'] = moment(info['end_date']).add(1, 'day').format('YYYY-MM-DD');
            getAllPackageData(info, dates);
        } else {
            globalLoader.hide();
        }

    } else {
        globalLoader.hide();
        availableList.value = tempAvailableList.value;
        invalid.value = tempInvalid.value;
        tempAvailableList.value = [];
        tempInvalid.value = [];
    }updateVariantData

}

function updateVariantData(pacakgeInfo) {

    product_list.value = pacakgeInfo.products;
    if(product_list.value?.length){
        product_list.value.forEach(product => {
            let product_id = product.id;
            let default_variants_products_id = product.variants[0].id;
            package_products.value.push({
                product_id: product_id,
                variants_products_id: default_variants_products_id
            })
            // product.variants = product.variants.filter((variant)=> variant.variant_chain != 'Unassigned: Unassigned');
            // if(product.variants.length) {
            //   isVariantsFound.value = true;
            // }
        });
        variants_products_id.value = pacakgeInfo.variants_products_id;
    }
}

function buyCartGetDuration() {
    const cartList = localStorage.getItem("user_cart")
        ? JSON.parse(localStorage.getItem("user_cart"))
        : null;
    if (cartList?.rent_start) return;

    const priceId = rentelPriceId.value ? rentelPriceId.value : recurringPriceId.value;
    const obj = prices.rent.price.find(item => item.id === priceId);
    const loc = RENTMY_GLOBAL.locationId;
    // const res = packageService.getData(
    //     `product/get_dates_price_duration?start_date=${min_date.value}&price_id=${priceId}&location=${loc}`
    // )
    // if (res && res.status == 'OK') {
    //     const data = res.result.data;
    //     rentStartDate.value = data.start_date;
    //     rentEndDate.value = data.end_date;
    //     if (isAutoSelectEarliestDate.value) {
    //         autoSelectStartDate.value = rentStartDate.value;
    //         rentStartDate.value = null;
    //     }
    // }

}

function hasBuyCartData() {
    let status = false;
    let userCart = JSON.parse(localStorage.getItem('user_cart'));
    if (userCart && !userCart?.rent_end) {
        status = true;
    }
    return status;
}


/* -------------------------------------------------------------------------- */
/*                                 COMPONENTS                                 */
/* -------------------------------------------------------------------------- */

const RentmyPackageComponent = wrapper.querySelector('[RentmyPackageComponent]');
const RentMyProductPackageArea = wrapper.querySelector('[RentMyProductPackageArea]');


createComponentHTML(wrapper, [
    {
        selector: '[debug]',
        attr: { '@click': `log('debug')` },
    }
]);


function formatPricingDom() {
    // product name, pricing, buy rent switching
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:general', [
        {
            selector: '[RentMyProductName]',
            text: "{{ details.name }}",
        }, 
        {
            selector: '[RentMyProductPrice]',
            text: `{{ currencyConvert((total.price + customFieldPrices) * total.qty) }}
                <span v-if="total.term && !enable_due_date && showPricingOption" class="price-labeler">{{formatPriceDurationTerm(total.term)}}</span>
            `,
        },
        {
            selector: '[RentMyBuyRentToggle]',
            attr: {
                'v-if': 'prices?.rent?.type && prices?.buy?.type'
            }
        },
        {
            selector: '[BuyRentToggleSwitch]',
            attr: {
                ':checked': 'baseRadio == 2',
                'v-on:click': 'onChangeBuyTypeSwitch($event)'
            }
        }
    ]));

    // recurring section funtionality
    // selector: [RentMyRecurring]
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentMyRecurring]', [
        {
            selector: '[RentMyRecurring]',
            template: true,
            attr: { 'v-if': 'is_recurring_product && isActiveRecurring' },
            child: [
                {
                    selector: '__SELF__',
                    template: true,
                    attr: { 'v-if': 'baseRadio == 2 && showPricingOption() && isShowPricingForExactTimeItem' },
                    child: [
                        {
                            selector: '__SELF__',
                            template: true,
                            attr: { 'v-if': '(!enable_due_date && (!enable_exact_time || isDisableDatePicker))' },
                            child: [
                                {
                                    selector: '[RecurringTitle]',
                                    text: `
                                        {{contents?.site_specific?.product_details?.lbl_recurring_pricing}}
                                        <a style="cursor: pointer;"
                                            v-if="!isRentalDateShowAll && details?.recurring_prices?.length > priceLimitShowFirst"
                                            @click="onClickRentalDateShowAll(details.recurring_prices)">
                                            (Show All)
                                        </a>
                                    `,
                                },
                                {
                                    selector: '[RecurringItem]',
                                    template: true,
                                    attr: { 'v-for': '(item, index) in details?.recurring_prices' },
                                },
                                {
                                    selector: '[RecurringItem]',
                                    attr: {
                                        'v-if': '(index <= priceLimitShowFirst && !isRentalDateShowAll) || isRentalDateShowAll',
                                        ':class': "{'RecurringActive': recurringPriceId == item?.id && !isDisableDatePicker, 'disabled-cursor': isDisableDatePicker}",
                                        'v-on:click': "toggleRentalPriceRangePicker(item.id, item)",
                                    },
                                    text: `
                                        {{ contents?.site_specific?.product_details?.billed_at_a_rate ?
                                            contents?.site_specific?.product_details.billed_at_a_rate : "Billed at a rate of"
                                        }}
                                        {{ currencyConvert(item?.price) }}
                                        <template v-if="item?.duration > 1">
                                            for {{ item?.duration }}
                                            {{ item?.duration_type =="monthly" ?
                                            contents?.site_specific?.others?.lbl_months || "Months"
                                            : contents?.site_specific?.others?.lbl_weeks || "Weeks"}}
                                        </template>
                                        <template v-else>
                                            per
                                            {{ item?.duration_type =="monthly" ?
                                            contents?.site_specific?.others?.lbl_month || "Month"
                                            : contents?.site_specific?.others?.lbl_week || "Week"}}
                                        </template>
                                    `,
                                }
                            ]
                        }
                    ]
                }
            ]
        },       
    ]));

    // varient section funtionality
    // selector: [RentMyVariant]'
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentMyVariant]', [
        {
            selector: '[RentMyVariant]',
            attr: { 'v-if': 'details?.variant_set_list?.length > 0' },
            child: [
                {
                    selector: '[VariantTitle]',
                    attr: {
                        'v-if': 'vs?.variants?.length'
                    },
                    text: "{{ vs?.name }} <span style='color: red'>*</span>",
                },
                {
                    selector: '[VariantList]',
                    template: true,
                    attr: { 
                        'v-for': '(item, index) in vs?.variants', 
                        ':class': "{'color-variants': isShowColorVariant && vs?.name?.toLowerCase() == 'color' }" 
                    },
                    child: [
                        {
                            selector: '__SELF__',
                            template: true,
                            attr: { 'v-if': "vs.id !== 1 && vs.name !== 'Unassigned'" },
                            child: [
                                {
                                    selector: '[VariantItem]',
                                    template: true,
                                    attr: { 'v-for': '(item, index) in vs?.variants' },
                                    child: [
                                        {
                                            selector: '__SELFT__',
                                            attr: { 
                                                'v-if': 'item?.id',
                                                ':class': "{'VariantActive': item?.selected}",
                                                '@click': "changeVariant(i, item.id)",
                                                ':style': "{ 'background-color': isShowColorVariant && vs?.name?.toLowerCase() == 'color' ? item?.name?.toLowerCase() : '' }",
                                                text: "{{ isShowColorVariant && vs?.name?.toLowerCase() == 'color' ? '' : item?.name }}",
                                            },

                                        },                
                                    ],
                                },
                            ],
                        }
                    ],
                },
            ],   
        },
       
        
    ]));



    // Product options
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentMyProductOptions]', [
    {
        selector: '[RentMyProductOptions]',
        template: true,
        attr: { 'v-if': `(customSelectFieldList || []).filter(cf => cf.type !== 'rich_text_box')?.length` },
        child: [
            {
                selector: '[ProductOptionsItem]',
                template: true,
                attr: { 'v-for': `(customSelectField, index) in customSelectFieldList.filter(cf => cf.type !== 'rich_text_box')` },
                child: [
                  {
                    selector: '__SELF__',
                    template: true,
                    attr: {
                      'v-if': `customSelectField.applicable_for == 1 ||
                          (customSelectField.applicable_for == 2 && baseRadio == 2) ||
                          (customSelectField.applicable_for == 3 && baseRadio == 1)` 
                    },
                    child: [  
                       {
                        selector: '[ProductOptionsTitle]',
                        attr: { 
                            'v-if': "customSelectField?.type != 'predefined_radio'",
                            '@click': 'log({customSelectField, customSelectFieldList, "display_format": customSelectField?.display_format})'                                                
                          },
                        text: "{{ customSelectField?.label }}",
                      },

                      /* -------------------------------------------------------------------------- */
                      /*                                 type-select                                */
                      /* -------------------------------------------------------------------------- */
                      {
                        selector: '[type-select]',
                        template: true,
                        attr: {
                          "v-if": "customSelectField?.display_format != 'button' && customSelectField?.type === 'select'",
                        },
                        child: [
                          {
                            selector: '[fieldLabel]',
                            text: '{{ customSelectField?.label }}'
                          },
                          {
                            selector: 'select',
                            attr: {
                              "v-model": "customSelectField.selectedValue",
                              ":dispaly_format": "customSelectField?.display_format",
                              ":field_type": "customSelectField?.type",
                              "@change": "onFieldValueChange($event.target.value, customSelectField)",                              
                            },
                            text: `
                              <template v-for="(field, index2) in customSelectField?.product_field_value || []">
                                  <option :value="field?.id">{{ field?.showValue }}</option>
                              </template>
                            `
                          },
                        ],
                      },
                                   
                      /* -------------------------------------------------------------------------- */
                      /*                                 type-button                                */
                      /* -------------------------------------------------------------------------- */
                      {
                        selector: '[type-button]',
                        template: true,
                        attr: {
                          // "v-if": "customSelectField?.display_format != 'button' && customSelectField?.type === 'predefined_radio'",
                          "v-if": "customSelectField?.display_format == 'button' && customSelectField?.type === 'select'",
                        },
                        child: [
                          {
                            selector: '[fieldLabel]',
                            text: '{{ customSelectField?.label }}'
                          },
                          {
                            selector: '[fieldValue]',
                            attr: {
                              'v-for': '(field, index2) in customSelectField?.product_field_value || []',
                              '@contextmenu': 'log(field, customSelectField?.product_field_value)',
                              '@click.stop': 'onFieldValueChange(field?.id, customSelectField);helper.toggleLoopItem(customSelectField?.product_field_value, -1);helper.toggleLoopItem(customSelectField?.product_field_value, index2)',
                              ':class': `{'active': field?.isShow}`
                            },
                            text: `{{ field?.showValue }} `
                          }
                        ],
                        
                      },
                      /* -------------------------------------------------------------------------- */
                      /*                                 type-richtext                              */
                      /* -------------------------------------------------------------------------- */
                      {
                        selector: '[type-richtext]',
                        template: true,
                        attr: {
                          "v-if": "customSelectField?.type === 'rich_text_box' && false", // rich text never show in product options
                        },
                        child: [
                          {
                            selector: '[fieldLabel]',
                            text: '{{ customSelectField?.label }}'
                          },                          
                          {
                            selector: '[fieldValue]',
                            text: '<div v-html="customSelectField?.field_value" ></div>'
                          },                      
                        ],
                        
                      },
                    ], 
                  }
                ],
                  
            },
        ]
    },
    ]));


    // RENTAL DATES ERROR MESSAGE
    createComponentHTML(wrapper, [
        {
            selector: '[RentmyAvailableNotice]',
            text: `
                <div v-if="endDateErrorMessage">
                    <small class="text-danger"> {{endDateErrorMessage}} </small>
                </div>
                
                <div
                    v-if="actualAvailableQty <= 0 && contents.site_specific.confg.checkout?.multiple_location">
                    <small class="text-danger">
                        {{contents.site_specific.product_details?.lbl_select_location}}
                    </small>
                </div>

                <div v-if="availableQ <= 0 && is_show_not_available_message && !contents.site_specific.confg.checkout?.multiple_location">
                    <small style="color: red;">
                        {{contents.site_specific.product_details &&
                        contents.site_specific.product_details.not_available_text ?
                        contents.site_specific.product_details.not_available_text : 'This product is not available.'}}
                    </small>
                </div>
                <div v-if="unavailableForRent">
                    <small style="color: red;">{{contents?.site_specific?.custom?.msg_selected_quantity_is_not_available ? contents?.site_specific?.custom?.msg_selected_quantity_is_not_available : 'Selected quantity is not available'}}</small>
                </div>
            `,
        },
    ]);


    // FULLFILMENT OPTION SELECT
    // selector: [RentMyDeliveryOptions]
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentMyProductDetilsInfo]fulfilment_option_select', [
        {
            selector: '[RentMyDeliveryOptions]',
            template: true,
            attr: { 'v-if': 'isShowFulfilmentOption && fulfilmentOptionList?.length > 1' },
            child: [
                {
                    selector: '[DeliveryOptionsTitle]',
                    text: `{{ contents?.site_specific?.product_details?.lbl_fulfillment_option }}`,
                },
                {
                    selector: '[DeliveryOptionsItem]',
                    attr: {
                        ':class': `{'StartDateActive': fullfilment_type == item.id, 'disabled-cursor': isDisabledFulfillmentOption}`,
                        'v-for': `(item,i) in fulfilmentOptionList`,
                        '@click': "onClickSelectFulfilment(item)"
                    },
                    text: `{{ item?.name }}`,
                },
            ]
        },       
    ]));




    // RENTAL DATE RANGE SELECTION
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentMyProductDetilsInfo]rental_date_range_selection', [
        {
        selector: '[RentMyRentalStartDate]',
        child: [
            {
                selector: '[RentalStartDateTitle]',
                attr: {
                    'v-if': `
                    (isShowToday 
                    || isShowTomorrow
                    || showStartDate()
                    || checkRentTime(rentStartDate, 'start'))
                    && (!SHOWING_START_AND_END_DATE)
                    `            
                },
                text: `{{ contents?.site_specific?.product_details?.lbl_rental_start_date }}`,
                },
                {
                selector: '[RentalStartDateSelectedLabel]',
                attr: {            
                    'v-if': `
                    (isShowToday 
                    || isShowTomorrow
                    || showStartDate()
                    || checkRentTime(rentStartDate, 'start'))
                    && (!SHOWING_START_AND_END_DATE)
                    `
                },
                text: `
                <span v-if="checkRentTime(rentStartDate, 'start')">
                    <i v-if="checkRentTime(rentStartDate, 'start')" class="fa fa-calendar me-1"></i> 

                    <template v-if="startDate_useRangePicker">
                        {{ checkRentTime(rentStartDate, 'start')}} - {{ checkRentTime(rentEndDate, 'end') }}
                    </template>
                    <template v-else>
                        {{ checkRentTime(rentStartDate, 'start')}}
                    </template>

                    <template v-if="false">
                        <span v-if="product.booking === false" class="timepicker-icon"
                            :class="{'startdate-active': isDisableDatePicker, 'disabled-cursor': isDisableDatePicker, 'timepicker-disable' : isDisableDatePicker}"
                            v-if="isSeparateDate && showStartTime && checkRentTime(rentStartDate, 'start')"
                            >
                            <i class="fa fa-clock-o pl-2"></i>
                            <div DateTimePicker>
                                <div StartTimePicker id="time_picker"></div> 
                            </div>
                        </span>
                    </template>
                </span>
                `
                },
                {
                selector: '[Today]',
                attr: {
                    'v-if': "isSameDateBooking && isShowToday",
                    ':class': "{'startdate-active': (startDateFlag == 'today' && !isDisableDatePicker) && !isNonrecurring, 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
                    '@click.stop': "toggleStartDatePicker(false, 'today')"
                },
                text: `{{ contents?.site_specific?.product_details?.lbl_today ? contents.site_specific.product_details.lbl_today : "Today" }}`,
                },
                {
                selector: '[Tomorrow]',
                attr: {
                    'v-if': "isShowTomorrow",
                    ':class': "{'startdate-active': (startDateFlag == 'tomorrow' && !isDisableDatePicker) && !isNonrecurring, 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
                    '@click.stop': "toggleStartDatePicker(false, 'tomorrow')"
                },
                text: `{{ contents?.site_specific?.product_details?.lbl_tomorrow ? contents.site_specific.product_details.lbl_tomorrow : "Tomorrow" }}`,
                },
                {
                selector: '[PickDate]',
                attr: {
                    'v-if': "showStartDate() && SHOWING_START_AND_END_DATE === false",
                    ':class': "{'startdate-active': (startDateFlag == 'pick_start' && !isDisableDatePicker) && !isNonrecurring, 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
                    '@click.stop': "toggleStartDatePicker(true, 'pick_start')"
                },
                text: `{{ contents?.site_specific?.product_details?.start_date ? contents.site_specific.product_details.start_date : "Pick Start Date" }} 
                    <div DateTimePicker>
                    <div StartDatePicker></div> 
                    </div>
                `,
                },
            ]
        }, 
        {
            selector: '[dueDate]',
            attr: {
                'v-if': "enable_due_date"
            },
            text: `<h4 class="due-date-title"> <i class="fa fa-calendar me-1"></i> {{rentalEndDate }} </h4>`,
        },
    ]));



    // Exact times  
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentMyBookingExactTimes]start_date', [
        {
        selector: '[RentMyBookingExactTimes]',
        template: true,
        attr: {
            'v-if': 'isEnableExactTime'
        },
        child: [
            {
            selector: '[TimeArea]',
            attr: {
                'v-if': 'productDetailsStore.exactTimes?.times?.length > 0'       
            }, 
            child: [
                {
                selector: '[TitleTag]',
                attr: { // just for log
                    '@click.stop': 'log({exactTimes: productDetailsStore.exactTimes})'
                }
                },
                {
                selector: '[exactTimeItem]',
                template: false,
                attr: {
                    'v-for': '(duration, i) in productDetailsStore.exactTimes.times',
                    ':class': `{'timeActive': duration?.isActive}`,
                    '@contextmenu': 'log(duration)',
                    '@click.stop': `async ()=>{
                    globalLoader.show();
                    await getChangTime(duration.time, duration)
                    helper.toggleLoopItem(productDetailsStore.exactTimes.times, -1, 'isActive'); 
                    helper.toggleLoopItem(productDetailsStore.exactTimes.times, i, 'isActive'); 
                    getPriceValue(cart);
                    isSelectedExactTime = true;
                    globalLoader.hide();
                    }`,
                },
                text: '{{ duration?.time }}'
                }
            ]
            }, 
            {

            },
            {
                selector: '[TourNotAvailableMessageArea]',
                attr: {
                    'v-if': 'productDetailsStore.exactTimes?.showWarningMessage',
                }, 
                text: `This tour is not available on the date you selected. Please pick another date.`
            }, 
        ]
        },   
        

    ]));

    // START DATE FUNTIONALITY (rental prices)
    /// selector: [RentMyRentalDateRange]
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentMyRentalDateRange]start_date_functionality', [
        {
            selector: '[RentMyRentalDateRange]',
            template: true,
            attr: {
                'v-if': "showPricingOption()"
            },
            child: [
                {
                    selector: '[RentalDateRangeTitle]',
                    attr: { 'v-if': 'showPricingOption()' },
                    text: `{{ contents?.site_specific?.product_details?.lbl_rental_date_range }}
                            <a style="cursor: pointer;"
                                v-if="!isRentalDateShowAll && prices.rent.price?.length > priceLimitShowFirst"
                                v-on:click="onClickRentalDateShowAll(prices.rent.price)">
                                {{ contents?.site_specific?.others?.txt_show_all || '(Show All)' }}
                            </a>
                        `,
                },
                {
                    selector: '[RentalDateRangeList]',
                    attr: {
                        'v-if': "showPricingOption()",

                    },
                },
                {
                    selector: '[RentalDateRangeItem]',
                    template: true,
                    attr: {
                        'v-for': "(item, i) in prices?.rent?.price"
                    },
                },
                {
                    selector: '[RentalDateRangeItem]',
                    attr: {
                        'v-if': "(i < 6 && !isRentalDateShowAll) || isRentalDateShowAll",
                        ':class': "{'DaterangeActive': (rentelPriceId == item.id && !isDisableDatePicker) && !isNonrecurring, 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
                        '@click': "toggleRentalPriceRangePicker(item.id, item)"
                    },
                    child: [
                            {
                            selector: '[setActiveClass]',
                            attr: {
                                ':class': "{'DaterangeActive': (cart?.rent_start === item?.rent_start && cart?.rent_end === item?.rent_end) }",
                            },
                        },
                        
                        {
                            selector: '[PricePreText]',
                            text: `{{ item?.duration }} {{ getDurationUnit(item?.duration > 1 ? item?.type + 's' : item?.type) }} `
                        },
                        {
                            selector: '[Devider]',
                            attr: { 'v-if': 'item?.duration' }
                        },
                        {
                            selector: '[Price]',
                            text: `{{ currencyConvert(item?.price) }}`
                        },
                    ]

                }
            ]
        },        
        
    ]));


    // END DATE FUNTIONALITY
    // selector: [RentMyRentalEndDate]
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentMyRentalEndDate]', [
        {
            selector: "[RentMyRentalEndDate]",
            template: true,
            attr: {
                'v-if': "(baseRadio == 2 && !(enable_due_date || enable_exact_time)) && showEndDate() && !enable_due_date"
            },
            child: [
                {
                    selector: '__SELF__',
                    attr: {
                        "v-if": "showEndDate() && !enable_due_date",                        
                    },
                    child: [
                        {
                            selector: '[RentalEndDatePicker]',
                            attr: {
                                "v-if": "showStartDate()",
                                ":class": "{'daterange-active': (endDateFlag == 'pick_end' && !isDisableDatePicker) && !isNonrecurring , 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
                                "@click.stop": "toggleEndDatePicker(true, 'pick_end')"
                            },
                            text: `__EXISTING_HTML__
                                <div DateTimePicker>
                                    <div EndDatePicker></div> 
                                </div>
                                `,
                        },
                        {
                            selector: '[RentalEndDateSelectedLabel]',
                            template: true,
                            attr: {
                                "v-if": "showEndDate() && !enable_due_date"
                            },
                            text: `<i v-if="checkRentTime(rentEndDate, 'end')" class="fa fa-calendar me-1"></i>
                                <template v-if="endDate_useRangePicker">
                                    {{ checkRentTime(rentStartDate, 'start')}} - {{ checkRentTime(rentEndDate, 'end') }}
                                </template>
                                <template v-else>
                                    {{ checkRentTime(rentEndDate, 'end') }}
                                </template>
                            `,
                        }
                    ]
                }
            ],
        },
    ]));



    // EXACT SELECT DURATION
    // selector: [RentMyExactSelectDuration]
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentMyExactSelectDuration]', [
        {
            selector: '[RentMyExactSelectDuration]',
            attr: {
                "v-if": "baseRadio == 2 && enable_exact_time && !this.isDisableDatePicker",
            },
            child: [
                {
                    selector: '[RentMyExactSelectDurationTitle]',
                    text: `{{contents.site_specific.product_details &&
                    contents.site_specific.product_details.exact_select_duration ?
                    contents.site_specific.product_details.exact_select_duration : 'Select Duration'}}`
                },
                {
                    selector: '[RentMyExactSelectDurationItem]',
                    attr: {
                        "v-for": "(duration, i) in extact_durations",
                        ":class": "{'daterange-active': (selectedDuration == (duration.value +','+duration.type +','+duration.id) && !isDisableDatePicker) && !isNonrecurring, 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
                        "@click": "onDurationChange(duration.value +','+duration.type +','+duration.id)"
                    },
                    text: `{{duration.label}}`
                }
            ],
        },
        
    ]));


    // EXACT SELECT TIME
    // selector: [RentMyExactSelectTime]
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentMyExactSelectTime]', [
        {
            selector: '[RentMyExactSelectTime]',
            attr: {
                "v-if": "baseRadio == 2 && enable_exact_time && !this.isDisableDatePicker && extact_times.length>0 && isShowStartTimeSelection",
            },
            child: [
                {
                    selector: '[RentMyExactSelectTimeTitle]',
                    text: `{{ contents.site_specific.product_details && contents.site_specific.product_details.exact_select_start_time ? contents.site_specific.product_details.exact_select_start_time : 'Select Start time'}}`
                },
                {
                    selector: '[RentMyExactSelectTimeItem]',
                    attr: {
                        "v-for": "(e_time, i) in extact_times",
                        ":class": "{'daterange-active': (selected_exact_time == e_time && !isDisableDatePicker) && !isNonrecurring , 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
                        "@click": "changTime(e_time)"
                    },
                    text: `{{e_time}}`
                }
            ],
        },
        
    ]));


    // Wishlist section
    createComponentHTML(wrapper,  [
        {
        selector: '[RentMyAddToWishlistBtn]',
        attr: {
            'v-if': "is_active_wish_list",
            '@click.stop': `async ()=>{
            globalLoader.show()
            await wishlistStore.addToList({productDetails: product, rental_type: baseRadio == 1 ? 'buy' : 'rent'})
            globalLoader.show()
            }`
        },
        },
        
    ]);

   

    // CHANGE LOCATION 
    // selector: [RentMySelectLocation]
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentMySelectLocation]', [
        {
            selector: '[RentMySelectLocation]',
            template: true,
            attr: {
                'v-if': 'contents.site_specific?.confg?.checkout?.multiple_location && onlineStore?.locations?.length > 1'
            },
            child: [
                {
                    selector: '[SelectLocationTitle]',
                    text: `{{ allowDyanmicContent ? contents?.site_specific?.custom?.product_page_location : '__EXISTING_HTML__' }}`,
                },
                {
                    selector: '[SelectLocationList]',
                    attr: {
                        'v-if': `onlineStore?.locations?.length > 1`,
                    },
                },
                {
                    selector: '[SelectLocationItem]',
                    attr: {
                        'v-for': "(loc,i) in onlineStore?.locations",
                        ':class': `{'LocationActive': selectedLocation == loc.id }`,
                        '@click': "onSelectLoc(loc)"
                    },
                    text: `{{ loc.name }}`,
                }
            ]
        },
        
    ]));

    // QUANTITY
    // selector: [RentmyQuantityContainer]
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentmyQuantityContainer]', [
        // {
        //     selector: '[RentmyQuantityContainer]',
        //     attr: {
        //         'v-if': 'product.booking === false && show_checkout_availability_text'
        //     }
        // },
        {
            selector: '[QuantityContainerTitle]',
            text: `{{ allowDyanmicContent ? contents.site_specific.product_details.quantity: '__EXISTING_HTML__' }}`,
        },
        {
            selector: '[QuantityDecrementBtn]',
            attr: {
                '@click': "decreaseQty()"
            },
        },
        {
            selector: '[NumberOfQuantity]',
            attr: {
                'v-model': "total.qty",
                ':disabled': 'true',
                '@keydown': "validateQuantity($event)",
                '@keyup': "onQuantityKeyup($event.target.value)"
            },
        },
        {
            selector: '[QuantityIncrementBtn]',
            attr: {
                '@click': "increaseQty()"
            },
        },
        {
            selector: '[RentmyAvailableLabel]',
            text: `
                {{ allowDyanmicContent ? contents.site_specific.product_details.available : '__EXISTING_HTML__' }}          
        `,
        },
        {
            selector: '[RentmyAvailableQty]',
            text: `{{ actualAvailableQty <= 0 ? 0 : actualAvailableQty }} `,
        },
    ]));


    // did for Ionic-rental
    // selector: [StockLabels]
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[StockLabels]', [   
        {
        selector: '[InStock]',
        attr: {
            'v-if': "actualAvailableQty != 0"
        },
        },
        {
        selector: '[OutStock]',
        attr: {
            'v-if': "actualAvailableQty == 0"
        },
        },
        {
        selector: '[Devider]',
        attr: {
            'v-if': "actualAvailableQty != 0"
        },
        },
        {
        selector: '[QuantityArea]',
        attr: {
            'v-if': "actualAvailableQty != 0"
        },
        child: [
            {
            selector: '[Quantity]',          
            text: `{{ actualAvailableQty <= 0 ? 0 : actualAvailableQty }}`,
            },
        ],
        },    
        
    ]));

    // ADD TO CART
    // selector: [RentMyCartBtnArea]
    createComponentHTML(wrapper, RentMyEvent.apply_filters('options:package:[RentMyCartBtnArea]', [
        {
            selector: '[RentMyCartBtnArea]',
            template: true,
            attr: {
                'v-if': 'is_show_addtoCart_btn'
            },
            child: [
                {
                    selector: '[RentMyAddCartBtn]',
                    attr: {
                        '@click': 'addTocart()',
                        ':disabled': `isAddToCartLoading 
                            || (availableQ <= 0 && is_show_not_available_message) 
                            || (!prices?.buy?.type && !prices.rent?.type) 
                            || unavailableForRent || isRentalDateNull() 
                            || ( unavailableWhenExactStartTime && !isDisableDatePicker ) 
                            || isShowRecurrignWarn`
                    },
                    text: `{{ allowDyanmicContent ? contents.site_specific.product_details.add_to_cart : '__EXISTING_HTML__' }}`,
                },
            ],
        }        
        
    ]));


    // PACKAGEPRODUCT
    // selector: [RentMyProductPackageArea]
    createComponentHTML(RentMyProductPackageArea, RentMyEvent.apply_filters('options:package:[RentMyProductPackageArea]', [
        {
            selector: '[RentMyProductPackageAreaTitle]',
            text: `{{allowDyanmicContent ? contents.site_specific.product_details.title_package_includes : '__EXISTING_HTML__'}}`,
        },
        {
            selector: '[RentMyProductPackageContent]',
            attr: {
                'v-if': 'details?.package_content',
                'v-html': 'details?.package_content',
            },
        },
        {
            selector: '[PackageSingleProduct]',
            attr: {
                'v-for': '(product,idx) in packageProduct'
            },
        },
        {
            selector: '[PackageProductNameTitle]',
            text: `{{ product.name }} ({{ product.quantity }})`,
        },
        {
            selector: '[PakageProductVarient]',
            attr: {
                'v-if': 'removingUnAssignedValue(product.variants).length'
            },
        },
        {
            selector: '[PakageProductVarientInnerSelect]',
            attr: {
                '@change': 'showVarientQuantiy($event.target.value, product.id, idx)'
            },
        },
        {
            selector: '[PakageProductVarientInnerOption]',
            attr: {
                'v-for': '(variant,i) in removingUnAssignedValue(product.variants)',
                ':selected': 'i === 0',
                ':value': 'variant.id'
            },
            text: '{{ variant.variant_chain }}',
        }
    ]))




}
formatPricingDom();

const pricingHtml = RentmyPackageComponent.innerHTML
window.pricingHtml = {pricingHtml}
RentmyPackageComponent.innerHTML = '';


const SHOW_START_DATE = storeContent?.site_specific?.confg?.show_start_date;
const SHOW_END_DATE = storeContent?.site_specific?.confg?.show_end_date;
const SHOWING_START_AND_END_DATE = SHOW_START_DATE && SHOW_END_DATE;
let endDate_useRangePicker = (RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_useRangePicker_for_endDate ?? false) && SHOW_END_DATE;
let startDate_useRangePicker = (RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_useRangePicker_for_startDate ?? false) && SHOW_END_DATE;



const rentmyComponent = defineAsyncComponent(
    () =>
        new Promise((resolve, reject) => {
            resolve({
                template: `<div>${pricingHtml}</div>
                `,
                components: {
                    EmDateTimePicker,
                },
                data() {
                    return {
                        helper,
                        startDatePicker,
                        endDatePicker,
                        globalLoader,
                        startDate_useRangePicker,
                        endDate_useRangePicker,
                        SHOWING_START_AND_END_DATE,
                        cartLoad,
                        price_type,
                        packageProduct,
                        packageVariant,
                        locationId,
                        is_show_addtoCart_btn,
                        cartItemList,
                        addonsProductList,
                        customFieldList,
                        deliveryOptionList,
                        customSelectFieldList,
                        unitType,
                        store_config,
                        currencySymbol,
                        customFieldPrices,
                        rentalEndDate,
                        startDateFlag,
                        endDateFlag,
                        priceLimitShowFirst,
                        isRentalDateShowAll,
                        isShowPricingForExactTimeItem,
                        addonslabel,
                        enable_due_date,
                        is_showCustomDatetime_label,
                        is_show_not_available_message,
                        selected_exact_duration,
                        exact_times,
                        isModernLayout,
                        deliveryFlow,
                        selectedDeliveryFlow,
                        isActiveMultiDistance,
                        isShowFulfilmentOption,
                        fulfilmentOptionList,
                        fullfilment_type,
                        fullfilment_option,
                        deliveryLimitErrorLbl,
                        deliveryLimitWarningLbl,
                        isDisabledFulfillmentOption,
                        isHideFulfillment,
                        isSameDateBooking,
                        isAddToCartLoading,
                        currentLocation,
                        onlineStore,
                        selectedLocation,
                        isDisplayMiniCart,
                        initialLoad,
                        details,
                        rentEndDate,
                        rentStartDate,
                        min_date,
                        store,
                        images,
                        featureImage,
                        defaultImage,
                        baseRadio,
                        available,
                        rentelPriceId,
                        diabledAddtoCarts,
                        packageAvailableMessage,
                        isDisableDatePicker,
                        termFlag,
                        selectedCustomFields,
                        productOptionValueCheck,
                        extact_durations,
                        extact_times,
                        extact_times_values,
                        selected_exact_time,
                        selectedDuration,
                        enable_exact_time,
                        selectedPriceObj,
                        recurringPriceId,
                        endDateErrorMessage,
                        isShowToday,
                        isShowTomorrow,
                        is_recurring_product,
                        isApply,
                        isSeparateDate,
                        startTime,
                        selectedRecurringPrice,
                        isShowRecurrignWarn,
                        isActiveRecurring,
                        isNonrecurring,
                        unavailableWhenExactStartTime,
                        isAutoSelectEarliestDate,
                        autoSelectStartDate,
                        autoSelectEndDate,
                        isUpdateExactTime,
                        cartDataFound,
                        unavailableForRent,
                        availableQ,
                        actualAvailableQty,
                        temp_rent_price,
                        temp_qty,
                        tempAvailableList,
                        availableList,
                        invalid,
                        tempInvalid,
                        package_products,
                        product_list,
                        isVariantsFound,
                        variants_products_id,
                        isRentalDateOrTimeChanged,
                        product,
                        contents,
                        delivery_settings,
                        prices,
                        total,
                        cart,
                        daterangeConfig,
                        usingInPageCartWidget: document.querySelector('.RentMyWrapperInpageCartWidget'),
                        isEnableExactTime,
                        isSelectedExactTime, 
                        allowDyanmicContent,
                        wishlistStore,
                        is_active_wish_list,
                    }
                },
                methods: {
                    startDate_useRangePicker,
                    endDate_useRangePicker,
                    log: console.log,
                    showVarientQuantiy,
                    currencyConvert,
                    getDeliveryOptionList,
                    onClickSelectDeliveryFlow,
                    onClickSelectFulfilment,
                    isDisabledDeliveryFlow,
                    disabledDeliveryFlow,
                    saveDeliveryFlow,
                    getCustomFieldList,
                    predefineRadioCustomfieldSeleted,
                    customFieldSelector,
                    customPricingDataFormat,
                    getAddonsProductList,
                    onVariantQtyKeyup,
                    showPricingOption,
                    isAddonsProductCombinationOk,
                    addTocart,
                    redirectAddToCart,
                    changeVariant,
                    variantChain,
                    showEndDate,
                    showEndTime,
                    showStartTime,
                    showStartDate,
                    getLastvariantChain,
                    validateQuantity,
                    onQuantityKeyup,
                    decreaseQty,
                    increaseQty,
                    modifyAddonsProductVariantQuantity,
                    onDurationChange,
                    changTime,
                    getChangTime,
                    isShowStartTimeSelection,
                    formatPriceDurationTerm,
                    getDurationUnit,
                    onChangeBuyRent,
                    onFieldValueChange,
                    onFieldValueChangeForDefault,
                    _addToSelectedCustomField,
                    selectField,
                    onChangeBuyTypeSwitch,
                    toggleStartDatePicker,
                    toISOLocal,
                    toggleEndDatePicker,
                    toggleRentalPriceRangePicker,
                    onClickRentalDateShowAll,
                    onClickOpenTimePicker,
                    checkRentTime,
                    formatDate,
                    isStandardPrpduct,
                    hasRecurringPrice,
                    checkFulfilmentOption,
                    disabledFulfillmentOption,
                    checkEarliestDateActive,
                    _setRentalDate,
                    isRentalDateNull,
                    onSelectLoc,
                    onChangeLocation,
                    showLocationAlert,
                    load,
                    changeFeatureImage,
                    changeBuyRent,
                    changeRent,
                    calculateRentDate,
                    getRentDate,
                    _getOnlyDate,
                    checkTodayTomorrow,
                    checkPrice,
                    getShowEndDate,
                    getPriceValue,
                    getQtyFromCart,
                    checkAvailableForType,
                    checkAvailabe,
                    callInitPrice,
                    initPrice,
                    imageFormat,
                    setTotalPrice,
                    changeOnRent,
                    changeOnBuy,
                    formatBuyRent,
                    changeVariantCall,
                    formatVariant,
                    formatVariantSetList,
                    formatVariantList,
                    variantChainRes,
                    getLastvariantChainCall,
                    addCartObj,
                    diabledAddtoCart,
                    getTimeFromManualChange,
                    getDateFromManualChange,
                    selectstartDateTime,
                    _getDateForNoTime,
                    selectendDateTime,
                    _getRentalDate,
                    getAvailableList,
                    buyCartGetDuration,
                    hasBuyCartData,
                    removingUnAssignedValue,
                    show_checkout_availability_text,
                },
                async mounted(){
                     
                    emitter.on('changed:reantalDate:from:inPageCartWidget', (data) => {
                        if(!isDisableDatePicker.value){
                            selectstartDateTime((data.startDate || '') + (data?.startTime ? ` ${data.startTime}` : ''));        
                            selectendDateTime((data.endDate || '') + (data?.endTime ?  ` ${data?.endTime}` : '')); 
                            checkPrice();            
                        }
                    });
                },
                
            })
        })
)


const rentmy_template = RentmyPackageComponent;

const datepickers = ref(null);
setInterval(() => {
    datepickers.value = wrapper.querySelectorAll('[DateTimePicker]');
}, 500);


let pickerTheme = RENTMY_GLOBAL?.emDateTimePicker?.theme || 'light';
let  pickerColors = RENTMY_GLOBAL?.emDateTimePicker?.colors || {};


let detailsPage_startDatePicker_ajdustX = RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_startDatePicker_ajdustX || 0;
let detailsPage_startDatePicker_ajdustY = RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_startDatePicker_ajdustY || 0;

let detailsPage_endDatePicker_ajdustX = RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_endDatePicker_ajdustX || 0;
let detailsPage_endDatePicker_ajdustY = RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_endDatePicker_ajdustY || 0;

let detailsPage_startDatePicker_displayIn = RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_startDatePicker_displayIn || 'modal';
let detailsPage_endDatePicker_displayIn = RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_endDatePicker_displayIn || 'modal';

let timePickerUi = RENTMY_GLOBAL?.emDateTimePicker?.timePickerUi || 'standard';
let timePickerButtons = RENTMY_GLOBAL?.emDateTimePicker?.timePickerButtons ?? true;

</script>
<template>
    <template v-if="loaded && rentmy_template">
        <teleport :to="rentmy_template">
            <component :is="rentmyComponent">
            </component>
        </teleport>
    </template>
    
    <template v-if="datepickers && datepickers.length && rentmyComponent">
        <template v-for="(datepicker, i) in datepickers">
            <template v-if="datepicker.querySelector('[StartDatePicker]')">
                <teleport :to="datepicker.querySelector('[StartDatePicker]')" :key="i">
                    <EmDateTimePicker ref="startDatePicker"
                        :est_valid_dates="product?.est_valid_dates"
                        @change="async (data) => { 
                            selectstartDateTime(data.startDateTime);

                            if(startDate_useRangePicker){
                                selectendDateTime(data.endDateTime); 
                            } 
                            // checkPrice();
                            if(isEnableExactTime && !productDetailsStore.exactTimes.times?.length){ 
                                globalLoader.show()
                                await packageDetailsStore.getExactTimes(product.id, data.startDate) 
                                if(productDetailsStore.exactTimes.times?.length){
                                    let duration = productDetailsStore.exactTimes.times[0]
                                    await getChangTime(duration.time, duration)
                                    duration['isActive'] = true,
                                    getPriceValue(cart);
                                    isSelectedExactTime = true; 
                                }
                                globalLoader.hide()
                            }
                        }"
                        :startDate="rentStartDate" 
                        :rangePicker="startDate_useRangePicker"
                        :timePicker="(showStartTime() && !isSeparateDate)" 
                        :minDate="min_date"
                        :isDisabled="isDisableDatePicker || isNonrecurring"
                        :invisible="true"
                        :theme="pickerTheme"
                        :colors="pickerColors"
                        :timePickerUi="timePickerUi"
                        :timePickerButtons="timePickerButtons"
                        :adjustX="detailsPage_startDatePicker_ajdustX"
                        :adjustY="detailsPage_startDatePicker_ajdustY"
                        :displayIn="detailsPage_startDatePicker_displayIn"
                        >
                    </EmDateTimePicker>
                </teleport>
            </template>

            <template v-if="datepicker.querySelector('[EndDatePicker]')">
                <teleport :to="datepicker.querySelector('[EndDatePicker]')" :key="i">
                    <EmDateTimePicker ref="endDatePicker"
                        @change="(data) => { 

                            if(endDate_useRangePicker){
                            console.log('end -date >>>', data); 
                            selectstartDateTime(data.startDateTime);
                            }
                            selectendDateTime(data.endDateTime); 
                            // checkPrice();
                        }"
                        :startDate="rentStartDate" 
                        :endDate="rentEndDate" 
                        :rangePicker="endDate_useRangePicker"
                        :timePicker="(showEndTime() && !isSeparateDate)" 
                        :minDate="min_date"
                        :isDisabled="isDisableDatePicker || isNonrecurring"
                        :invisible="true"
                        :theme="pickerTheme"
                        :colors="pickerColors"
                        :timePickerUi="timePickerUi"
                        :timePickerButtons="timePickerButtons"
                        :adjustX="detailsPage_endDatePicker_ajdustX"
                        :adjustY="detailsPage_endDatePicker_ajdustY"
                        :displayIn="detailsPage_endDatePicker_displayIn"
                        >
                    </EmDateTimePicker>                   
                </teleport>
            </template>

            <template v-if="datepicker.querySelector('[StartTimePicker]')">
                <teleport :to="datepicker.querySelector('[StartTimePicker]')" :key="i">
                    <TimePicker :_id="'time_picker'" 
                    :timePickerUi="timePickerUi" 
                    :timePickerButtons="timePickerButtons"
                    :date="isAutoSelectEarliestDate ? autoSelectStartDate : rentStartDate"
                        @date-model="($event, startDateFlag) => { getTimeFromManualChange($event, startDateFlag) }">
                    </TimePicker>
                </teleport>
            </template>
        </template>
    </template>

    <LocationNoticeModal v-model="isShowModel">
    {{ model_msg }}
  </LocationNoticeModal>
</template>

<style>
#start_date_picker,
#start_time_picker,
#end_date_picker,
#time_picker {
    display: none;
}
</style>