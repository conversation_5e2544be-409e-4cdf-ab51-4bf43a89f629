<script setup>
import { ref, inject, onMounted, defineProps, provide, computed } from 'vue';
import { createComponentHTML } from "@utils/functions/withComponent";
let { helper, cookie, domElement, http, labelSelector, currency } = inject('utils');
let { wrapper, relatedProducts } = defineProps(['wrapper','relatedProducts']);
let globalLoader = inject('globalLoader');
let setWraperIsReady = inject('setWraperIsReady');
import { Toaster, emitter } from '@/import-hub';

import { useProductListStore } from '../../product-list/controllers/productList.controller';
import { useCartStore } from '../../cart/controllers/cart.controller';
const productListStore = useProductListStore();
const cartStore = useCartStore();


createComponentHTML(wrapper, [
    {
        selector: "[RentMyRelatedProducts]",
        attr: { 'v-if': 'relatedProducts?.length' },
    },
    {
        selector: "[RentMyRelatedProducts] [RentMyProductItem]",
        template: true,
        attr: { 'v-for': '(product, index) in relatedProducts', ':key': 'index' },
    },
    {
        selector: "[RentMyProductItem] a[RentMyProductImageUrl]",
        attr: { ':href': 'getProductDetailsURL(product)' },
    },
    {
        selector: "[RentMyProductItem] img[RentMyProductImage]",
        attr: { ':src': 'helper.getProductImage(product)' },
    },
    {
        selector: "[RentMyProductItem] [RentMyProductName]",
        attr: { ':src': 'helper.getProductImage(product)' },
        text: `<a :href="getProductDetailsURL(product)">{{ product?.name }}</a>`,
    },
    {
        selector: "[RentMyProductItem] [RentMyProductPrice]",
        text: `{{ getProductPrice(product) }}`,
    },
    {
        selector: "[RentMyProductItem] [RentMyViewDetailsBtn]",
        attr: { ':href': 'getProductDetailsURL(product)' },
    },
    {
        selector: "[RentMyProductItem] [RentMyAddToCartBtn]",
        attr: { 'v-if': 'hasBuyPrice(product)', '@click.stop': 'createCart(product)' },
    }
]);

let template = wrapper.innerHTML
wrapper.innerHTML = '';

let relatedProductsComponent = {
    template,
    data() {
        return {
            helper,
            currency,
            relatedProducts,
        }
    },
    async mounted() {

    },
    computed: {

    },
    methods: {
        getProductDetailsURL: function(product){   
            let detailsPageUrl = product?.type == 2 ? window.RENTMY_GLOBAL.page?.package_details : window.RENTMY_GLOBAL.page?.product_details;
            return helper.withURL.generateURL(detailsPageUrl, product)
        },
        getProductPrice: function(product){   
            let { currencyConfig } = cartStore;
            let priceLabel = currency.formatListPrice(product.prices?.[0], currencyConfig);
            return priceLabel?.price || '';
        },
        hasBuyPrice: function(product){   
            let { prices } = product;
            let _prices = helper.formatBuyRent(prices)
            let isBuyType = false
            if (_prices && Object.keys(_prices).length > 0) {
                if(_prices?.buy?.type) isBuyType = true;
            }   
            return !!isBuyType;
        },
        createCart: function(product){        
            globalLoader.show()
            productListStore.createCart(product).then(response => {
                globalLoader.hide()
                this.disabled = true;
                if (response.status == 'OK') {
                    if(response.result.error){
                        Toaster().error(response.result.error);
                        return;
                    }
                    Toaster().success('Item added to cart')
                    localStorage.setItem('user_cart', JSON.stringify(response.result.data));
                    let token = response.result.data?.token;
                    if(RENTMY_GLOBAL.ajax_url){
                        http.post(RENTMY_GLOBAL.ajax_url, {
                            token,
                            action:'rentmy_cdn_request',
                            method:'rentmy_cart_token',
                        })
                    }
                } else {
                    if(response.result.error){
                        Toaster().error(response.result.error);
                    }
                }
            })
        },
    },    
}

</script>

<template>
    <teleport :to="wrapper">
        <relatedProductsComponent></relatedProductsComponent>
    </teleport>
</template>