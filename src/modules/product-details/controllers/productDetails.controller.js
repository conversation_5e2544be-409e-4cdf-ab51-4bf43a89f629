import { defineStore } from 'pinia'
import { reactive, computed, ref, onMounted } from 'vue'
import productDetailsService from '../services/productDetails.service'
import { useGlobalStore } from '@stores/global';

export const useProductDetailsStore = defineStore('productDetails', () => {

  const state = reactive({
    product: null,
    datesPriceDuration: null,
    exactTimes: {
      durations: {},
      showWarningMessage: false,
      times: [],
    }
  })

  const globalStore = useGlobalStore();

  let product = computed(() => state.product);
  let datesPriceDuration = computed(() => state.datesPriceDuration);
  let settings = computed(() => globalStore.storeAndLocations );
  let deliverySettings = computed(() => globalStore.deliverySettings );
  let relatedProducts = computed(() => globalStore.relatedProducts );
  let priceValue = computed(() => globalStore.priceValue );
  let customFields = computed(() => globalStore.customFields );
  let currency_config = computed(() => globalStore.currency_config );
  let exactTimes = computed(() => state.exactTimes );

  const init = async function(){ 
    await globalStore.getDeliverySettings() 
  } 

  const getProductDetails = async function(uid, { view_type }){
    let response = await productDetailsService.getProductDetails(uid, { view_type });
    if(response.status === 'OK'){
      let details = response?.result?.data;
      state.product = details;
      state.product.__variantSetData = null;
      return details;
    }
  }

  // this assigned data, just help to fire a watch() function in detailsOfProduct.vue
  const setVariantSet___toUpdateImageView = async function(data){
    state.product.__variantSetData = data;
  }

  const getRelatedProducts = async function(product_id){
    let related_products = await globalStore.getRelatedProducts(product_id);   
    return related_products;
  }

  const getDatesPriceDuration = async function(start_date, start_time, price_id=false){
    let response = await productDetailsService.getDatesPriceDuration(start_date, start_time, price_id);   
    let datesPriceDuration = response?.result?.data;
    state.datesPriceDuration = datesPriceDuration;
    return datesPriceDuration;
  }

  const getPriceValue = async function(payload){
    let price_value = await globalStore.getPriceValue(payload); 
    return price_value;
  }

  const getCustomFields = async function(product_id){
    let custom_fields = await globalStore.getCustomFields(product_id); 
    return custom_fields;
  }

  const getExactTimes = async function(product_id, start_date){
    try {
      let response = await productDetailsService.getExactTimes(product_id, start_date);
      let data = response?.result?.data;
      state.exactTimes = [] 
      if(data?.durations?.length){
        state.exactTimes.showWarningMessage = false
        let timesArray = []
        data?.durations.forEach(({id, value, type, label, values, times} )=> {
          times.forEach(time => {
              let time_id = values.find(v => v.value === time).id
              let data = { id, time_id, time, value, type, label } 
              timesArray.push(data)
          })
        }) 
        state.exactTimes.times = timesArray
      } else {
        state.exactTimes.showWarningMessage = true
      }
    } catch (error) {
      console.error('getExactTimes:error', error);
    }
      
  }


  return {
    /**variables */
    settings,
    deliverySettings,
    product,
    relatedProducts,
    datesPriceDuration,
    priceValue,
    customFields,
    currency_config,
    exactTimes,

    /**Functions */
    init,
    getProductDetails,
    setVariantSet___toUpdateImageView,
    getRelatedProducts,
    getDatesPriceDuration,
    getPriceValue,
    getCustomFields,
    getExactTimes,
  }
})