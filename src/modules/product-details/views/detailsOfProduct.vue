<script setup>
import 'vue/dist/vue.esm-bundler.js';
import { ref, inject, provide, watch, onMounted, defineProps } from 'vue';
import { useProductDetailsStore } from '../controllers/productDetails.controller';
import { Toaster, emitter } from '@/import-hub';
import ProductPricing from './productPricing.vue';
import RelatedProducts from './relatedProducts.vue';
 
let { helper, domElement, labelSelector } = inject('utils');
let { wrapper } = defineProps(['wrapper']);
const productDetailsStore = useProductDetailsStore();
provide('productDetailsStore', productDetailsStore);
let RentMyParams = domElement.parseRentMyData(wrapper, {}, ['uid', 'url', 'product_id'], {}); // product_id, can be pass also from wp shortcode: (example: product_id=RentMy-45454)
let { default_product_image } = window.RENTMY_GLOBAL?.images;
let isMounted = ref(false);
let details = ref(null);
globalThis.details= details
const relatedProducts = ref(null);

const Dom = {
     printProductTitle: function(){
          let { product } = productDetailsStore;
          let selectors = {product_name: '[RentMyProductName]'}
          labelSelector.labelToSelectorAll(selectors, wrapper);
          let elements = Array.from(wrapper.querySelectorAll(selectors.product_name));
          elements?.forEach(el => el.textContent = product?.name);
     },
     printProductPriceText: function(text=''){ 
          let selectors = {product_price_text: '[RentMyProductPrice]'}
          labelSelector.labelToSelectorAll(selectors, wrapper);
          let elements = Array.from(wrapper.querySelectorAll(selectors.product_price_text));
          elements?.forEach(el => el.textContent = '$0.0000');
     },
     image_area_li: null,
     printProductImages: function(){
          let { product } = productDetailsStore; 
          let img_index = 0;
          if(product?.images?.length){
               product.images?.sort((a, b) => b.status - a.status);   
          }    
          let small_images = product?.images?.map(img => img?.image_small 
               ? helper.generateImage(img?.image_small, product.id) : default_product_image );

          const setFeaturedImage = (img_index=0) => {
               let featured_image = product?.images?.[img_index]?.image_large;
               featured_image = featured_image ? helper.generateImage(featured_image, product.id) : default_product_image;
               let img = wrapper.querySelector('img[RentMyProductImage]');
               img.src = featured_image;
          }

          setFeaturedImage(img_index);
          let imagesArea = wrapper.querySelector('[RentMyProductImages]');
          if(imagesArea){
               let options = domElement.parseOptions(imagesArea);
               const activeClass = options?.activeClass || 'ActiveImg';
               Dom.image_area_li = imagesArea.querySelector('li');   
               imagesArea.innerHTML = '';
               Dom.isGenerate = true;
               small_images?.forEach((image_url, index) => {
                    let li = Dom.image_area_li.cloneNode(true);    
                    if(img_index == index){
                         li.classList.add(activeClass);
                    } else {
                         li.classList.remove(activeClass);
                    }
                    let img = li.querySelector('img');
                    img.src = image_url;    
                    li.addEventListener('click', (event) => {
                         event.preventDefault();
                         setFeaturedImage(index);
                         Array.from(imagesArea.querySelectorAll('li')).forEach((li, i) => {
                              if(i == index) li.classList.add(activeClass);
                              else li.classList.remove(activeClass);
                         })                         
                    })     
                    imagesArea.appendChild(li);
               });            
          }
     },
};

const BuyRentSwitch = {
     status: false,
     checkbox: wrapper.querySelector('input[type=checkbox][BuyRentToggleSwitch]'),
     applyChangeEvent: function(){
          if(this.checkbox){
               this.checkbox.addEventListener('change', (event) => {
                    this.status = event.target.checked;
                    emitter.emit('switch-changed', event.target.checked)
               })
          }
     },
     on: function(){
          if(this.checkbox){
               this.checkbox.checked = true;
               if (document.createEvent) {
                    let EVENT = document.createEvent("HTMLEvents");
                    EVENT.initEvent('change', true, true);         
                    this.checkbox.dispatchEvent(EVENT)
               }
          }
     },
     off: function(){
          if(this.checkbox){
               this.checkbox.checked = false;
               if (document.createEvent) {
                    let EVENT = document.createEvent("HTMLEvents");
                    EVENT.initEvent('change', true, true);         
                    this.checkbox.dispatchEvent(EVENT)
               }
          }
     },
}

onMounted(async ()=>{
     const { parseUIDfromUrl, getQuery, setQuery } = helper.withURL;
     let params = parseUIDfromUrl('product_details', RentMyParams);
     RentMyParams[params.key] = params.value;

     if(!RentMyParams[params.key] && getQuery('uid')){
          params.key = 'uid'
          params.view_type = ''
          params['value'] = getQuery('uid')
          RentMyParams['uid'] = getQuery('uid')
     }

     if(!RentMyParams[params.key]){
          Toaster().warning(`Product ${params.key} not found`);
          return;
     }

     await productDetailsStore.init();
     await productDetailsStore.getProductDetails(RentMyParams[params.key], { view_type: params.view_type });
     let { product } = productDetailsStore; 

     if(!product){
          Toaster().error(`Product not found in this location.`);
          setTimeout(() => {
               setQuery({}, RENTMY_GLOBAL.page.products_list)
               window.location.reload()
          }, 2000);
          return
     }
     details.value = product

     const descriptionDiv = wrapper.querySelector('[RentMyProductDescription]');
     if(descriptionDiv) descriptionDiv.innerHTML = product.description || '';
     

     Dom.printProductTitle();
     Dom.printProductPriceText();
     Dom.printProductImages();
     relatedProducts.value =  await productDetailsStore.getRelatedProducts(product?.id);

     BuyRentSwitch.applyChangeEvent();  
     // domElement.setWraperIsReady(wrapper);
     isMounted.value = true;
     RentMyEvent.emit('cdn:details_page:mounted', true);

})

watch(()=>productDetailsStore.product?.__variantSetData, (a, b) => {
     Dom.printProductImages();
})

const relatedProductsWrapper = document.querySelector('.RentMyRelatedProductBody');



</script>

<template>
     <div v-if="isMounted && details">
          <ProductPricing :details="details" :wrapper="wrapper"></ProductPricing>
     </div>    
     <div v-if="isMounted && relatedProductsWrapper && relatedProducts">
          <RelatedProducts :wrapper="relatedProductsWrapper" :relatedProducts="relatedProducts"></RelatedProducts>
     </div> 
</template>