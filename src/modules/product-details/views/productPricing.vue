<script setup>
import { getQtyFrom<PERSON>artList, getQtyFromProductList, isCartDateExist, preventInputAlphabets, datePipe, formatPriceDuration, GET_OnlineStoreCartToken, getOnlineStoreCartDate } from '@utils/functions';
import { createComponentHTML } from '@utils/functions/withComponent';
import { defineProps, defineAsyncComponent, ref, reactive, inject, watch } from 'vue';
import productService from '../services/productDetails.service';
import http from '@utils/http';
import currency from '@utils/currency';
import { Toaster } from '@/import-hub';
import EmDateTimePicker from '@components/em-datetimepicker/EmDateTimePicker.vue';
import TimePicker from '@components/TimePicker.vue';
let { helper } = inject("utils");
let globalLoader = inject('globalLoader');
let { domElement } = inject('utils');
import LocationNoticeModal from "@components/modal/LocationNotice.vue";
let emitter = inject('emitter');
const productDetailsStore = inject('productDetailsStore');
let allowDyanmicContent = inject('allowDyanmicContent');

import { useWishlistStore } from "@stores/wishlist";
const wishlistStore = useWishlistStore()

const site_specific = JSON.parse(localStorage.getItem('rentmy_contents'))?.site_specific;
const is_active_wish_list = site_specific?.confg?.inventory?.wishlist?.active
 
/* -------------------------------------------------------------------------- */
/*                                  VARIABLES                                 */
/* -------------------------------------------------------------------------- */

const storeContent = localStorage.getItem("rentmy_contents")
    ? JSON.parse(localStorage.getItem("rentmy_contents"))
    : null;
let props = defineProps({
  details: {},
  wrapper: null
})
const log = console.log;
const loaded = ref(false)
const isMounted = ref(false)
let wrapper = props.wrapper;
let details = props.details;
let product = props.details;
let cartLoad = ref(null);
let price_type = ref(null);
let is_show_addtoCart_btn = ref(null);
let cartItemList = ref([]);
let addonsProductList = ref([]);
let customFieldList = ref([]);
let deliveryOptionList = ref([]);
let customSelectFieldList = ref([]);
let addonslabel = ref(null);
let store = ref(null);
let enable_due_date = ref(false);
let enable_exact_time = ref(false);
let extact_durations = ref([]);
let extact_times = ref([]);
let extact_times_values = ref([]);
let exact_times = ref({});
let selectedDuration = ref("null");
let is_showCustomDatetime_label = ref(false);
let isShowPricingForExactTimeItem = ref(true);
let is_show_not_available_message = ref(true);
let endDateFlag = ref('');
let priceLimitShowFirst = ref(RENTMY_GLOBAL?.detailsPage_priceLimitShowFirst || 6);
let isRentalDateShowAll = ref(false);
let deliveryFlow = ref({});
let selectedDeliveryFlow = ref(null);
let selectedDeliveryFlowId = ref(null);
let isDisabledFulfillmentOption = ref(false);
let isActiveMultiDistance = ref(false);
let isNonrecurring = ref(false);
let isShowFulfilmentOption = ref(false);
let fulfilmentOptionList = ref([]);
let fullfilment_type = ref(0);
let isSameDateBooking = ref(true);
let contents = reactive({});
let description = reactive({});
let delivery_settings = reactive({});
let is_recurring_product = ref(null);
let rentalEndDate = ref(null);
let customFieldPrices = reactive({});
let unitType = ref(null);
let currencySymbol = ref(null);
let store_config = reactive({});
let fullfilment_option = ref('in-store');
let deliveryLimitWarningLbl = ref('');
let deliveryLimitErrorLbl = ref('');
let isHideFulfillment = ref(false);
let isAddToCartLoading = ref(false);
let currentLocation = ref(null);
let onlineStore = reactive({});
let selectedLocation = ref(null);
let current_quantity = ref(null);
let quantity_limit = ref(null);
let isDisplayMiniCart = ref(true);
let isShowColorVariant = ref(false);
let variantChains = ref([]);
let rentEndDate = ref(null);
let rentStartDate = ref(null); 
let min_date = ref(null);
let images = ref([]);
let featureImage = ref(null);
let defaultImage = ref("");
let baseRadio = ref(null);
let available = ref(null);
let rentelPriceId = ref(null);
let recurringPriceId = ref(null);
let cartList = reactive({});
let isDisableDatePicker = ref(false);
let selectedPriceObj = reactive({});
let selectedRecurringPrice = reactive({});
let termFlag = ref(false);
let selected_exact_time = ref(null);
let selected_exact_duration = reactive({});
let unavailableWhenExactStartTime = ref(null);
let availableQ = ref(null);
let actualAvailableQty = ref(null);
let actualAvailableQtyForBooking = ref(null);
let unavailableForRent = ref(null);
let productOptionValueCheck = ref(false);
let selectedCustomFields = ref([]);
let isShowToday = ref(false);
let isShowTomorrow = ref(false);
let endDateErrorMessage = ref('');
let initialStartTime = ref(true);
let isApply = ref(false);
let startTime = ref("");
let isSeparateDate = ref(false);
let startDateFlag = ref('');
let isActiveRecurring = ref(false);
let isShowRecurrignWarn = ref(false);
let isAutoSelectEarliestDate = ref(false);
let autoSelectStartDate = ref(null);
let temp_rent_price = ref(0);
let temp_qty = ref(0);
let cartDataFound = ref(false);
let availableList = ref([]);
let invalid = ref([]);
let hourlyCalenderData = ref([]);
let hourlyCalenderTable = ref([]);
let editDate = ref(false);
let isEnableExactTime = product.exact_time_with_days
let isSelectedExactTime = ref(false)
let show_checkout_availability_text = storeContent?.site_specific?.confg?.show_checkout_availability_text === true 
let initialLoadSameDateBooking = ref(true);
 
let widgetDates = function(){
  let using_inPageCartWidget = document.querySelector('.RentMyWrapperInpageCartWidget');
  let startDate = sessionStorage.getItem('online_inagecart_widget_startDate');
  let endDate = sessionStorage.getItem('online_inagecart_widget_endDate');
  if(using_inPageCartWidget && startDate && endDate){
      return {startDate, endDate};
  } else {
      return null;
  }
}

let prices = reactive({
  buy: { type: false, price: 0, id: null },
  rent: { type: false, price: [] }
});
let total = reactive({
  qty: 1,
  price: 0,
  term: ""
});
let cart = reactive({
  deposit_amount: 0,
  deposite_tax: "",
  driving_license_required: false,
  price: 0,
  product_id: 0,
  quantity: 0,
  variants_products_id: null,
  location: null,
  rent_start: "",
  rent_end: "",
  fullfilment_option: "",
  rental_duration: 0,
  rental_type: "",
  sales_tax: 0,
  term: "",
  token: "",
  price_id: null,
  custom_fields: []
})

const api_call = ref(true);

let daterangeConfig = reactive({
  pages: 1,
  hideActionBtn: false,
  showRangeLabels: true
})

const asset_url = RENTMY_GLOBAL?.env?.ASSET_RUL || import.meta.env.VITE_ASSET_URL;
const product_image = asset_url + "products/";
const model_msg = ref('');
const isShowModel = ref(false);
/* -------------------------------------------------------------------------- */
/*                                  FUNCTIONS                                 */
/* -------------------------------------------------------------------------- */

function InitFunction() {
  try {
    isActiveMultiDistance.value = productService.hasActiveMultiDistance();
    isSameDateBooking.value = productService.hasSameDateBooking();
    if (!isSameDateBooking.value) {
      let date = new Date();
      date.setDate(date.getDate() + 1);
      rentStartDate.value = formatDate(date);
    }

    store.value = window.RENTMY_GLOBAL.store_id;
    const storeConfig = sessionStorage.getItem("online_store")
      ? JSON.parse(sessionStorage.getItem("online_store"))
      : null;
  
    if (isActiveMultiDistance.value) {
      disabledDeliveryFlow();
    }
   
      // we are skipping isSeparateDate.value config, forefully by requirement
    isSeparateDate.value = false ?? storeContent?.site_specific?.confg?.inventory?.seperate_datetime_picker == true;
    onlineStore = sessionStorage.getItem("online_store") ? JSON.parse(sessionStorage.getItem("online_store")) : null;
    currentLocation = JSON.parse(localStorage.getItem('current_location'));
    selectedLocation.value = RENTMY_GLOBAL.locationId;



    const contents = JSON.parse(localStorage.getItem('rentmy_contents'));
    isDisplayMiniCart.value = contents.hasOwnProperty('site_specific')
      && contents?.site_specific?.hasOwnProperty('confg')
      && contents?.site_specific?.confg.hasOwnProperty('checkout')
      && contents?.site_specific?.confg.checkout.hasOwnProperty('view_mini_cart')
      ? contents?.site_specific?.confg.checkout.view_mini_cart
      : true;
    cartItemList.value = localStorage.getItem("user_cart")
      ? JSON.parse(localStorage.getItem("user_cart")).cart_items
      : null;

    if (product.rent_end) {
      try {
          let date = datePipe(product.rent_end);
          if(date) rentalEndDate.value = date.split(' ').slice(0, 3).join(' ');
      } catch (error) {
          
      }
    }
    isActiveRecurring.value = productService.hasActiveRecurringPayment() && details?.enduring_rental;
    unitType.value = "%";
    store_config = localStorage.getItem("currency") ? localStorage.getItem("currency") : null;

    currencySymbol.value = (store_config && store_config.symbol) ? store_config.symbol : "$";
    is_recurring_product.value = details.hasOwnProperty('recurring_prices') && details?.enduring_rental && details.recurring_prices.length > 0
      ? true
      : false;
    if (is_recurring_product.value) {
      details.recurring_prices.map(p => {
        p['recurring'] = true;
      })
    }



    /** Set Exact date or time config */
    enable_due_date.value = !!(details?.exact_date && details.exact_date);
    if (details.hasOwnProperty('exact_time') && details.exact_time) {
      enable_exact_time.value = true;
      extact_durations.value = product.extact_durations.durations;
      extact_times.value = product.extact_durations.times;
      if (!checkEarliestDateActive) unavailableWhenExactStartTime.value = true;
    } else {
      enable_exact_time.value = false;
    }

    if (details) {
      load();
      price_type.value = details.default_variant.price_type;
      if (is_recurring_product.value && details.recurring_prices.length > 0) {
        initPriceRecurring();
      }
    }
    if (prices.buy?.type) {
      const available_quantity = details.available_for_sale
        ? details.available_for_sale
        : details.available;
      availableQ.value = available_quantity == 1
        ? available_quantity
        : available_quantity - total.qty;
      actualAvailableQty.value = available_quantity;
    } else {
      availableQ.value = details.available == 1
        ? details.available
        : details.available - total.qty;
      actualAvailableQty.value = details.available;
    }

    if (
      contents?.site_specific?.confg &&
      contents?.site_specific?.confg.checkout &&
      contents?.site_specific?.confg.checkout.hasOwnProperty(
        "online_order"
      ) &&
      contents?.site_specific?.confg.checkout.online_order ==
      false
    ) {
      is_show_addtoCart_btn.value = false;
    } else {
      is_show_addtoCart_btn.value = true;
    }

    getAddonsProductList();
    if (isActiveMultiDistance.value && !delivery_settings?.charge_by_zone) {
      getDeliveryOptionList();
    }
    getCustomFieldList();

    if (isActiveRecurring.value) {
      hasRecurringPrice();
    }
    checkFulfilmentOption();
    if (checkEarliestDateActive) {
      if (baseRadio.value !== 1) {
        _setRentalDate();
      }
    }

    doublePageDatepickerConfig();
    // check product availablity
    getAvailableList();
    getHourlyData();
    buyCartGetDuration();
    if (isActiveSubscription) {
      _subscribeProductQty();
    }

  } catch (error) {
    console.error('InitFunction:error', error);
  }
}

InitFunction();

function currencyConvert(amount) {
  return currency.currencyConvert(amount);
}

function getDeliveryOptionList() {
  productService
    .getDeliveryOptionList()
    .then(res => {
      if (res.status == 'OK') {
        deliveryOptionList.value = res.result.data;
        if (deliveryOptionList.value.length > 0) {
          onClickSelectDeliveryFlow(deliveryOptionList.value[0]);
        }
      } else {
        deliveryOptionList.value = [];
      }
    })
    .catch(err => {
      deliveryOptionList.value = [];
    });
}

function onClickSelectDeliveryFlow(item) {
  if (isDisabledDeliveryFlow) return;
  deliveryFlow.value = item;
  selectedDeliveryFlow.value = item.delivery_flow;
  selectedDeliveryFlowId.value = item.id;
}


function onClickSelectFulfilment(item) {
  if (isDisabledFulfillmentOption.value) return;
  fullfilment_type.value = item.id;
  fullfilment_option.value = item.value;
}

function isDisabledDeliveryFlow() {
  let status = false;
  let deliveryFlow = JSON.parse(localStorage.getItem("deliveryFlow"));
  let userCart = localStorage.getItem("user_cart") ? JSON.parse(localStorage.getItem("user_cart")) : null;
  if (deliveryFlow) {
    status = deliveryFlow?.delivery_flow && userCart?.cart_items?.length > 0 ? true : false;
    selectedDeliveryFlow.value = deliveryFlow.delivery_flow;
  }
  return status;
}

function disabledDeliveryFlow() {
  if (localStorage.getItem("deliveryFlow") && localStorage.getItem("deliveryFlow") != 'undefined') {
    if (localStorage.getItem("user_cart")) {
      let userCart = localStorage.getItem("user_cart") ? JSON.parse(localStorage.getItem("user_cart")) : null;
      if (userCart && userCart.hasOwnProperty('cart_items')) {
        if (userCart.cart_items.length == 0) {
          localStorage.removeItem('deliveryFlow')
          localStorage.removeItem('deliveryFlowLabel')
        }
      }
    }
  }
}


function saveDeliveryFlow() {
  productService.saveDeliveryFlow(deliveryFlow.value);
  disabledDeliveryFlow();
}

function onVariantQtyKeyup(quantity, variant_id, product_id) {
  if (!isNaN(parseInt(quantity))) {
    addonsProductList.value.map(p => {
      if (p.id == product_id) {
        p.variants.map(v => {
          if (v.id == variant_id) {
            v["min_qty"] = parseInt(quantity);
          }
        });
      }
    });
  }
}

function showPricingOption() {
  if (
    contents?.site_specific?.confg &&
    contents?.site_specific?.confg.hasOwnProperty("rental_price_option")
  ) {
    return contents?.site_specific?.confg?.rental_price_option;
  }
  return true;
}

function isAddonsProductCombinationOk() {
  let requiredAddonsQty = 0;
  let result = true;
  let sumOfVariantQty = 0;

  if (addonsProductList.value.length > 0) {
    addonsProductList.value.map(p => {
      requiredAddonsQty = requiredAddonsQty + total.qty * p.min_quantity;

      p.variants.map(v => {
        sumOfVariantQty = sumOfVariantQty + v.min_qty;
      });
    });

    if (sumOfVariantQty > requiredAddonsQty) {
      result = false;
    }
  }

  return result;
}

async function addTocart() {  
  
  isAddToCartLoading.value = true;
  if (!isAddonsProductCombinationOk()) {
    Toaster().error(
      "Select product add-on quantities"
    );

    return;
  }
  const user = JSON.parse(localStorage.getItem("onlineAuthUser"));
  const cart = addCartObj();
  cart.price = total.price;
  cart.custom_fields = selectedCustomFields.value;
  if (enable_due_date.value) {
    cart.rent_start = product.rent_start;
    cart.rent_end = product.rent_end;
  }

  if (isShowFulfilmentOption.value) {
    cart.fullfilment_option = fullfilment_option.value;
  }

  if (addonsProductList.value.length > 0) {
    let required_addons = [];
    let variant = [];

    addonsProductList.value.map(p => {
      variant = p.variants.filter(v => v.min_qty > 0);
      if (variant.length != 0) {
        for (let i = 0; i < variant.length; i++) {
          required_addons.push({
            product_id: p.id,
            variants_products_id: variant[i].id,
            quantity_id: variant[i].quantity_id,
            quantity: variant[i].min_qty
          });
        }
      }
    });
    cart["required_addons"] = required_addons;
  }

  if (isActiveRecurring.value) {
    cart["recurring"] = true;
  }

  if (isActiveRecurring.value && Object.keys(selectedRecurringPrice).length > 0) {
    cart["recurring"] = true;
    cart["price_id"] = selectedRecurringPrice.id;
  }
  // Set exact_times if is enable exact time
  if (exact_times.value
    && Object.keys(exact_times.value).length > 0
    && enable_exact_time.value
    && extact_times.length > 0) {
    cart['exact_times'] = exact_times.value;
  }
  if (user?.customer_id) {
    cart['customer_id'] = user.customer_id ? user.customer_id : null;
  }

  cartLoad.value = true;
  if(isMounted.value) globalLoader.show();
  const res = await productService.addtoCart(cart);
  globalLoader.hide();

  if (res.status === "OK") {
    if (res.result.error) {
      Toaster().error(
        deliveryLimitErrorLbl.value != '' && res.result.error == "Our delivery slots for this day has been fulfilled, not taking new orders." ? deliveryLimitErrorLbl.value : res.result.error
      );
      isAddToCartLoading.value = false;
      return
    }
    if (res.result.warning) {
      Toaster().error(
        deliveryLimitWarningLbl.value ? deliveryLimitWarningLbl.value : res.result.warning
      );
      isAddToCartLoading.value = false;
      return
    }
    // else { 
    if (res.result.data || res.result.hasOwnProperty('data')) {

      if(res.result?.data?.token) localStorage.setItem('token', res.result?.data?.token);  

      actualAvailableQty.value = actualAvailableQty.value - cart.quantity;
      productService.saveCartsInlocalStorage(res.result.data);
      if (isActiveMultiDistance.value && !delivery_settings?.charge_by_zone) {
        saveDeliveryFlow();
      }
      if (isDisplayMiniCart.value) {
        // OPEN MINICART            
        localStorage.setItem('user_cart', JSON.stringify(res.result.data)); 
      }

      localStorage.setItem("token", res.result.data.token);

      if (
        isCartDateExist() == false &&
        cart.rent_start &&
        cart.rent_end
      ) {
        sessionStorage.setItem(
          "online_cartStartDate",
          JSON.stringify(cart.rent_start)
        );
        sessionStorage.setItem(
          "online_cartEndDate",
          JSON.stringify(cart.rent_end)
        );
      }

      if (isShowFulfilmentOption.value) {
        sessionStorage.setItem(
          "online_fullfilment_option",
          fullfilment_option.value
        );
      }

      if (enable_exact_time.value) {
        is_showCustomDatetime_label.value = true;
        rentStartDate.value = cart.rent_start;
        rentEndDate.value = cart.rent_end;
        isShowPricingForExactTimeItem.value = false;
      }

      isDisableDatePicker.value = true;

      localStorage.setItem('user_cart', JSON.stringify(res.result.data));      
      let RentMyProductImg = document.querySelector('.RentMyProductDetailsImgShow');
      let RentMyMiniCart = document.querySelector('.RentMyMiniCart');             
      // domElement.dropInCart(RentMyProductImg, RentMyMiniCart);
    }

    cartLoad.value = false;
    Toaster().success('Product added to cart');
    if(document.querySelector('.RentMyWrapperInpageCartWidget')){
      emitter.emit('open_inCartWidget', true);
    } else {
      redirectAddToCart(); // =========================== Redirecting....
    }
    isAddToCartLoading.value = false;
  } else {
    isAddToCartLoading.value = false;
    cartLoad.value = false;
    Toaster().error(
      "Product has not been added to cart"
    );
  }
}

function redirectAddToCart() {
  if(RENTMY_GLOBAL?.using_in_cli_project){
          RentMyEvent.emit('cdn:goto:cart_page', RENTMY_GLOBAL.page.cart);
  } else {
    if(RENTMY_GLOBAL?.after_add_to_cart_redirecto_cart === false){
      windowLocation().reload();
    } else {
      window.open(RENTMY_GLOBAL?.page?.cart, '_self');    
    }
  }
}

async function changeVariant(pos, id) {
  const data = changeVariantCall(pos, id);
  if (data) {
    if ("pos" in data) {
      await variantChain(data);
    } else {
      await getLastvariantChain(data); // changing image by emit
    }
    await this.getPriceValue(this.cart);
  }

}

async function variantChain(data) {
  const timestamp = new Date().getTime();
  const response = await http.get(`variant-chain?product_id=${product.id}&variant_id=${data.id}&variant_chain=${data.chain}&t=${timestamp}&rent_type=${baseRadio.value ? (baseRadio.value === 1 ? 'buy' : 'rent') : 'rent'}`)
  const m = response.data;
  const res = m.result.data;
  variantChainRes(res, data);
}

function showEndDate() {
  if (enable_exact_time.value && !isDisableDatePicker.value) {
    return false;
  } else if (
    contents?.site_specific?.confg &&
    contents?.site_specific?.confg.hasOwnProperty("show_end_date") 
  ) {
    return contents?.site_specific?.confg.show_end_date;
  }

  return true;
}
function showEndTime() {
  if (enable_exact_time.value && !isDisableDatePicker.value) {
    return false;
  } else if (
    contents?.site_specific?.confg &&
    contents?.site_specific?.confg.hasOwnProperty("show_end_time")
  ) {
    return contents?.site_specific?.confg.show_end_time;
  }
  return true;
}
function showStartTime() {
  if (enable_exact_time.value && !isDisableDatePicker.value) {
    return false;
  } else if (
    contents?.site_specific?.confg &&
    contents?.site_specific?.confg.hasOwnProperty("show_start_time")
  ) {
    return contents?.site_specific?.confg.show_start_time;
  }
  return true;
}
function showStartDate() {
  if (
    contents?.site_specific?.confg &&
    contents?.site_specific?.confg.hasOwnProperty("show_start_date")
  ) {
    return contents?.site_specific?.confg.show_start_date;
  }
  return true;
}

async function getLastvariantChain(data) {
  let rent_start = rentStartDate.value;
  isAddToCartLoading.value = true;
  const timestamp = new Date().getTimezoneOffset();
  const timezone_offset_minutes = timestamp === 0 ? 0 : -timestamp;
  const response = await http.get(
    `get-path-of-chain?product_id=${product.id}&variant_id=${data.id}&variant_chain=${data.chain}&t=${timezone_offset_minutes}&token=${GET_OnlineStoreCartToken()}
          &rent_type=${baseRadio.value ? (baseRadio.value === 1 ? 'buy' : 'rent') : 'rent'}`)
  const m = response.data;
  const res = m.result.data;
  productDetailsStore.setVariantSet___toUpdateImageView(res);

  getLastvariantChainCall(res);
  checkRecurring(res);
  rentStartDate.value = rent_start;  
  if (true ?? isAutoSelectEarliestDate.value) {
    rentelPriceId.value = prices.rent.price[0].id;
    total.price = prices.rent.price[0].price;
  }
  if (prices?.buy && baseRadio.value == 1) {
    total.price = prices.buy.price;
  }
  if ((true ?? isAutoSelectEarliestDate.value) && (!rentStartDate.value || rentStartDate.value == "null")) return;
  price_type.value = res.variant.price_type;
  total.qty = 1;
  availableQ.value =
    res.available == 1 ? res.available : res.available - total.qty;
  actualAvailableQty.value = res.available;
  available.value = res.available;
  if (availableQ.value <= 0) {
    unavailableForRent.value = true;
    availableQ.value = res.available;
  } else {
    unavailableForRent.value = false;
  }
  isAddToCartLoading.value = false;

}

function checkRecurring(res) {
  if (res.recurring_prices && res.recurring_prices.length) {
    is_recurring_product.value = true;
    details.recurring_prices = res.recurring_prices;
    details.recurring_prices.map(p => {
      p['recurring'] = true;
    });
    if (details.recurring_prices.length > 0) {
      initPriceRecurring(); // to set 1st radio button checked
    }
  } else {
    delete details.recurring_prices;
    is_recurring_product.value = false;
  }
}


function validateQuantity(e) {
  preventInputAlphabets(e);
}

function onQuantityKeyup(value) {
  if (!isNaN(parseInt(value))) {
    availableQ.value = actualAvailableQty.value - parseInt(value);

    if (availableQ.value < actualAvailableQty.value) {
      unavailableForRent.value = false;
    }

    modifyAddonsProductVariantQuantity(total.qty);
  } else if (value == "") {
    total.qty = 1;
    availableQ.value = actualAvailableQty.value - total.qty;
    modifyAddonsProductVariantQuantity(total.qty);
  }
}

function decreaseQty() {
  if (total.qty > 1) {
    if (actualAvailableQty.value <= total.qty) {
      total.qty--;
      if (total.qty <= actualAvailableQty.value) {
        availableQ.value = 1;
        unavailableForRent.value = false;
      } else {
        unavailableForRent.value = true;
      }
    } else {
      total.qty--;
      availableQ.value = availableQ.value + 1;
    }
    // unavailableForRent.value=false;
    modifyAddonsProductVariantQuantity(total.qty);
  }
}

function increaseQty() {
  if (isActiveSubscription) {
    if (!(parseInt(total.qty.toString()) < actualAvailableQty.value)) {
      return;
    }
    if ((quantity_limit.value == 0 || quantity_limit.value) && parseInt(total.qty.toString()) >= parseInt(quantity_limit.value.toString())) {
      return;
    };
  }
  if (parseInt(total.qty.toString()) < actualAvailableQty.value) {
    total.qty++;
    availableQ.value = actualAvailableQty.value - total.qty;

    if (availableQ.value == 0) {
      is_show_not_available_message.value = false;
    }

    modifyAddonsProductVariantQuantity(total.qty);
    // setTotalPrice();
  } else {
    total.qty++;
    availableQ.value = 0;
    unavailableForRent.value = true;
  }
}

function modifyAddonsProductVariantQuantity(quantity) {
  addonsProductList.value = addonsProductList.value.map(p => {
    let index = 0;
    p.variants.map(v => {
      if (index == 0) {
        v["min_qty"] = quantity * p.min_quantity;
      } else {
        v["min_qty"] = 0;
      }

      index++;

      return v;
    });

    return p;
  });
}

function onDurationChange(duration_value) {
  try {
    selectedDuration.value = duration_value;
    selected_exact_time.value = null;
    unavailableWhenExactStartTime.value = true;

    if (duration_value != "null") {
      let durationArray = duration_value.split(",");
      let durationValue = durationArray[0];
      let durationType = durationArray[1];
      const selectedDurationObject = extact_durations.value.find(duration => {
        return duration.value === durationValue && duration?.type === durationType
      });

      extact_times.value = selectedDurationObject.times;
      extact_times_values.value = selectedDurationObject.values;
      let duration = duration_value.split(",");
      selected_exact_duration = { value: duration[0], type: duration[1], id: duration[2] };
      total.term =
        selected_exact_duration.value +
        " " +
        (selected_exact_duration.value > 1
          ? selected_exact_duration?.type + "s"
          : selected_exact_duration?.type);
      // if (selected_exact_time.value !== null) {
      //   changTime(selected_exact_time.value);
      // }
      if (!isShowStartTimeSelection) {
        getChangTime(null, selected_exact_duration);
      }
    }
  } catch (error) {
    console.error('onDurationChange:error', error);
  }
}

function changTime(value = null) {
  if (value != "null") {
    selected_exact_time.value = value;
    if (extact_times_values.value) {
      extact_times_values.value.forEach((val) => {
        if (val.value == value) {
          selected_exact_duration.id = val.id;
        }
      })
    }
    getChangTime(selected_exact_time.value, selected_exact_duration);
  }
}

function isShowStartTimeSelection() {
  let show_start_time;
  if (
    contents?.site_specific?.confg &&
    contents?.site_specific?.confg.hasOwnProperty("show_start_time")
  ) {
    show_start_time = contents?.site_specific?.confg.show_start_time;
  }
  if (enable_exact_time.value && show_start_time) {
    return true;
  }
  return false;
}

function formatPriceDurationTerm(terms) {
  try {
    if (!terms) {
      return '';
    }
    const labels = JSON.parse(localStorage.getItem('rentmy_contents')).site_specific.others;
    const lbl_for = labels.product_list_for ? (labels.product_list_for + ' ') : 'for ';
    const termsArr = terms.split(' ');
    const term_duration = termsArr[0];
    if (termsArr.length == 1) {
      return lbl_for + term_duration
    }
    const term_unit = termsArr[1];
    let unit = '';
    switch (Number(term_duration) > 1 ? term_unit.toLowerCase() + 's' : term_unit.toLowerCase()) {
      case "hour":
        unit = labels.lbl_hour ? labels.lbl_hour : 'hour';
        return lbl_for + term_duration + ' ' + unit;
      case "hours":
        unit = labels.lbl_hours ? labels.lbl_hours : 'hours';
        return lbl_for + term_duration + ' ' + unit;
      case "day":
        unit = labels.lbl_day ? labels.lbl_day : 'day';
        return lbl_for + term_duration + ' ' + unit;
      case "days":
        unit = labels.lbl_days ? labels.lbl_days : 'days';
        return lbl_for + term_duration + ' ' + unit;
      case "week":
        unit = labels.lbl_week ? labels.lbl_week : 'week';
        return lbl_for + term_duration + ' ' + unit;
      case "weeks":
        unit = labels.lbl_weeks ? labels.lbl_weeks : 'weeks';
        return lbl_for + term_duration + ' ' + unit;
      case "month":
        unit = labels.lbl_month ? labels.lbl_month : 'month';
        return lbl_for + term_duration + ' ' + unit;
      case "months":
        unit = labels.lbl_months ? labels.lbl_months : 'months';
        return lbl_for + term_duration + ' ' + unit;
    }
  } catch (error) {
    console.error('formatPriceDurationTerm:error', error);
  }

}

function getDurationUnit(type) {
  return formatPriceDuration(type);
}

function onChangeBuyRentRadio(type) {
  baseRadio.value = type === "buy" ? 1 : 2; // set type
  customFieldSelector(); // select first items of custom fields
  onChangeBuyRent(type); // process operation as usual
}

function onFieldValueChange(fieldValue, field, format) {
  productOptionValueCheck.value = true;
  if (fieldValue !== "") {
    if (selectedCustomFields.value.length) {
      selectedCustomFields.value = selectedCustomFields.value.filter(cf => { 
        return cf.name !== field.name
      });
    }
    const value = field.product_field_value.find(v => v.id == fieldValue);
    if (format === 'button') {
      field.product_field_value.forEach(item => {
        if (item.id == fieldValue) { item.active = !item.active; } else { item.active = false; }
      });
      const activeCount = field.product_field_value.filter(x => x.active == false);
      field.activeCount = activeCount.length;
      if (value.active) {
        selectField('push', value, field);
      } else {
        selectField('pop', field);
      }
    } else {
      selectField('push', value, field);
    }
  } else {
    if (format === 'button') {
      field.product_field_value.forEach(item => {
        if (item.id == fieldValue) { item.active = !item.active; } else { item.active = false; }
      });
      const activeCount = field.product_field_value.filter(x => x.active == false);
      field.activeCount = activeCount.length;
    }
    selectField('pop', field);
  }
  checkPrice(true);
}

function selectField(type, value, field) {
  if (type === 'push') {
    selectedCustomFields.value.push({
      id: value.id,
      value: value.value,
      name: value.name,
      label: value.label,
      amount: value.price_amount,
      type: value.price_type,
      applicable_for: field.applicable_for,
    });
  } else {
    const name = value.name;
    selectedCustomFields.value = selectedCustomFields.value.filter(f => f.name !== name);
  }
  if (enable_exact_time.value) {
    if ((selectedDuration.value && selected_exact_time.value) || cartDataFound.value) checkPrice(true);
  } else {
    checkPrice(true);
  }
}

let startDatePicker = ref(null);
let endDatePicker = ref(null);



function toggleStartDatePicker(isOpen = false, type) {
  try {
    if (!isDisableDatePicker.value) {
      startDateFlag.value = type;

      if (isOpen) {     
        if(type == 'pick_start'){
          this.startDatePicker[0].toggle(); 
        }
      }
      

      if (type !== 'pick_start') {
        isApply.value = true;
        let date = new Date();
        if (type == 'tomorrow') date.setDate(date.getDate() + 1);

        // safari doesn't support dash(-) format
        if (rentStartDate.value.split(' ').length > 1) {
          rentStartDate.value = rentStartDate.value.split('-').join('/');
        }

        // separating time
        const dateTime = new Date(rentStartDate.value);
        date.setHours(dateTime.getHours());
        date.setMinutes(dateTime.getMinutes());

        rentStartDate.value = formatDate(date);
        selectstartDateTime(rentStartDate.value);
        if (details?.enduring_rental && isActiveRecurring.value && details?.recurring_prices?.length) {
          callInitPrice(recurringPriceId.value);
        } else {
          callInitPrice(rentelPriceId.value);
        }
        setTimeout(() => {
          // checkPrice();
        }, 900);
      }
    }
  } catch (error) {
    console.error('toggleStartDatePicker:error', error);
  }
}


function toggleEndDatePicker(isOpen = false, type) {  
  endDatePicker.value?.[0].toggle();
  if (!isDisableDatePicker.value) {
    endDateFlag.value = type;
  }
}


function toISOLocal(d) {
  let offset;
  const dat = n => ('0' + n).slice(-2);
  const sec = n => ('00' + n).slice(-3);
  offset = d.getTimezoneOffset();
  const sign = offset > 0 ? '-' : '+';
  offset = Math.abs(offset);

  return d.getFullYear() + '-' + dat(d.getMonth() + 1) + '-' + dat(d.getDate()) + 'T' +
    dat(d.getHours()) + ':' +
    dat(d.getMinutes()) + ':' +
    dat(d.getSeconds()) + '.' +
    sec(d.getMilliseconds()) +
    sign + dat(offset / 60 | 0) + ':' + dat(offset % 60);
}



function toggleRentalPriceRangePicker(priceId, price) {
  try {
    if (!isDisableDatePicker.value) {
      if (price) changeRent(priceId, price);
      else changeRent(priceId);
    }
  } catch (error) {
    console.warning('toggleRentalPriceRangePicker()', error);
  }
}

function onClickRentalDateShowAll(list = []) {
  if (list.length < priceLimitShowFirst.value) return;
  isRentalDateShowAll.value = !isRentalDateShowAll.value;
}


function checkRentTime(rentDate, type) {
  rentDate = datePipe(rentDate, type, {booking: product.booking});
  if (rentDate == 'Invalid date') {
      return '';
  }
  return rentDate;
}

function formatDate(d) {
  const dformat = [
    d.getFullYear(),
    ("0" + (d.getMonth() + 1)).slice(-2),
    ("0" + d.getDate()).slice(-2),
  ].join('-')
    + ' ' +
    [
      ("0" + d.getHours()).slice(-2),
      ("0" + d.getMinutes()).slice(-2)
    ].join(':');
  return dformat;
}

function isStandardPrpduct() {
  return contents?.site_specific?.confg?.arb?.store_active == 'standard' || !details?.enduring_rental;
}

function hasRecurringPrice() {
  if (localStorage.getItem('user_cart')) {
    let userCart = {};
    userCart = JSON.parse(localStorage.getItem('user_cart'));
    if ((userCart && userCart.options == null && !userCart.options?.recurring) && userCart.cart_items?.length) {
      isDisableDatePicker.value = false;
      isNonrecurring.value = true;
    }
    if (userCart && userCart.options && userCart.options.recurring) {
      let recurringObj = userCart.options.recurring;
      let priceObj = {};
      if (recurringObj && details?.enduring_rental && details?.recurring_prices) {
        priceObj = details.recurring_prices.find(el => el['duration_type'] == recurringObj['duration_type']);
        selectedRecurringPrice = priceObj || {};
        if (priceObj && Object.keys(priceObj).length > 0) {
          isShowRecurrignWarn.value = false;
        } else {
          isShowRecurrignWarn.value = true;
        }
      }
    }
  }
}

function checkFulfilmentOption() {
  delivery_settings = sessionStorage.getItem('deliverySettings') ? JSON.parse(sessionStorage.getItem('deliverySettings')) : {};
  isHideFulfillment.value = contents?.site_specific?.confg?.checkout?.hide_fulfillment || false;
  let isDeliveryActive = delivery_settings?.delivery && contents?.site_specific?.confg?.delivery?.active;
  if ((!isHideFulfillment.value && isDeliveryActive)) {
    if ((delivery_settings
      && delivery_settings.hasOwnProperty('max_delivery')
      && delivery_settings.hasOwnProperty('delivery_limit')
      && parseInt(delivery_settings.max_delivery) > 0)
    ) {
      isShowFulfilmentOption.value = true;
      fulfilmentOptionList.value = [

        { id: 0, name: contents?.site_specific?.checkout_info.title_delivery_option || 'Delivery', value: "delivery" },
        { id: 1, name: contents?.site_specific?.checkout_info.title_pickup_option || 'Pickup', value: "in-store" },
        { id: 2, name: contents?.site_specific?.checkout_info.title_shipping_option || 'Shipping', value: "shipping" }

      ];

      if (delivery_settings
        && delivery_settings.hasOwnProperty('disable_instore_pickup')
        && delivery_settings.disable_instore_pickup
      ) {
        let index = fulfilmentOptionList.value.findIndex(el => el.value == 'in-store');
        fulfilmentOptionList.value.splice(index, 1);
      }

      if (delivery_settings
        && delivery_settings.hasOwnProperty('shipping')
        && !delivery_settings.shipping
        || !contents?.site_specific?.confg?.shipping?.active
      ) {
        let index = fulfilmentOptionList.value.findIndex(el => el.value == 'shipping');
        fulfilmentOptionList.value.splice(index, 1);
      }


      if (delivery_settings
        && delivery_settings.hasOwnProperty('delivery')
        && !delivery_settings.delivery
        || !contents?.site_specific?.confg?.delivery?.active
      ) {
        let index = fulfilmentOptionList.value.findIndex(el => el.value == 'delivery');
        fulfilmentOptionList.value.splice(index, 1);
      }

      if (fulfilmentOptionList.value.length) {
        onClickSelectFulfilment(fulfilmentOptionList.value[0]);
      }

      disabledFulfillmentOption();

      deliveryLimitWarningLbl.value = contents?.site_specific?.cart['delivery_limit_msg_warning'];
      deliveryLimitErrorLbl.value = contents?.site_specific?.cart['delivery_limit_msg_error']

    }
  }
}

function disabledFulfillmentOption() {
  let fulfilmentOption = sessionStorage.getItem('online_fullfilment_option');
  if (fulfilmentOption) {
    let index = fulfilmentOptionList.value.findIndex(el => el.value == fulfilmentOption);
    onClickSelectFulfilment(fulfilmentOptionList.value[index]);
    isDisabledFulfillmentOption = true;
  }

}

function _setRentalDate(value = null) {
  autoSelectStartDate.value = rentStartDate.value;


  rentStartDate.value = value;

  const cartList = localStorage.getItem("user_cart")
    ? JSON.parse(localStorage.getItem("user_cart"))
    : null;


  if (extact_durations.value.length) {
    onDurationChange(extact_durations.value[0].value + ',' + extact_durations.value[0]?.type + ',' + extact_durations.value[0].id);
    if (extact_durations.value[0].hasOwnProperty('times') && extact_durations.value[0]['times'].length) {
      changTime(extact_durations.value[0]['times'][0])
    }
  }

  if (cartList && cartList?.cart_items && cartList?.cart_items?.length) {
    // rentStartDate.value = cartList?.rent_start;
    // rentEndDate.value = cartList?.rent_end;
    selectstartDateTime(cartList?.rent_start);
    selectendDateTime(cartList?.rent_end);
  }
}

function isRentalDateNull() {
  let value = false;
  if (baseRadio.value == 2) {
    if (!rentStartDate.value || !rentEndDate.value) {
      value = true;
    }
    if (value && enable_due_date.value) {
      // if due date found then use product start_date & end_date as default 
      value = false;
    }
    let cartItemList = localStorage.getItem("user_cart")
      ? JSON.parse(localStorage.getItem("user_cart"))
      : null;
    if (value && cartItemList && cartItemList?.cart_items && cartItemList?.rent_start) {
      // if cart products found then we use cart start date
      value = false;
    }
  }
  return value;
}

function addItemToSubscribe() {
  if (!quantity_limit.value) {
    activeModal.close(false);
    return;
  };
  const cart = addCartObj();
  cart.custom_fields = selectedCustomFields.value;
  if (addonsProductList.value.length > 0) {
    let required_addons = [];
    let variant = [];

    addonsProductList.value.map(p => {
      variant = p.variants.filter(v => v.min_qty > 0);
      if (variant.length != 0) {
        for (let i = 0; i < variant.length; i++) {
          required_addons.push({
            product_id: p.id,
            variants_products_id: variant[i].id,
            quantity_id: variant[i].quantity_id,
            quantity: variant[i].min_qty
          });
        }
      }
    });
    cart["required_addons"] = required_addons;
  }
  cart['product'] = details;
  if (cart.quantity == 0) return activeModal.close();
  activeModal.close(cart);
}
function _subscribeProductQty() {
  if (current_quantity.value) {
    setTimeout(() => {
      total.qty = current_quantity.value;
    }, 300);
  }
}


function isActiveSubscription() {
  let status = false;
  if (contents?.site_specific?.confg?.customer?.membership) {
    if (contents?.site_specific?.confg?.checkout?.checkout_by == "cart") {
      status = true;
    }
  }
  return status;
}

function onSelectLoc(loc) { 
  let cart = JSON.parse(localStorage.getItem('user_cart'));
  if (cart?.cart_items?.length > 0) {
    showLocationAlert();
    return
  }
  let urlWithLocationId = helper.withURL.setQuery({rentmy_location: loc.id},  window.location.href, true)
  window.location.replace(urlWithLocationId)  
}

function onChangeLocation(location_id) {
  let cart = JSON.parse(localStorage.getItem('user_cart'));
  if (cart?.cart_items?.length > 0) {
    return showLocationAlert();
  }
  let locationData;
  onlineStore?.locations.forEach((location) => {
    if (location?.id == location_id) {
      locationData = location;
    }
  });
  if (locationData) {
    if (JSON.stringify(locationData) == JSON.stringify(onlineStore['location'])) {
      return;
    } else {
      onlineStore['location'] = locationData;
      localStorage.setItem('current_location', JSON.stringify(locationData));
      localStorage.setItem('current_location_id', locationData.id);
      sessionStorage.setItem('online_store', JSON.stringify(onlineStore));
      windowLocation().reload();
    }
  }
}

function showLocationAlert() {  
  model_msg.value = `Finish the order before changing locations`;
  isShowModel.value = true;
}


function changeFeatureImage(i) {
  featureImage.value = images[i].lg_feature;
}
 

// not used funtion
function getPriceValue(cart) {
  if (cart.rental_type == "rent" && ((!cart.rent_start || cart.rent_start == null || cart.rent_start == 'null') || (!cart.rent_end || cart.rent_end == null))) return;
  const priceId = cart.price_id;
  cart.custom_fields = selectedCustomFields.value;
  if (isActiveRecurring.value && Object.keys(selectedRecurringPrice).length > 0) {
    cart["price_id"] = selectedRecurringPrice.id;
  }
  http
    .post('get-price-value', cart)
    .then(res => {
      if (res.status === 'OK') {
        endDateErrorMessage.value = '';
        if (res.result.error) {
          endDateErrorMessage.value = res.result.error;
          unavailableForRent.value = true;
          total.price = 0;
          return;
        }
        total.term = '';
        // update term after get price value
        if (termFlag.value || productOptionValueCheck.value) {
          const rentPrice = prices.rent.price.find(item => item.id === priceId);
          if (rentPrice?.duration && rentPrice?.label) {
            total.term = `${rentPrice.duration} ${rentPrice.label} `;
          }
          termFlag.value = false;
          productOptionValueCheck.value = false;
        }
        // addCartObj().price =total.price;
        total.price = res.result.data;
        availableQ.value = res.result.available == 1 ? res.result.available : (res.result.available - this.total.qty);
        actualAvailableQty.value = res.result.available;
        available.value = res.result.available;
        if (availableQ.value <= 0) {
          unavailableForRent.value = true;
          availableQ.value = res.result.available;
        } else {
          unavailableForRent.value = false;
        }
        const cart = addCartObj();
        cart.price = total.price;
        // following 3 conditions are: if start/end time is before/after open/closed time
        // end_date, start_date and message; for force end date
        if (res.result?.start_date) {
          cart.rent_start = res.result.start_date;
          rentStartDate.value = res.result.start_date; 
        }
        if (res.result?.end_date) {
          cart.rent_end = res.result.end_date;
          rentEndDate.value = res.result.end_date;
        }
        endDateErrorMessage.value = '';
        if (res.result?.message && typeof res.result.message == 'string') {
          endDateErrorMessage.value = res.result?.message;
        }
      }
    });
}

function callInitPrice(rentalPriceid) {
  initPrice(rentalPriceid);
}

function imageFormat() {
  if (details && details.images.length > 0) {
    images.value = details.images
      .sort((a, b) => {
        return b.status - a.status;
      })
      .map(m => {
        m["lg_feature"] = `${product_image}${store}/${details.id}/${m.image_large
          }`;
        m["sm_feature"] = `${product_image}${store}/${details.id}/${m.image_small
          }`;
        return m;
      });

    featureImage.value = images.value[0].lg_feature;
  }
}


function changeVariantCall(pos, id) {
  let chain = "";
  variantChains.value[pos] = id;
  if (id <= 0) {
    variantChains.value = variantChains.value.splice(0, pos);
  }
  if (parseInt(id) > 0) {
    if (parseInt(pos) > 0) {
      chain = variantChains.value.slice(0, -1).join(',');
    }
    for (let a of details.variant_set_list[pos]["variants"]) {
      if (a.id == parseInt(id)) {
        a.selected = true;
      } else {
        a.selected = false;
      }
    }
    if (pos + 1 === details.variant_set_list.length) {
      return { id: id, chain: chain };
    } else {
      return { id: id, chain: chain, pos: pos };
    }
  }
}

function formatVariantList(pos) {
  for (let j = 0; j < details.variant_set_list.length; j++) {
    if (j > pos) {
      details.variant_set_list[j]["variants"] = [];
    }
  }
}

function variantChainRes(res, data) {
  formatVariantList(data.pos);
  res.splice(0, 0, {
    id: 0,
    name: "-Select One-",
    variant_set_id: details.variant_set_list[data.pos + 1].id
  });
  formatVariantSetList(res);
}

function getLastvariantChainCall(res) {
  details.images = res.images;
  details.prices = res.prices;
  details.default_variant = res.variant;
  details.default_variant.variants_products_id = res.variant.id
    ? res.variant.id
    : details.default_variant.variants_products_id;
  details.products_availabilities = res.products_availabilities;
  details.rental_price = res?.rental_price ? res.rental_price : null;
  imageFormat();
  let {buy, rent} = formatBuyRent(details.prices);
  prices.buy = buy;
  prices.rent = rent;
  // initPrice();
  // console.log(details.default_variant.variants_products_id);
}

function diabledAddtoCart() {
  let chain = '';
  const VariantActive = wrapper.querySelector('.VariantActive');
  if (VariantActive) {
    chain = VariantActive.value.toString();
  }

  if (!chain.includes("0") && total.qty > 0) {
    return true;
  }
  return false;
}

function getTimeFromManualChange(e, startDateFlag = "") {
  if (e.startTime) {
    startTime.value = e.startTime;
    getDateFromManualChange(e, startDateFlag);
  }
}
function getDateFromManualChange(e, startDateFlag = "") {
  try {
    if (e.isSingleEndDate) {
      selectendDateTime(e.startDate);
    } else {
      if (e.startDate) {
  
        if (isSeparateDate.value && startTime.value) {
          let date = e.startDate.split(' ')[0]; //only date
          e.startDate = date + ' ' + startTime.value;
        } else if (isSeparateDate.value && !startTime.value) {
          e.startDate = _getDateForNoTime(e.startDate);
        }   
        // if new product design pick start date
        if (startDateFlag) {
          if (getShowEndDate() && showPricingOption() && prices.rent.price?.length) {
            if (details?.enduring_rental && isActiveRecurring.value && details?.recurring_prices?.length) {
              changeRent(recurringPriceId.value);
            } else {
              changeRent(rentelPriceId.value);
            }
          } else {
            isApply.value = true;
            checkPrice();
          }
  
        }
      } else if (e.endDate) {
        selectendDateTime(e.endDate);
      }
    }
    
  } catch (error) {
    console.error('getDateFromManualChange:error', error);
  }
}

function selectstartDateTime(startDate) {
  if (isSeparateDate.value && !startTime.value && startDate) { // separate time but no time selected yet
    let dateTime = _getDateForNoTime(startDate);
    cart.rent_start = dateTime;
    rentStartDate.value = dateTime;
  } else {
    cart.rent_start = startDate;
    rentStartDate.value = startDate;
  }


  if (api_call.value && hasHourlyAvailabilty()) {
    api_call.value = false;
    getHourlyData();
    setTimeout(() => {
      api_call.value = true;
    }, 1000);
  }

}


function _getDateForNoTime(e) {
  try { 
    let time = rentStartDate.value ? rentStartDate.value.split(' ')[1] : '12:00 AM';
    if (isAutoSelectEarliestDate.value) time = autoSelectStartDate.value ? autoSelectStartDate.value.split(' ')[1] : '12:00 AM';
    let date = e.split(' ')[0];
    return date + ' ' + time;
  } catch (error) {
    console.error('_getDateForNoTime:error', error);
  }
}

function selectendDateTime(endDate) {
  if (!enable_exact_time.value || isDisableDatePicker.value) {
    cart.rent_end = endDate;
    rentEndDate.value = endDate;
  }
  else {
    rentEndDate.value = '';
  }
  if (isAutoSelectEarliestDate.value) {
    rentEndDate.value = endDate;
    cart.rent_end = endDate;
  }
}

function _getRentalDate(value = null) {
  autoSelectStartDate.value = rentStartDate.value;

  rentStartDate.value = value;
  rentEndDate.value = value;

  const cartList = localStorage.getItem("user_cart")
    ? JSON.parse(localStorage.getItem("user_cart"))
    : null;

  if (cartList && cartList?.cart_items && cartList?.cart_items?.length) {
    rentStartDate.value = cartList?.rent_start;
    rentEndDate.value = cartList?.rent_end;
  }
}
function getAvailableList(date) {

  // if super admin config and admin config
  const storeContent = localStorage.getItem("rentmy_contents") ? JSON.parse(localStorage.getItem("rentmy_contents")) : null;
  const show_daily_availability_super = storeContent?.site_specific?.confg?.inventory?.availability_calendar?.product?.active ? true : false;
  const show_daily_availability = storeContent?.site_specific?.confg?.inventory?.availability_calendar?.product?.daily ? true : false;
  if (!show_daily_availability_super || !show_daily_availability) return;

  // if date not from calender then init this month
  if (!date) {
    date = {};
    date['start_date'] = moment(min_date.value ? new Date(min_date.value) : new Date()).format('YYYY-MM-DD');
    date['end_date'] = moment(min_date.value ? new Date(min_date.value) : new Date()).endOf('month').format('YYYY-MM-DD');
  } else {
    date['start_date'] = moment(date['start_date']).format('YYYY-MM-DD');
    date['end_date'] = moment(date['end_date']).format('YYYY-MM-DD');
  }
  if (daterangeConfig.pages == 2) {
    date['end_date'] = moment(date['end_date']).add(1, 'month').format('YYYY-MM-DD');
  }
  const variants_products_id = details.default_variant.variants_products_id;
  const location_id = RENTMY_GLOBAL.locationId;
  const product_id = details.id;
  productService
    .getCalenderData(product_id, date, variants_products_id, location_id)
    .then(res => {
      if (res.status == 'OK') {
        availableList.value = [];
        (res.result.available || []).forEach((data) => {
          availableList.value.push({
            date: data.date,
            color: data.available > 0 ? '#a7ffa4' : '#fff',
            title: data.available
          });
           
        });

        if(startDatePicker.value?.length){
          // firing date picker method to set available dates
          startDatePicker.value.forEach(picker => {
            picker.setAvailableDates(availableList.value)
          })
        }
        if(endDatePicker.value?.length){
          // firing date picker method to set available dates
          endDatePicker.value.forEach(picker => {
            picker.setAvailableDates(availableList.value)
          })
        }


      } else {
        availableList.value = [];
      }
    })
    .catch(err => {
      availableList.value = [];
    });
}

function getHourlyData() {
  return;
  if (!hasHourlyAvailabilty()) return;
  let date = {};
  date['start_date'] = moment(rentStartDate.value).format("YYYY-MM-DD");
  const variants_products_id = details.default_variant.variants_products_id;
  const location_id = RENTMY_GLOBAL.locationId;
  const product_id = details.id;
  productService
    .getHourlyRentalData(product_id, date, variants_products_id, location_id)
    .then(res => {
      if (res.status == 'OK') {
        let hourlyData = res.result.data;
        hourlyCalenderData.value = [];
        hourlyCalenderTable.value = [];
        hourlyData.forEach(data => {
          // let formated_time = new Date(data.time).toLocaleTimeString('en-US',{timeZone:'UTC',hour12:true,hour:'numeric',minute:'numeric'});
          let formated_time = data.formated_time;
          if (formated_time == '00:00 AM') {
            formated_time = '12:00 AM';
          }
          hourlyCalenderData.value.push({
            available: data.available,
            time: formated_time
          })
        });
        hourlyCalenderTable.value = splitArray(hourlyCalenderData.value, 2);
      } else {
        hourlyCalenderData.value = [];
      }
    })
    .catch(err => {
      hourlyCalenderData.value = [];
    });
}

function splitArray(array, n) {
  let [...arr] = array;
  var res = [];
  while (arr.length) {
    res.push(arr.splice(0, n));
  }
  return res;
}

function hasHourlyAvailabilty() {
  let value = false;
  // super admin config and admin config
  const storeContent = localStorage.getItem("rentmy_contents") ? JSON.parse(localStorage.getItem("rentmy_contents")) : null;
  const show_hourly_availability_super = storeContent?.site_specific?.confg?.inventory?.availability_calendar?.product?.active ? true : false;
  const show_hourly_availability = storeContent?.site_specific?.confg?.inventory?.availability_calendar?.product?.hourly ? true : false;
  if (show_hourly_availability_super && show_hourly_availability) {
    value = true;
  }
  // if found start date
  if (value && rentStartDate.value) {
    value = true;
  }
  return value;
}


function doublePageDatepickerConfig() {
  const storeContent = localStorage.getItem("rentmy_contents") ? JSON.parse(localStorage.getItem("rentmy_contents")) : null;
  if ((storeContent?.site_specific?.confg?.show_start_date && storeContent?.site_specific?.confg?.show_end_date) &&
    (!storeContent?.site_specific?.confg?.show_end_time && !storeContent?.site_specific?.confg?.show_start_time)) {
    daterangeConfig.pages = 2;
    daterangeConfig.hideActionBtn = true;
    daterangeConfig.showRangeLabels = false;
  }

}

/* -------------------------------------------------------------------------- */
/*                                OLD FUNCTIONS                               */
/* -------------------------------------------------------------------------- */

function initPrice(priceId = null) {
  if (prices && Object.keys(prices).length > 0) {
    if (prices.rent?.type) {
      baseRadio.value = 2;
      priceId = priceId ? priceId : prices.rent.price[0].id;
      changeRent(priceId);
    }
    else if (prices.buy?.type) {
      if (baseRadio.value && baseRadio.value === 2 && prices.rent?.type) {
        baseRadio.value = 2;
        changeRent(prices.rent.price[0].id);
      } else {
        baseRadio.value = 1;
        changeOnBuy();
      }
    }
  } else {
    total.qty = 0;
  }
}

function changeOnBuy() {
  cart.price_id = prices.buy.id;
  if (!selectedCustomFields.value.length) {
    cart.price = prices.buy.price;
  }
  cart.deposit_amount = 0;
  cart.deposite_tax = "";
  cart.rent_start = "";
  cart.rental_duration = 0;
  cart.rental_type = "buy";
  cart.term = "";
  selectedCustomFields.value = selectedCustomFields.value.filter(elem => elem.applicable_for != 2);
  checkAvailabe();
  total.term = "";
  checkPrice(true);
}

function formatBuyRent(data) {
  if (data.length > 0) {
    var prices = data[0];
    var obj = {
      buy: { type: false, price: 0, id: null },
      rent: { type: false, price: [] }
    };
    var rent = ["hourly", "daily", "weekly", "monthly"];
    if (prices.base.price > 0) {
      obj.buy["type"] = true;
      obj.buy["price"] = prices.base.price;
      obj.buy["id"] = prices.base.id;
    }
    let ren = [];
    const rentPrices = data[0];

    if (rentPrices.fixed) {
      const fp = {
        type: "",
        price: rentPrices.fixed.price,
        id: rentPrices.fixed.id,
        label: "",
        rent_start: rentPrices.fixed.rent_start,
        rent_end: rentPrices.fixed.rent_end
      };
      obj.rent["price"].push(fp);
    } else {
      for (let c in rentPrices) {
        for (let i = 0; i < rentPrices[c].length; i++) {
          rentPrices[c][i]["type"] = rentPrices[c][i].label;
          obj.rent["price"].push(rentPrices[c][i]);
        }
      }
    }
    if (obj.rent["price"].length > 0) obj.rent["type"] = true;
    return obj;
  }
  return prices;
}

async function onChangeBuyRent(v) {
  if (v === "buy") {
    const available_quantity = (details.available_for_sale || details.available_for_sale == 0)
      ? details.available_for_sale
      : details.available;
    availableQ.value = available_quantity == 1
      ? available_quantity
      : available_quantity - total.qty;
    actualAvailableQty.value = available_quantity;
    if (unavailableWhenExactStartTime.value) unavailableWhenExactStartTime.value = false;
  } else {
    if (hasBuyCartData) {
      isDisableDatePicker.value = false;
      await buyCartGetDuration();
    }
    if (enable_exact_time.value) {
      selected_exact_time.value = null;
      selectedDuration.value = null;
      if (!unavailableWhenExactStartTime.value) unavailableWhenExactStartTime.value = true;
    }

    availableQ.value = details.available == 1
      ? details.available
      : details.available - total.qty;
    actualAvailableQty.value = details.available;
  }
  changeBuyRent(v, is_recurring_product.value ? true : false);
}

function hasBuyCartData() {
  let status = false;
  let userCart = JSON.parse(localStorage.getItem('user_cart'));
  if (userCart && !userCart?.rent_end) {
    status = true;
  }
  return status;
}

async function buyCartGetDuration() {
  const cartList = localStorage.getItem("user_cart") ? JSON.parse(localStorage.getItem("user_cart")) : null;
  if (!cartDataFound.value || cartList?.rent_start) return;
  const priceId = rentelPriceId.value ? rentelPriceId.value : recurringPriceId.value;
  const obj = prices.rent.price.find(item => item.id === priceId);
  await getRentDate(obj?.id, min_date.value);
}

function changeBuyRent(v, recurring) {
  if (v === "buy") {
    baseRadio.value = 1;
    changeOnBuy();
  } else {
    baseRadio.value = 2;
    const price = {
      id: recurring ? details.recurring_prices[0].id : prices.rent.price[0].id,
      obj: recurring ? details.recurring_prices[0] : null
    }
    changeRent(price.id, price.obj);
    selectedDuration.value = "null";
    if (!rentStartDate.value) {
      total.price = temp_rent_price.value ? temp_rent_price.value : total.price;
      availableQ.value = temp_qty.value ? temp_qty.value : availableQ.value;
    }
  }
}

function changeRent(v, obj=null) {
  termFlag.value = true;
  if (details.recurring_prices && details.recurring_prices.length > 0) {
    selectedPriceObj = obj;
  }
  if (selectedPriceObj) {
    recurringPriceId.value = v;
    rentelPriceId.value = null;
  } else {
    rentelPriceId.value = v;
    recurringPriceId.value = recurringPriceId.value || null;
  }

  selectedCustomFields.value = selectedCustomFields.value.filter(elem => elem.applicable_for != 3);
  changeOnRent(v);
  calculateRentDate(v);
}

function changeOnRent(id) {
  const rentObj = prices.rent.price.find(f => f.id === id);
  if (rentObj?.duration && rentObj?.label) {
    total.term = `${rentObj?.duration} ${rentObj?.label} `;
  }

  cart.price_id = rentObj?.id;
  if (!selectedCustomFields.value.length) {
    cart.price = rentObj?.price;
  }
  cart.rental_type = rentObj?.type;
  cart.term = rentObj?.duration;
  cart.rent_start = rentObj?.rent_start;
  cart.rental_duration = 1;
  cart.deposit_amount = details.deposit_amount;
  cart.deposite_tax = details.deposite_tax;
  cart.driving_license_required = details.driving_license;

  checkAvailabe();
}
function calculateRentDate(v) {

  let obj = prices.rent.price.find(item => item.id === v);

  if (isDisableDatePicker.value || cartDataFound.value) {
    const cartDateObj = getOnlineStoreCartDate();
    if (cartDateObj?.startDate) {
      rentStartDate.value = cartDateObj.startDate;
    }
    if (cartDateObj?.endDate) {
      rentEndDate.value = cartDateObj.endDate;
    }

    let userCart = localStorage.getItem('user_cart') ? JSON.parse(localStorage.getItem('user_cart')) : null;
    if ((userCart && userCart.options == null && !userCart.options?.recurring)
      && userCart.cart_items?.length
      && isActiveRecurring.value
      && !isDisableDatePicker.value
      || cartDataFound.value
    ) {
      checkPrice();
    }

  }
  else {
    const loc = window.RENTMY_GLOBAL.locationId;
    if (!obj) {
      rentelPriceId.value = prices.rent.price[0].id;
      obj = prices.rent.price.find(item => item.id === rentelPriceId.value);
    }
    if (!enable_exact_time.value) {
      rentStartDate.value = rentStartDate.value ? rentStartDate.value : obj.rent_start;
      rentEndDate.value = rentEndDate.value ? rentEndDate.value : obj.rent_end;
    }
    rentStartDate.value = rentStartDate.value ? rentStartDate.value : obj.rent_start;
    rentEndDate.value = rentEndDate.value ? rentEndDate.value : obj.rent_end;

    if(rentStartDate.value){
      checkSameDateBooking();
    }

    enable_due_date.value = !!(details?.exact_date && details.exact_date);

    if (!enable_due_date.value && !enable_exact_time.value) {
      let userCart = JSON.parse(localStorage.getItem('user_cart'));
      if ((userCart && userCart?.options == null && !userCart.options?.recurring)
        && userCart.cart_items?.length
        && isActiveRecurring.value
        && !isDisableDatePicker.value
      ) {
        checkPrice();
      } else {

        getRentDate(obj?.id, rentStartDate.value ? rentStartDate.value : obj.rent_start);
      }
    }
  }
  if (obj?.hasOwnProperty('min_date')) {
    min_date.value = obj.min_date ? obj.min_date : '';
  } else {
    min_date.value = '';
  }
}

function checkSameDateBooking(){
  let userCart = JSON.parse(localStorage.getItem('user_cart'));
  if(userCart && userCart?.rent_start){ return }
  if(!isSameDateBooking.value && initialLoadSameDateBooking.value){
    let date = new Date(rentStartDate.value);
    date.setDate(date.getDate() + 1);
    rentStartDate.value = formatDate(date);
    initialLoadSameDateBooking.value = false;
  }
}

function checkEarliestDateActive() {
  const storeContent = localStorage.getItem("rentmy_contents") ? JSON.parse(localStorage.getItem("rentmy_contents")) : null;

  if (storeContent?.site_specific?.confg?.hasOwnProperty('show_earliest_start_date')) {
    if (!storeContent.site_specific.confg.show_earliest_start_date) {
      isAutoSelectEarliestDate.value = true;
    } else {
      isAutoSelectEarliestDate.value = false;
    }
  } else {
    isAutoSelectEarliestDate.value = false;
  }

  return isAutoSelectEarliestDate.value;

}

function _getOnlyDate(date) {
  try {
    if (date) {
      const dateArr = date.split(' ');
      return dateArr[0];
    }
  } catch (error) {
    console.error('_getOnlyDate:error', error);
  }
}

async function getRentDate(price_id, start_date) {
  try {

    if(isEnableExactTime){
      return
    }
    let userCart = JSON.parse(localStorage.getItem('user_cart'));
    if ((cartDataFound.value && userCart.rent_end) || (!start_date || start_date == null || start_date == 'null')) return;
    let initStartTime; // if initial load, then send false, otherwise start time
    if (initialStartTime.value && !checkEarliestDateActive()) {
      initStartTime = false;
      initialStartTime.value = false;
    } else { // 2021-10-03 08:00
      const times = start_date?.split(' ');
      initStartTime = times[1];
    }
    // if(isMounted.value) globalLoader.show();
    const res = await productService.getDatesPriceDuration(start_date, initStartTime, price_id)
    globalLoader.hide();
    if (res.status == "OK") {
      const data = res.result.data;
      rentStartDate.value = data.start_date;
      rentEndDate.value = data.end_date;
      if (initialLoad) {
        checkTodayTomorrow(rentStartDate.value);
      }
      checkPrice();
      initialLoad = false;
    }
  } catch (error) {
    console.error('getRentDategetRentDate:error', error, {start_date});
  }
}
function checkTodayTomorrow(rentStartDate) {


  let stDate = moment(_getOnlyDate(rentStartDate), "YYYY-MM-DD");
  let today = moment(moment().format("YYYY-MM-DD"), "YYYY-MM-DD");



  if (stDate > today) {
    isShowToday.value = false;
  } else {
    isShowToday.value = true;
  }

  let tomorrow = moment(moment().add(1, 'days').format("YYYY-MM-DD"), "YYYY-MM-DD");


  if (stDate > tomorrow) {
    isShowTomorrow.value = false;
  } else {
    isShowTomorrow.value = true;
  }

  if (isAutoSelectEarliestDate.value) {
    isShowToday.value = false;
    isShowTomorrow.value = false;
  }

}

function checkAvailabe() {
  const dqty = details.available;
  const a = dqty - checkAvailableForType();
  available.value = a < 0 ? 0 : a;
  setTotalPrice();
}

function checkAvailableForType() {
  if (cart.rental_type == "hourly") {
    return getQtyFromCart(cart.rent_start, 1);
  } else if (cart.rental_type == "weekly") {
    return getQtyFromCart(cart.rent_start, +cart.term * 7);
  } else if (cart.rental_type == "daily") {
    return getQtyFromCart(cart.rent_start, +cart.term);
  }
  return getQtyFromCart(new Date(), 14);
}

function setTotalPrice() {
  if (isDisableDatePicker.value) {
    if (cart.rental_type == "buy") {
      total.price = cart.price;
    }
    else {
      total.price = cart.price ? cart.price : details.rental_price;
      total.term = '';
    }

  }
  else {
    total.price = cart.price;
  }
}

function getQtyFromCart(date, duration) {
  const cartList = localStorage.getItem("user_cart")
    ? JSON.parse(localStorage.getItem("user_cart"))
    : null;
  const cartQty = cartList
    ? getQtyFromCartList(cartList.cart_items, details, date, duration)
    : 0;
  const availableQty = details.products_availabilities
    ? getQtyFromProductList(
      details.products_availabilities,
      date,
      duration
    )
    : 0;
  return cartQty + availableQty;
}

function getShowEndDate() {
  const contents = localStorage.getItem("rentmy_contents") ? JSON.parse(localStorage.getItem("rentmy_contents")) : null;
  if (enable_exact_time.value && !isDisableDatePicker.value) {
    return false;
  }
  if (contents?.site_specific?.confg && contents?.site_specific?.confg.hasOwnProperty("show_end_date")) {
    return contents?.site_specific?.confg.show_end_date;
  }
  return true;
}

async function getChangTime(time, duration, type=null /** example: hour */) {
  try {
    const locationId = window.RENTMY_GLOBAL.locationId
    const cart = addCartObj(); 
    
    const sendDate = {
      product_id: details.id,
      variants_products_id: details.default_variant.variants_products_id,
      start_date: cart?.rent_start?.split(' ')?.[0] || '',
      start_time: time,
      duration: parseInt(duration?.value),
      exact_times_id: duration?.id,
      type: duration?.type,
      location_id: locationId
    }; 
    if (!sendDate['duration']) return; 
    if (cartDataFound.value || !sendDate.start_date || sendDate.start_date == 'null') return; 
    const res = await productService.getDatesFromDuration(sendDate)
    if (res.status == 'OK') {
      const data = res.result.data;
      cart.rent_start = data.start_date;
      rentStartDate.value = data.start_date;
      cart.rent_end = data.end_date;
      rentEndDate.value = data.end_date;
      total.price = data.price;
      unavailableWhenExactStartTime.value = false;

      if (data.hasOwnProperty('available')) {
        availableQ.value = data.available == 1 ? data.available : data.available - total.qty;
        actualAvailableQty.value = data.available;
      }
    }
  } catch (error) {
    console.error('getChangTime:error', error);
  }
}

async function checkPrice({isIntialCall=false}={}) {  

  if (cartDataFound.value) {
    const priceId = rentelPriceId.value ? rentelPriceId.value : recurringPriceId.value;
    const obj = prices.rent.price.find(item => item.id === priceId);
    if(isMounted.value) globalLoader.show();
    let initStartTime; // if initial load, then send false, otherwise start time
    if (initialStartTime.value && !checkEarliestDateActive()) {
      initStartTime = false;
      initialStartTime.value = false;
    } else { // 2021-10-03 08:00
      const times = rentStartDate.value?.split(' ');
      initStartTime = times[1];
    }
    const res = await productService.getDatesPriceDuration(rentStartDate.value, initStartTime, obj?.id)
    globalLoader.hide();
    if (res.status == "OK") {
      const data = res.result.data;
      rentStartDate.value = data.start_date;
      rentEndDate.value = data.end_date;
    }
  }
  const cart = addCartObj();

  const contents = localStorage.getItem('rentmy_contents') ? JSON.parse(localStorage.getItem('rentmy_contents')) : null;
  const showPricingOption = (contents?.site_specific?.confg?.rental_price_option) ?
    contents?.site_specific?.confg.rental_price_option : true;
  const showEndDate = getShowEndDate();

  if (showPricingOption && !showEndDate && baseRadio.value == 2) {
    const priceId = rentelPriceId.value ? rentelPriceId.value : recurringPriceId.value;

    const obj = prices.rent.price.find(item => item.id === priceId);
    if(true){
      if(isMounted.value) globalLoader.show();
      let initStartTime; // if initial load, then send false, otherwise start time
      if (initialStartTime.value && !checkEarliestDateActive()) {
        initStartTime = false;
        initialStartTime.value = false;
      } else { // 2021-10-03 08:00
        const times = rentStartDate.value?.split(' ');
        initStartTime = times[1];
      }
      const res = await productService.getDatesPriceDuration(rentStartDate.value, initStartTime, obj?.id);
      globalLoader.hide();
      if (res?.status == 'OK') {
        const data = res.result.data;
        rentStartDate.value = data.start_date == "null" ? null : data.start_date;
        rentEndDate.value = data.end_date;
  
    
        if(isEnableExactTime && rentStartDate.value && !productDetailsStore.exactTimes.times?.length){
          await productDetailsStore.getExactTimes(product.id, rentStartDate.value.split('')[0])
          if(productDetailsStore.exactTimes.times?.length){
              let duration = productDetailsStore.exactTimes.times[0]
              await getChangTime(duration.time, duration)
              duration['isActive'] = true,
              getPriceValue(cart);
              isSelectedExactTime.value = true;
          } else {
            if(startDatePicker.value?.[0]){ 
              // will set holidays and will select first valid date and fire change_date event from EmDateTimePicker.vue
              startDatePicker.value?.[0].setHolidays()
            }
          }
        }  
  
        if (rentStartDate.value && initialLoad) {
          checkTodayTomorrow(rentStartDate.value);
          initialLoad = false;
        }
        if (productOptionValueCheck.value || selectedCustomFields.value.length) {
          cart.rent_start = data.start_date == "null" ? null : data.start_date;
          cart.rent_end = data.end_date;
          productOptionValueCheck.value = true;
          await priceValue(cart);
        }
      }
    }
    if (cartDataFound.value) {
      await priceValue(cart);
    }
  }

  if (enable_exact_time.value) {
    let showStartTime;
    if (contents?.site_specific?.confg && contents?.site_specific?.confg.hasOwnProperty('show_start_time')) {
      showStartTime = contents?.site_specific?.confg.show_start_time;
    }
    const isShowStartTimeSelection = (enable_exact_time.value && showStartTime) ? true : false;
    if (isShowStartTimeSelection) {
      if (selected_exact_time.value && selected_exact_duration) {
        await getChangTime(selected_exact_time.value, selected_exact_duration);
      }
    } else {
      if (selected_exact_duration) {
        await getChangTime(null, selected_exact_duration);
      }
    }
  } else {
    try {
      const rentEnd = cart.rent_start.split(' ');
      rentEnd[1] = '23:59';
      const rentalEndFordisabledEndDate = rentEnd.join(' ');
      let rent_end_date = '';
      if (!showEndDate) {
        rent_end_date = showPricingOption ? rentEndDate.value : rentalEndFordisabledEndDate;
      }     
      cart.rent_end = showEndDate ? cart.rent_end : rent_end_date; // here now
      await priceValue(cart);  
    } catch (error) {
      console.warn('checkPrice:2222', error);
    }

  }
}

async function priceValue(cart) {
  cart["location"] = window.RENTMY_GLOBAL.locationId
  const priceId = cart.price_id;
  cart.custom_fields = selectedCustomFields.value;
  if (isActiveRecurring.value && Object.keys(selectedRecurringPrice).length > 0) {
    cart["price_id"] = selectedRecurringPrice.id;
  }
  const res = await productService.getPriceValue(cart);
  if (res.status === 'OK') {
    endDateErrorMessage.value = '';
    if (res.result.error) {
      endDateErrorMessage.value = res.result.error;
      unavailableForRent.value = true;
      total.price = 0;
      return;
    }
    total.term = '';
    if (termFlag.value || productOptionValueCheck.value) {
      const rentPrice = prices.rent.price.find(item => item.id === priceId);
      if (rentPrice?.duration && rentPrice?.label) {
        total.term = `${rentPrice.duration} ${rentPrice.label} `;
      }
      termFlag.value = false;
      productOptionValueCheck.value = false;
    }
    total.price = res.result.data;
    availableQ.value = res.result.available == 1 ? res.result.available : (res.result.available - total.qty);
    actualAvailableQty.value = res.result.available;
    available.value = res.result.available;
    if (availableQ.value <= 0) {
      unavailableForRent.value = true;
      availableQ.value = res.result.available;
    } else {
      unavailableForRent.value = false;
    }
    const cart = addCartObj();
    cart.price = total.price;
    if (res.result?.start_date) {
      cart.rent_start = res.result.start_date;
      rentStartDate.value = res.result.start_date;
    }
    if (res.result?.end_date) {
      cart.rent_end = res.result.end_date;
      rentEndDate.value = res.result.end_date;
    }
    endDateErrorMessage.value = '';
    if (res.result?.message && typeof res.result.message == 'string') {
      endDateErrorMessage.value = res.result?.message;
    }
  }
}

function addCartObj() {
  if (cart.rental_type !== "buy") {
    cart.rental_type = "rent";
    cart.rent_start = rentStartDate.value;
    cart.rent_end = rentEndDate.value;
  }
  cart.product_id = details.id;
  if(product.booking){
    cart.booking = true
    cart.quantity = actualAvailableQtyForBooking
    cart.exact_times = productDetailsStore?.exactTimes?.times?.length ? productDetailsStore.exactTimes?.times?.filter(time => time.isActive)?.[0] : null
  } else {
    delete cart.exact_times 
    cart.booking = false
    cart.quantity = total.qty;
  }
  cart.variants_products_id = details.default_variant
    .variants_products_id
    ? details.default_variant.variants_products_id
    : "";
  (cart.location = sessionStorage.getItem("online_store")
    ? RENTMY_GLOBAL.locationId
    : ""),
    (cart.sales_tax = details.sales_tax);
  const token = localStorage.getItem("token");
  cart.token = token ? token : "";
  if (cart.token === "") {
    localStorage.removeItem('user_cart');
  }
  return cart;
}

function formatVariant() {
  if (details.variant_set_list.length > 0) {
    details.variant_set_list = details.variant_set_list.map(
      function (f) {
        f["variants"] = [];
        return f;
      }
    );
    details.variant_list.sort((a, b) => (a.selected ? -1 : 0));
    formatVariantSetList(details.variant_list, true);
  }
}

function formatVariantSetList(vValue, isInit) {
  for (const i of vValue) {
    const ind = details.variant_set_list.findIndex(
      f => f.id === i.variant_set_id
    );
    if (ind > -1) {
      details.variant_set_list[ind]["variants"].push(i);
    }
  }
  if (isInit) {
    details.variant_set_list.forEach((elem, idx) => {
      if (elem.id !== 1 && elem.name !== 'Unassigned') {
        if (elem.variants.length) {
          elem.variants.forEach((item, index) => {
            if (idx == 0 && index == 0) {
              variantChains.value[0] = item.id;
            }
          });
        }
      }
    });
  }
}

function hasActiveRecurringPayment() {
  let st = false;
  let contents = JSON.parse(localStorage.getItem("rentmy_contents"));
  if (contents?.site_specific && contents?.site_specific?.confg && contents?.site_specific?.confg?.arb) {
    let arb = contents?.site_specific?.confg?.arb;
    if (arb?.active && arb?.store_active != "standard") {
      st = true;
    }
  }
  return st;
}

function initPriceRecurring() {
  if (prices && Object.keys(prices).length > 0) {
    if (prices.rent?.type) {
      baseRadio.value = 2;
      changeRent(details.recurring_prices[0].id, details.recurring_prices[0]);
    }
    else if (prices.buy?.type) {
      if (baseRadio.value && baseRadio.value === 2 && prices.rent?.type) {
        baseRadio.value = 2;
        changeRent(details.recurring_prices[0].id, details.recurring_prices[0])
      } else {
        baseRadio.value = 1;
        changeOnBuy();
      }
    }
  } else {
    total.qty = 0;
  }
}

async function initDetailsPageFunctions() {
  try {
    contents = JSON.parse(localStorage.getItem("rentmy_contents"));
    if (product.rent_end) {
      let date = datePipe(product.rent_end);
      rentalEndDate.value = date.split(' ').slice(0, 3).join(' ');
    }
    isActiveRecurring.value = hasActiveRecurringPayment() && details?.enduring_rental;
    unitType.value = "%";
    store_config = localStorage.getItem("currency") ? localStorage.getItem("currency") : null;

    currencySymbol.value = (store_config && store_config.symbol) ? store_config.symbol : "$";
    is_recurring_product.value = details.hasOwnProperty('recurring_prices') && details?.enduring_rental && details.recurring_prices.length > 0
      ? true
      : false;
    if (is_recurring_product.value) {
      details.recurring_prices.map(p => {
        p['recurring'] = true;
      })
    }

    enable_due_date.value = !!(details?.exact_date && details.exact_date);
    if (details.hasOwnProperty('exact_time') && details.exact_time) {
      enable_exact_time.value = true;
      extact_durations.value = product.extact_durations.durations;
      extact_times.value = product.extact_durations.times;
      if (!checkEarliestDateActive) unavailableWhenExactStartTime.value = true;
    } else {
      enable_exact_time.value = false;
    }

    if (details) {
      price_type.value = details.default_variant.price_type;
      if (is_recurring_product.value && details.recurring_prices.length > 0) {
        initPriceRecurring();
      }
    }
    if (prices.buy?.type) {
      const available_quantity = details.available_for_sale
        ? details.available_for_sale
        : details.available;
      availableQ.value = available_quantity == 1
        ? available_quantity
        : available_quantity - total.qty;
      actualAvailableQty.value = available_quantity;
    } else {
      availableQ.value = details.available == 1
        ? details.available
        : details.available - total.qty;
      actualAvailableQty.value = details.available;
    }

    if (
      contents?.site_specific?.confg &&
      contents?.site_specific?.confg.checkout &&
      contents?.site_specific?.confg.checkout.hasOwnProperty(
        "online_order"
      ) &&
      contents?.site_specific?.confg.checkout.online_order ==
      false
    ) {
      is_show_addtoCart_btn.value = false;
    } else {
      is_show_addtoCart_btn.value = true;
    }
    await getAddonsProductList();
    await getCustomFieldList();
  } catch (error) {
    console.error('initDetailsPageFunctions:error', error);
  }

}

async function getAddonsProductList() {
  const res = await productService.getAddonProductListById(details.id)
  if (res.status == "OK") {
    addonsProductList.value = res.result.data;
    addonslabel.value = res.result.label;
    addonsProductList.value.map(prod => {
      let index = 0;
      if (prod.image) {
        prod[
          "img"
        ] = `${product_image}${store.value}/${prod.id}/${prod.image}`;
      } else {
        prod["img"] = RENTMY_GLOBAL?.images?.default_product_image
      }
      prod.variants.map(v => {
        if (index == 0) {
          v["min_qty"] = prod.min_quantity;
        } else {
          v["min_qty"] = 0;
        }
        index++;
      });
      return prod;
    });
  } else {
    addonsProductList.value = [];
  }
}

async function getCustomFieldList() {
  const res = await productService.getCustomFieldList(details.id)
  if (res.status == 'OK') {
    customFieldList.value = res.result.data;
    if (customFieldList.value.length) {
      customFieldList.value.forEach(elem => {
        if (['select', 'predefined_radio', 'rich_text_box'].includes(elem?.type)) {
          elem.selectedValue = '';
          elem.product_field_value.forEach(item => {
            item.showValue = customPricingDataFormat(item);
            item.active = false;
          });
          const activeCount = elem.product_field_value.filter(x => x.active == false);
          elem.activeCount = activeCount.length;     
          let isExist = customSelectFieldList.value.findIndex(item => item.id == elem.id) > -1;
          if(!isExist) {
            if(elem?.type === 'rich_text_box'){
              if(elem?.field_value){
                customSelectFieldList.value.push(elem);
              }
            }
            else if(elem.product_field_value?.length){
              customSelectFieldList.value.push(elem);
            }
          }
        }
      });
    }
  } else {
    customFieldList.value = [];
  }
  customFieldSelector(true);
}

function customPricingDataFormat(item) {
  let text = item.value;
  if (item.price_amount && item.display_price) {
    text = text + " ("; // + amount + "" + type + ")";
    text = (item.price_amount < 0) ? (text + "-") : (text + "+");
    if (item.price_type == 1) { // type percentage
      text = text + Math.abs(item.price_amount) + "%)";
    } else {
      const priceWithSymbol = currency.currencyConvert(item.price_amount);
      text = text + priceWithSymbol + ")";
    }
  }
  return text;
}

function customFieldSelector(isInitial = false) {

  selectedCustomFields.value = []; // empty selected fields
  customSelectFieldList.value.forEach(field => {
    if (field.applicable_for == 1 ||
      (field.applicable_for == 2 && baseRadio.value == 2) ||
      (field.applicable_for == 3 && baseRadio.value == 1)) {

      if (!field.is_private && field.product_field_value.length && field.type !== 'rich_text_box') {
        if (field.display_format === 'button') {
          let elObj = field.product_field_value[0];
          onFieldValueChangeForDefault(elObj.id, field, 'button');

        } else {
          let elObj = field.product_field_value[0];
          field.selectedValue = elObj.id;
          onFieldValueChangeForDefault(elObj.id, field);
        }
      }

    }
  });
  // predefineRadioCustomfieldSeleted();
  if(isInitial){
    checkPrice({isIntialCall: true})
  } 

}

function predefineRadioCustomfieldSeleted() {
  let predefinedRadioCustomField = customFieldList.value.find(el => el.is_private && el?.type == 'predefined_radio');
  if (predefinedRadioCustomField?.applicable_for == 1 ||
    (predefinedRadioCustomField?.applicable_for == 2 && baseRadio.value == 2) ||
    (predefinedRadioCustomField?.applicable_for == 3 && baseRadio.value == 1)) {

    if (predefinedRadioCustomField) {
      let elObj = predefinedRadioCustomField.product_field_value[0];
      _addToSelectedCustomField(elObj, predefinedRadioCustomField);
    }
  }

}

function onFieldValueChangeForDefault(fieldValue, field, format) {
  productOptionValueCheck.value = true;
  const value = field.product_field_value.find(v => v.id == fieldValue);
  if (format === 'button') {

    field.product_field_value.forEach(item => {
      item.active = item.id == fieldValue;
    });

    const activeCount = field.product_field_value.filter(x => x.active == false);
    field.activeCount = activeCount.length;
    if (value.active) {
      _addToSelectedCustomField(value, field);
    }

  } else {
    _addToSelectedCustomField(value, field);
  }

}

function _addToSelectedCustomField(value, field) {
  selectedCustomFields.value.push({
    id: value.id,
    value: value.value,
    name: value.name,
    label: value.label,
    amount: value.price_amount,
    type: value.price_type,
    applicable_for: field.applicable_for,
    is_private: field.is_private
  });
}

function onChangeBuyTypeSwitch(e) {
  baseRadio.value = e.target.checked ? 2 : 1;
  customFieldSelector();
  if (baseRadio.value == 2) {
    onChangeBuyRent('rent');
  } else {
    onChangeBuyRent('buy');
  }
}

// init functions
async function load() {
  
  const cartLength = localStorage.getItem("user_cart") ? JSON.parse(localStorage.getItem("user_cart")).cart_items?.length : 0;
  if (cartLength) {
    cartDataFound.value = true;
  }
  enable_exact_time.value = !!(details?.exact_time && details.exact_time);
  if (checkEarliestDateActive()) {
    _getRentalDate();
  }
  // imageFormat();
  await formatVariant();
  prices = await formatBuyRent(details.prices);
  isDisableDatePicker.value = await isCartDateExist();
  initialLoad = true;
  await initPrice();
  await initDetailsPageFunctions();
  // we are skipping isSeparateDate.value config, forefully by requirement
  isSeparateDate.value = false ?? contents?.site_specific?.confg?.inventory?.seperate_datetime_picker == true;
  onlineStore = sessionStorage.getItem("online_store") ? JSON.parse(sessionStorage.getItem("online_store")) : null;
  checkFulfilmentOption();
  selectedLocation.value = RENTMY_GLOBAL.locationId;
  loaded.value = true; 
   
  
  if(widgetDates()){
    let dates = widgetDates();
    selectstartDateTime(dates.startDate);
    selectendDateTime(dates.endDate);
    checkPrice();
    
    setTimeout(() => {
      //startDatePicker
      if(startDatePicker.value?.[0]?.setDate){
        if(startDate_useRangePicker){
          startDatePicker.value?.[0]?.setDate(dates.startDate, dates.endDate);
          startDatePicker.value?.[0]?.setTime(dates.startDate, dates.endDate);
        } else {
          startDatePicker.value?.[0]?.setDate(dates.startDate);
          startDatePicker.value?.[0]?.setTime(dates.startDate);
        }
      }
      // endDatePicker
      if(endDatePicker.value?.[0]?.setDate){
        if(endDate_useRangePicker){
          endDatePicker.value?.[0]?.setDate(dates.startDate, dates.endDate);
          endDatePicker.value?.[0]?.setTime(dates.startDate, dates.endDate);
        } else {
          endDatePicker.value?.[0]?.setDate(dates.startDate);
          endDatePicker.value?.[0]?.setTime(dates.startDate);
        }
      }
    }, 1000);
  }
  isMounted.value = true;
  globalLoader.hide();
  domElement.setWraperIsReady(wrapper); 
}
// load();

/* -------------------------------------------------------------------------- */
/*                                 COMPONENTS                                 */
/* -------------------------------------------------------------------------- */

const RentMyProductPricing = wrapper.querySelector('[RentMyProductDetilsInfo]');
// const RentMyProductPricing = wrapper.querySelector('.product-payment-details');
const RentMyRecurring = wrapper.querySelector('[RentMyRecurring]');
const RentMyVariant = wrapper.querySelector('[RentMyVariant]');
const RentMyProductOptions = wrapper.querySelector('[RentMyProductOptions]');
function formatPricingDom() {
  // product name, pricing, buy rent switching
  createComponentHTML(RentMyProductPricing, RentMyEvent.apply_filters('options:product:general', [
    {
      selector: '[RentMyProductName]',
      text: "{{ details.name }}",
    },  
    {
      selector: '[CustomFieldShortDesc] p',
      template: true,
      attr: { 
        'v-for': '(customSelectField, index) in customSelectFieldList'
       },
       child: [
        {
        selector: '__SELF__',
        attr: { 
          "v-if": "customSelectField?.type === 'rich_text_box'"
        },
        text: '<div v-html="customSelectField?.field_value" ></div>'
       }
      ]
    },
    {
      selector: '[RentMyProductPrice]',
      text: "{{ currencyConvert( product.booking ? getComputedBookingProductOptionsPrice : (( total.price) * total.qty ) ) }}",
      // text: "{{ currencyConvert(  getComputedBookingProductOptionsPrice + (( total.price) * total.qty ) ) }}",
    },
    {
      selector: '[RentMyBuyRentToggle]',
      attr: {
        'v-if': 'prices?.rent?.type && prices?.buy?.type'
      }
    },
    {
      selector: '[BuyRentToggleSwitch]',
      attr: {
        ':checked': 'baseRadio == 2',
        'v-on:click': 'onChangeBuyTypeSwitch($event)'
      }
    }
  ]));
  // recurring section funtionality
  createComponentHTML(RentMyRecurring, RentMyEvent.apply_filters('options:product:[RentMyRecurring]', [ 
    {
      selector: '[RecurringTitle]',
      text: `
            {{contents?.site_specific?.product_details?.lbl_recurring_pricing}}
              <a style="cursor: pointer;"
                  v-if="!isRentalDateShowAll && details?.recurring_prices?.length > priceLimitShowFirst"
                  @click="onClickRentalDateShowAll(details.recurring_prices)">
                  (Show All)
              </a>
          `,
    },
    {
      selector: '[RecurringItem]',
      template: true,
      attr: { 'v-for': '(item, index) in details?.recurring_prices' },
    },
    {
      selector: '[RecurringItem]',
      attr: {
        'v-if': '(index <= priceLimitShowFirst && !isRentalDateShowAll) || isRentalDateShowAll',
        ':class': "{'RecurringActive': recurringPriceId == item?.id && !isDisableDatePicker, 'disabled-cursor': isDisableDatePicker}",
        'v-on:click': "toggleRentalPriceRangePicker(item.id, item)",
      },
      text: `
            {{ contents?.site_specific?.product_details?.billed_at_a_rate ?
            contents?.site_specific?.product_details.billed_at_a_rate : "Billed at a rate of"
            }}
            {{ currencyConvert(item?.price) }}
            <template v-if="item?.duration > 1">
              for {{ item?.duration }}
              {{ item?.duration_type =="monthly" ?
              contents?.site_specific?.others?.lbl_months || "Months"
              : contents?.site_specific?.others?.lbl_weeks || "Weeks"}}
            </template>
            <template v-else>
              per
              {{ item?.duration_type =="monthly" ?
              contents?.site_specific?.others?.lbl_month || "Month"
              : contents?.site_specific?.others?.lbl_week || "Week"}}
            </template>
          `,
    }
  ]));
  // recurring section accessibility
  createComponentHTML(RentMyProductPricing, RentMyEvent.apply_filters('options:product:[RentMyRecurring]accessibility', [
    {
      selector: '[RentMyRecurring]',
      template: true,
      attr: { 'v-if': 'is_recurring_product && isActiveRecurring' },
    },
    {
      selector: '[RentMyRecurring]',
      template: true,
      attr: { 'v-if': 'baseRadio == 2 && showPricingOption() && isShowPricingForExactTimeItem' },
    },
    {
      selector: '[RentMyRecurring]',
      template: true,
      attr: { 'v-if': '(!enable_due_date && (!enable_exact_time || isDisableDatePicker))' },
    }
  ]));

  // varient section funtionality
  createComponentHTML(RentMyVariant, RentMyEvent.apply_filters('options:product:[RentMyVariant]', [
    {
      selector: '[VariantTitle]',
      attr: {
        'v-if': 'vs?.variants?.length'
      },
      text: "{{ vs?.name }} <span style='color: red'>*</span>",
    },
    {
      selector: '[VariantList]',
      attr: {
        ':class': "{'color-variants': isShowColorVariant && vs?.name?.toLowerCase() == 'color' }"
      }
    },
    {
      selector: '[VariantItem]',
      template: true,
      attr: { 'v-for': '(item, index) in vs?.variants' },
    },
    {
      selector: '[VariantItem]',
      template: true,
      attr: { 'v-if': 'item?.id' },
    },
    {
      selector: '[VariantItem]',
      attr: { ':class': "{'VariantActive': item?.selected}" },
    },
    {
      selector: '[VariantItem]',
      attr: { '@click': "changeVariant(i, item.id)" },
    },
    {
      selector: '[VariantItem]',
      attr: { ':style': "{ 'background-color': isShowColorVariant && vs?.name?.toLowerCase() == 'color' ? item?.name?.toLowerCase() : '' }" },
      text: "{{ isShowColorVariant && vs?.name?.toLowerCase() == 'color' ? '' : item?.name }}",
    },
  ]));

  // varient section accessibility
  createComponentHTML(RentMyProductPricing, RentMyEvent.apply_filters('options:product:[RentMyVariant]accessibility',
  [
    {
      selector: '[RentMyVariant]',
      template: true,
      attr: { 'v-if': 'details?.variant_set_list?.length > 0' },
    },
    {
      selector: '[RentMyVariant]',
      template: true,
      attr: { 'v-for': '(vs, i) in details.variant_set_list' },
    },
    {
      selector: '[RentMyVariant]',
      template: true,
      attr: { 'v-if': "vs.id !== 1 && vs.name !== 'Unassigned'" },
    }
  ]));

  // Product options
  createComponentHTML(wrapper, RentMyEvent.apply_filters('options:product:[RentMyProductOptions]', [
    {
        selector: '[RentMyProductOptions]',
        template: true,
        attr: { 'v-if': 'customSelectFieldList?.length > 0 && product.booking === false' },
        child: [
            {
                selector: '[ProductOptionsItem]',
                template: true,
                attr: { 'v-for': `(customSelectField, index) in customSelectFieldList.filter(cf => cf.type !== 'rich_text_box')` },
                child: [
                  {
                    selector: '__SELF__',
                    template: true,
                    attr: {
                      'v-if': `customSelectField.applicable_for == 1 ||
                          (customSelectField.applicable_for == 2 && baseRadio == 2) ||
                          (customSelectField.applicable_for == 3 && baseRadio == 1)` 
                    },
                    child: [  
                       {
                        selector: '[ProductOptionsTitle]',
                        attr: { 
                            'v-if': "customSelectField?.type != 'predefined_radio'",
                            '@click': 'log({customSelectField, customSelectFieldList, "display_format": customSelectField?.display_format})'                                                
                          },
                        text: "{{ customSelectField?.label }}",
                      },

                      /* -------------------------------------------------------------------------- */
                      /*                                 type-select                                */
                      /* -------------------------------------------------------------------------- */
                      {
                        selector: '[type-select]',
                        template: true,
                        attr: {
                          "v-if": "customSelectField?.display_format != 'button' && customSelectField?.type === 'select'",
                        },
                        child: [
                          {
                            selector: '[fieldLabel]',
                            text: '{{ customSelectField?.label }}'
                          },
                          {
                            selector: 'select',
                            attr: {
                              "v-model": "customSelectField.selectedValue",
                              ":dispaly_format": "customSelectField?.display_format",
                              ":field_type": "customSelectField?.type",
                              "@change": "onFieldValueChange($event.target.value, customSelectField)",
                              
                            },
                            text: `
                              <template v-for="(field, index2) in customSelectField?.product_field_value || []">
                                  <option :value="field?.id">{{ field?.showValue }}</option>
                              </template>
                            `
                          },
                        ],
                      },
                                   
                      /* -------------------------------------------------------------------------- */
                      /*                                 type-button                                */
                      /* -------------------------------------------------------------------------- */
                      {
                        selector: '[type-button]',
                        template: true,
                        attr: {
                          // "v-if": "customSelectField?.display_format != 'button' && customSelectField?.type === 'predefined_radio'",
                          "v-if": "customSelectField?.display_format == 'button' && customSelectField?.type === 'select'",
                        },
                        child: [
                          {
                            selector: '[fieldLabel]',
                            text: '{{ customSelectField?.label }}'
                          },
                          {
                            selector: '[fieldValue]',
                            attr: {
                              'v-for': '(field, index2) in customSelectField?.product_field_value || []',
                              '@contextmenu': 'log(field, customSelectField?.product_field_value)',
                              '@click.stop': 'onFieldValueChange(field?.id, customSelectField);helper.toggleLoopItem(customSelectField?.product_field_value, -1);helper.toggleLoopItem(customSelectField?.product_field_value, index2)',
                              ':class': `{'active': field?.isShow}`
                            },
                            text: `{{ field?.showValue }} `
                          }
                        ],
                        
                      },
                      /* -------------------------------------------------------------------------- */
                      /*                                 type-richtext                              */
                      /* -------------------------------------------------------------------------- */
                      {
                        selector: '[type-richtext]',
                        template: true,
                        attr: {
                          "v-if": "customSelectField?.type === 'rich_text_box' && false", // rich text never show in product options
                        },
                        child: [
                          {
                            selector: '[fieldLabel]',
                            text: '{{ customSelectField?.label }}'
                          },                          
                          {
                            selector: '[fieldValue]',
                            text: '<div v-html="customSelectField?.field_value" ></div>'
                          },                      
                        ],
                        
                      },
                    ], 
                  }
                ],
                  
            },
        ]
    },
  ]));


  // Product options for booking
  createComponentHTML(wrapper, RentMyEvent.apply_filters('options:product:[RentmyTicketBookingProductOptionsArea]', [
    {
        selector: '[RentmyTicketBookingProductOptionsArea]',
        template: true,
        attr: { 'v-if': 'product.booking === true && customSelectFieldList?.length > 0' },
        child: [
            {
                selector: '[ProductOptionsTitle]',
                text: `{{ customSelectFieldList?.[0]?.label }} `,
            },
            {
              selector: '[QtyContainer]',
              template: true,
              attr: {
                'v-if': 'show_checkout_availability_text',
              },
              child: [
                {
                  selector: '[RentmyAvailableLabel]', 
                  text: `
                      {{ allowDyanmicContent ? contents.site_specific.product_details.available : '__EXISTING_HTML__' }}         
                  `,
                },
                {
                    selector: '[RentmyAvailableQty]',
                    text: `{{ actualAvailableQty <= 0 ? 0 : actualAvailableQty }} `,
                },
              ]
            },
            {
                selector: '[ProductOptionsItem]',
                template: true,
                attr: { 'v-for': '(customSelectField, index) in customSelectFieldList' },
                child: [  
                   {
                    selector: '__SELF__',
                    template: true,
                    attr: {
                      'v-for': '(productField, i) in customSelectField?.product_field_value || []',
                      ':key': 'i',
                      "v-if": "customSelectField?.type !== 'rich_text_box'"
                    },
                    child: [

                      // input
                      {
                        selector: '[RentmyQtInput]', 
                        attr: {
                          ':value': 'productField?.quantity ?? 0',
                          ':readonly': 'true'
                        }, 
                      },
                      // Decreament (-)
                      {
                        selector: '[RentmyQtMinusBtn]', 
                        attr: {
                          '@click.stop': `bookingOptionIncrDecr(index, i, 'minus')`, 
                        }, 
                      },
                      // Icreament (+)
                      {
                        selector: '[RentmyQtPlusBtn]', 
                        attr: {
                          '@click.stop': `bookingOptionIncrDecr(index, i, 'plus')`, 
                        }, 
                      },
                      


                      /**
                       * ======== lable and price
                       */
                      // label
                      {
                        selector: '[fieldLabel]', 
                        attr: {
                          '@click': 'log({productField})'
                        },
                        text: `{{ bookingOptionFieldLabel(productField) }}`
                      },
                      // price
                      {
                        selector: '[fieldValue]', 
                        text: `{{ currency.currencyConvert(bookingOptionFieldPrice(productField)) }}`
                      },
                    ]
                   }    
                                
                   
                  
                ], 
                  
            },
        ]
    },
  ]));


  // RENTAL DATE RANGE SELECTION
  createComponentHTML(RentMyProductPricing, RentMyEvent.apply_filters('options:product:[RentMyProductDetilsInfo]rental_date_range_selection', [
    {
      selector: '[RentMyRentalStartDate]',
      attr: {
        'v-if': "baseRadio == 2"
      },
    },
    {
      selector: '[dueDate]',
      attr: {
        'v-if': "enable_due_date"
      },
      text: `<h4 class="due-date-title"> <i class="fa fa-calendar me-1"></i> {{rentalEndDate }} </h4>`,
    }
  ]));

  createComponentHTML(wrapper, RentMyEvent.apply_filters('options:product:[RentalStartDate]start_date', [
    {
      selector: '[RentMyRentalStartDate]',
      attr: {
        'v-if': '!product?.exact_date'
      }, 
      child: [
        {
          selector: '[RentalStartDateTitle]',
          attr: {
            'v-if': `
              (isShowToday 
              || isShowTomorrow
              || showStartDate()
              || checkRentTime(rentStartDate, 'start'))
              && (!SHOWING_START_AND_END_DATE)
            `            
          },
          text: `{{ product.booking ? 'Start Date' : contents?.site_specific?.product_details?.lbl_rental_start_date }}`,
        },
        {
          selector: '[RentalStartDateSelectedLabel]',
          attr: {            
            'v-if': `
              (isShowToday 
              || isShowTomorrow
              || showStartDate()
              || checkRentTime(rentStartDate, 'start'))
              && (!SHOWING_START_AND_END_DATE) 
            `
          },
          text: `
          <span v-if="checkRentTime(rentStartDate, 'start')">
              <i v-if="checkRentTime(rentStartDate, 'start')" class="fa fa-calendar me-1"></i> 

              <template v-if="startDate_useRangePicker">
                {{ checkRentTime(rentStartDate, 'start')}} - {{ checkRentTime(rentEndDate, 'end') }}
              </template>
              <template v-else>
                {{ checkRentTime(rentStartDate, 'start')}}
              </template>


              <template v-if="false">
                <span v-if="product.booking === false" class="timepicker-icon"
                    :class="{'startdate-active': isDisableDatePicker, 'disabled-cursor': isDisableDatePicker, 'timepicker-disable' : isDisableDatePicker}"
                    v-if="isSeparateDate && showStartTime && checkRentTime(rentStartDate, 'start')"
                  >
                
                  <i class="fa fa-clock-o pl-2"></i>
                  <div DateTimePicker>
                    <div StartTimePicker id="time_picker"></div> 
                  </div> 
                </span>
              </template>
          </span>
          `
        },
        {
          selector: '[Today]',
          attr: {
            'v-if': "isSameDateBooking && isShowToday && product.booking === false", //asdf
            ':class': "{'startdate-active': (startDateFlag == 'today' && !isDisableDatePicker) && !isNonrecurring, 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
            '@click.stop': "toggleStartDatePicker(false, 'today')"
          },
          text: `{{ contents?.site_specific?.product_details?.lbl_today ? contents.site_specific.product_details.lbl_today : "Today" }}`,
        },
        {
          selector: '[Tomorrow]',
          attr: {
            'v-if': "isShowTomorrow && product.booking === false", //asdf
            ':class': "{'startdate-active': (startDateFlag == 'tomorrow' && !isDisableDatePicker) && !isNonrecurring, 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
            '@click.stop': "toggleStartDatePicker(false, 'tomorrow')"
          },
          text: `{{ contents?.site_specific?.product_details?.lbl_tomorrow ? contents.site_specific.product_details.lbl_tomorrow : "Tomorrow" }}`,
        },
        {
          selector: '[PickDate]',
          attr: {
            'v-if': "showStartDate() && SHOWING_START_AND_END_DATE === false",
            ':class': "{'startdate-active': (startDateFlag == 'pick_start' && !isDisableDatePicker) && !isNonrecurring, 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
            '@click.stop': "toggleStartDatePicker(true, 'pick_start')"
          },
          text: `{{ contents?.site_specific?.product_details?.start_date ? contents.site_specific.product_details.start_date : "Pick Start Date" }} 
          <div DateTimePicker>
              <div StartDatePicker></div> 
            </div>
          `,
        },
      ]
    },   
    
    

  ]));


  // Exact times  
  createComponentHTML(wrapper, RentMyEvent.apply_filters('options:product:[RentMyBookingExactTimes]start_date', [
    {
      selector: '[RentMyBookingExactTimes]',
      template: true,
      attr: {
        'v-if': 'isEnableExactTime'
      },
      child: [
        {
          selector: '[TimeArea]',
          attr: {
              'v-if': 'productDetailsStore.exactTimes?.times?.length > 0'       
          }, 
          child: [
            {
              selector: '[TitleTag]',
              attr: { // just for log
                '@click.stop': 'log({exactTimes: productDetailsStore.exactTimes})'
              }
            },
            {
              selector: '[exactTimeItem]',
              template: false,
              attr: {
                'v-for': '(duration, i) in productDetailsStore.exactTimes.times',
                ':class': `{'timeActive': duration?.isActive}`,
                '@contextmenu': 'log(duration)',
                '@click.stop': `async ()=>{
                  globalLoader.show();
                  await getChangTime(duration.time, duration)
                  helper.toggleLoopItem(productDetailsStore.exactTimes.times, -1, 'isActive'); 
                  helper.toggleLoopItem(productDetailsStore.exactTimes.times, i, 'isActive'); 
                  getPriceValue(cart);
                  isSelectedExactTime = true;
                  globalLoader.hide();
                }`,
              },
              text: '{{ duration?.time }}'
            }
          ]
        }, 
        {

        },
        {
          selector: '[TourNotAvailableMessageArea]',
          attr: {
              'v-if': 'productDetailsStore.exactTimes?.showWarningMessage',
          }, 
          text: `This tour is not available on the date you selected. Please pick another date.`
        }, 
      ]
    },   
     

  ]));

  // START DATE FUNTIONALITY
  createComponentHTML(wrapper, RentMyEvent.apply_filters('options:product:[RentMyRentalDateRange]start_date_functionality', [
    {
      selector: '[RentMyRentalDateRange]',
      template: true,
      attr: {
        'v-if': 'product.booking === false'
      },
      child: [
        {
          selector: '[RentalDateRangeTitle]',
          attr: { 'v-if': 'showPricingOption()' },
          text: `{{ allowDyanmicContent ? contents?.site_specific?.product_details?.lbl_rental_date_range : '__EXISTING_HTML__' }}
            <a style="cursor: pointer;"
                v-if="!isRentalDateShowAll && prices.rent.price?.length > priceLimitShowFirst"
                v-on:click="onClickRentalDateShowAll(prices.rent.price)">
                {{ contents?.site_specific?.others?.txt_show_all || '(Show All)' }}
            </a>
          `,
        },
        {
          selector: '[RentalDateRangeList]',
          attr: {
            'v-if': "showPricingOption()",
    
          },
        },
        {
          selector: '[RentalDateRangeItem]',
          template: true,
          attr: {
            'v-for': "(item, i) in prices?.rent?.price",
          },
        },
        {
          selector: '[RentalDateRangeItem]',
          attr: {
            'v-if': "(i < priceLimitShowFirst && !isRentalDateShowAll) || isRentalDateShowAll",
            ':class': "{'DaterangeActive': (rentelPriceId == item.id && !isDisableDatePicker) && !isNonrecurring, 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
            '@click': "toggleRentalPriceRangePicker(item.id, item)"
          },
          child: [
            {
              selector: '[setActiveClass]',
              attr: {
                ':class': "{'DaterangeActive': (cart?.rent_start === item?.rent_start && cart?.rent_end === item?.rent_end) }",
              },
            },
            {
              selector: '[PricePreText]',
              text: `{{ item?.duration }} {{ getDurationUnit(item?.duration > 1 ? item?.type + 's' : item?.type) }}`
            },
            {
              selector: '[Devider]',
              attr: { 'v-if': 'item?.duration' }
            },
            {
              selector: '[Price]',
              text: `{{ currencyConvert(item?.price) }}`
            },
          ]
        }
      ]
    },
  ]));



  // EXACT SELECT DURATION
  const RentMyExactSelectDuration = wrapper.querySelector("[RentMyExactSelectDuration]");
  createComponentHTML(RentMyExactSelectDuration, RentMyEvent.apply_filters('options:product:[RentMyExactSelectDuration]', [
    {
      selector: '[RentMyExactSelectDurationTitle]',
      attr: {},
      text: `{{contents.site_specific.product_details &&
            contents.site_specific.product_details.exact_select_duration ?
            contents.site_specific.product_details.exact_select_duration : 'Select Duration'}}`
    },
    {
      selector: '[RentMyExactSelectDurationItem]',
      attr: {
        "v-for": "(duration, i) in extact_durations",
        ":class": "{'daterange-active': (selectedDuration == (duration.value +','+duration.type +','+duration.id) && !isDisableDatePicker) && !isNonrecurring, 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
        "@click": "onDurationChange(duration.value +','+duration.type +','+duration.id)"
      },
      text: `{{duration.label}}`
    }
  ]));

  // EXACT SELECT TIME  
  const RentMyExactSelectTime = wrapper.querySelector("[RentMyExactSelectTime]");
  createComponentHTML(RentMyExactSelectTime, RentMyEvent.apply_filters('options:product:[RentMyExactSelectTime]', [
    {
      selector: '[RentMyExactSelectTimeTitle]',
      attr: {},
      text: `{{ contents.site_specific.product_details && contents.site_specific.product_details.exact_select_start_time ? contents.site_specific.product_details.exact_select_start_time : 'Select Start time'}}`
    },
    {
      selector: '[RentMyExactSelectTimeItem]',
      attr: {
        "v-for": "(e_time,i) in extact_times",
        ":class": "{'daterange-active': (selected_exact_time == e_time && !isDisableDatePicker) && !isNonrecurring , 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
        "@click": "changTime(e_time)"
      },
      text: `{{e_time}}`
    }
  ]));

  // RENTAL DATES ERROR MESSAGE
  const RentmyRentalEnddate = wrapper.querySelector('[RentmyRentalEndDate]');

  // START END DATE ACCESSIBILITY
  createComponentHTML(wrapper, RentMyEvent.apply_filters('options:product:[RentmyRentalEndDate]', [
    {
      selector: '[RentMyRentalEndDate]',
      attr: {
        "v-if": "showEndDate() && !enable_due_date && product.booking === false",       
      },
    },
    {
      selector: '[RentalEndDatePicker]',
      attr: {
        "v-if": "showEndDate() && !enable_due_date && product.booking === false",
        ":class": "{'daterange-active': (endDateFlag == 'pick_end' && !isDisableDatePicker) && !isNonrecurring , 'disabled-cursor': isDisableDatePicker || isNonrecurring}",
        "@click.stop": "toggleEndDatePicker(true, 'pick_end')",
      },
      text: `__EXISTING_HTML__
        <div DateTimePicker>
            <div EndDatePicker></div> 
        </div>
        `,
    },
    {
      selector: '[RentalEndDateSelectedLabel]',
      template: true,
      attr: {
        "v-if": "showEndDate() && !enable_due_date"
      },
      text: `<i v-if="checkRentTime(rentEndDate, 'end')" class="fa fa-calendar me-1"></i> 
        <template v-if="endDate_useRangePicker">
          {{ checkRentTime(rentStartDate, 'start')}} - {{ checkRentTime(rentEndDate, 'end') }}
        </template>
        <template v-else>
          {{ checkRentTime(rentEndDate, 'end') }}
        </template>
       
      `,
    }
  ]));

  // START END DATE ACCESSIBILITY
  createComponentHTML(RentMyProductPricing, RentMyEvent.apply_filters('options:product:[RentMyProductDetilsInfo]start_date_accesibility', [
    {
      selector: '[RentMyRentalDateRange]',
      attr: {
        'v-if': "showPricingOption()"
      },
    },
    {
      selector: '[RentMyRentalDateRange]',
      template: true,
      attr: {
        'v-if': "(baseRadio == 2 && !(enable_due_date || enable_exact_time)) && isStandardPrpduct"
      },
    },
    {
      selector: "[RentMyRentalEndDate]",
      template: true,
      attr: {
        'v-if': "(baseRadio == 2 && !(enable_due_date || enable_exact_time)) && showEndDate() && !enable_due_date"
      },
    },
    {
      selector: '[RentMyExactSelectTime]',
      attr: {
        "v-if": "baseRadio == 2 && enable_exact_time && !this.isDisableDatePicker && extact_times.length>0 && isShowStartTimeSelection",
      },
      text: false
    },
    {
      selector: '[RentMyExactSelectDuration]',
      attr: {
        "v-if": "baseRadio == 2 && enable_exact_time && !this.isDisableDatePicker",
      },
      text: false
    }
  ]));

  
  // Wishlist section
  createComponentHTML(wrapper,  [
    {
      selector: '[RentMyAddToWishlistBtn]',
      attr: {
        'v-if': "is_active_wish_list",
        '@click.stop': `async ()=>{
          globalLoader.show()
          await wishlistStore.addToList({productDetails: product, rental_type: baseRadio == 1 ? 'buy' : 'rent'})
          globalLoader.show()
        }`
      },
    },
     
  ]);

  
  // RENTAL DATES ERROR MESSAGE
  createComponentHTML(wrapper, [
    {
        selector: '[RentmyAvailableNotice]',
        attr: {}, 
        text: `
        <div v-if="endDateErrorMessage">
            <small class="text-danger"> {{endDateErrorMessage}} </small>
        </div>
        <div
            v-if="actualAvailableQty <= 0 && contents.site_specific.confg.checkout?.multiple_location">
            <small class="text-danger">
                {{contents.site_specific.product_details?.lbl_select_location}}
            </small>
        </div>

        <div v-if="availableQ <= 0 && is_show_not_available_message && !contents.site_specific.confg.checkout?.multiple_location">
            <small style="color: red;">
                {{contents.site_specific.product_details &&
                contents.site_specific.product_details.not_available_text ?
                contents.site_specific.product_details.not_available_text : 'This product is not available.'}}
            </small>
        </div>
        <div v-if="unavailableForRent">
            <small style="color: red;">{{contents?.site_specific?.custom?.msg_selected_quantity_is_not_available ? contents?.site_specific?.custom?.msg_selected_quantity_is_not_available : 'Selected quantity is not available'}}</small>
        </div>
    `,
    },
  ]);

  // FULLFILMENT OPTION SELECT
  createComponentHTML(RentMyProductPricing, RentMyEvent.apply_filters('options:product:[RentMyProductDetilsInfo]fulfilment_option_select', [
    {
      selector: '[RentMyDeliveryOptions]',
      template: true,
      attr: { 'v-if': 'isShowFulfilmentOption' },
    },
    {
      selector: '[RentMyDeliveryOptions]',
      template: true,
      attr: { 'v-if': 'fulfilmentOptionList.length > 1' },
    },
    {
      selector: '[DeliveryOptionsTitle]',
      attr: {},
      text: `{{ contents?.site_specific?.product_details?.lbl_fulfillment_option }}`,
    },
    {
      selector: '[DeliveryOptionsItem]',
      attr: {
        ':class': `{'StartDateActive': fullfilment_type == item.id, 'disabled-cursor': isDisabledFulfillmentOption}`,
        'v-for': `(item,i) in fulfilmentOptionList`,
        '@click': "onClickSelectFulfilment(item)"
      },
      text: `{{ item.name }}`,
    },
  ]));

  // CHANGE LOCATION 
  createComponentHTML(RentMyProductPricing, RentMyEvent.apply_filters('options:product:[RentMySelectLocation]', [
    {
      selector: '[RentMySelectLocation]',
      template: true,
      attr: {
        'v-if': 'contents.site_specific?.confg?.checkout?.multiple_location && onlineStore?.locations?.length > 1'
      },
    },
    {
      selector: '[SelectLocationTitle]',
      attr: {},
      text: `{{ allowDyanmicContent ? contents?.site_specific?.custom?.product_page_location : '__EXISTING_HTML__' }}`,
    },
    {
      selector: '[SelectLocationList]',
      attr: {
        'v-if': `onlineStore?.locations?.length > 1`,
      },
    },
    {
      selector: '[SelectLocationItem]',
      attr: {
        'v-for': "(loc,i) in onlineStore?.locations",
        ':class': `{'LocationActive': selectedLocation == loc.id }`,
        '@click': "onSelectLoc(loc)"
      },
      text: `{{ loc.name }}`,
    }

  ]));

  // QUANTITY
  createComponentHTML(wrapper, RentMyEvent.apply_filters('options:product:[RentmyQuantityContainer]', [
    {
      selector: '[RentmyQuantityContainer]',
      template: true,
      attr: {
        'v-if': 'product.booking === false', 
      },
      child: [
        {
          selector: '[QuantityContainerTitle]', 
          text: `{{ allowDyanmicContent ? contents.site_specific.product_details.quantity: '__EXISTING_HTML__' }}`,
        },
        {
          selector: '[QuantityDecrementBtn]',
          attr: {
            '@click': "decreaseQty()"
          },
        },
        {
          selector: '[NumberOfQuantity]',
          attr: {
            'v-model': "total.qty",
            ':disabled': 'true',
            '@keydown': "validateQuantity($event)",
            '@keyup': "onQuantityKeyup($event.target.value)"
          },
        },
        {
          selector: '[QuantityIncrementBtn]',
          attr: {
            '@click': "increaseQty()"
          },
        },
        {
          selector: '[QuantityIncrementBtn]',
          attr: {
            '@click': "increaseQty()"
          },
        },
        {
            selector: '[RentmyAvailableLabel]',
            attr: {
              'v-if': 'show_checkout_availability_text' 
            },
            text: ` {{ allowDyanmicContent ? contents.site_specific.product_details.available : '__EXISTING_HTML__' }}: `,
        },
        {
            selector: '[RentmyAvailableQty]',
            attr: {
              'v-if': 'show_checkout_availability_text'
            },
            text: `{{ actualAvailableQty <= 0 ? 0 : actualAvailableQty }} `,
        },
        {
            selector: '[RentmyAvailableNotice]',
            attr: {},
            text: `
            <div
                v-if="actualAvailableQty <= 0 && contents.site_specific.confg.checkout?.multiple_location">
                <small class="text-danger">
                    {{contents.site_specific.product_details?.lbl_select_location}}
                </small>
            </div>
    
            <div v-if="availableQ <= 0 && is_show_not_available_message && !contents.site_specific.confg.checkout?.multiple_location">
                <p style="color: red;">
                    {{contents.site_specific.product_details &&
                    contents.site_specific.product_details.not_available_text ?
                    contents.site_specific.product_details.not_available_text : 'This product is not available.'}}
                </p>
            </div>
            <div v-if="unavailableForRent">
                <p style="color: red;">{{contents?.site_specific?.custom?.msg_selected_quantity_is_not_available ? contents?.site_specific?.custom?.msg_selected_quantity_is_not_available : 'Selected quantity is not available'}}</p>
            </div>
        `,
        },
      ]
    },
  ]));

  // did for Ionic-rental
  const StockLabels = wrapper.querySelector('[StockLabels]'); 
  createComponentHTML(StockLabels, RentMyEvent.apply_filters('options:product:[StockLabels]', [   
    {
      selector: '[InStock]',
      attr: {
        'v-if': "actualAvailableQty != 0"
      },
    },
    {
      selector: '[OutStock]',
      attr: {
        'v-if': "actualAvailableQty == 0"
      },
    },
    {
      selector: '[Devider]',
      attr: {
        'v-if': "actualAvailableQty != 0"
      },
    },
    {
      selector: '[QuantityArea]',
      attr: {
        'v-if': "actualAvailableQty != 0"
      },
      child: [
        {
          selector: '[Quantity]',  
          attr: { ':value': 'actualAvailableQty', ':disabled': 'true' } ,     
          text: `{{ actualAvailableQty <= 0 ? 0 : actualAvailableQty }}`,
        },
      ],
    },    
    
  ]));
  // Product ID
  createComponentHTML(wrapper, RentMyEvent.apply_filters('options:product:[ProductIdArea]', [
    {
      selector: '[ProductIdArea]',
      attr: {
        'v-if': "contents?.site_specific?.confg?.online_store?.show_product_id"
      },
      child: [
        {
          selector: '[ProductIdNo]',
          text: `${product?.id}`,
        },
      ],

    }
  ]));

  // ADD TO CART
  const RentMyCartBtnArea = wrapper.querySelector('[RentMyCartBtnArea]');

  createComponentHTML(RentMyCartBtnArea, RentMyEvent.apply_filters('options:product:[RentMyCartBtnArea]', [
    {
      selector: '[RentMyAddCartBtn]',
      attr: {
        '@click.stop': 'addTocart()',
        // '@click': `log({
        //   isAddToCartLoading,
        //   availableQ: [availableQ <= 0, is_show_not_available_message],
        //   'prices.buy|rent': !prices.buy?.type && !prices.rent?.type,
        //   unavailableForRent,
        //   isRentalDateNull: isRentalDateNull(),
        //   unavailableWhenExactStartTime: [unavailableWhenExactStartTime, !isDisableDatePicker],
        //   isShowRecurrignWarn,     
        // })`,
        ':disabled': `
        isAddToCartLoading 
        || (availableQ <= 0 && is_show_not_available_message) 
        || (!prices.buy?.type && !prices.rent?.type) 
        || unavailableForRent 
        || isRentalDateNull()
        || ( unavailableWhenExactStartTime && !isDisableDatePicker ) 
        || isShowRecurrignWarn
        || (product.booking && !getComputedBookingProductOptionsPrice)
        || (isEnableExactTime && !isSelectedExactTime)
        `
      },
      text: `{{ allowDyanmicContent ? contents.site_specific.product_details.add_to_cart : '__EXISTING_HTML__' }}`,
    },
    {
      selector: '[RentMyAddCartBtn]',
      template: true,
      attr: {
        'v-if': 'is_show_addtoCart_btn'
      },
    }
  ]))

}
formatPricingDom();

const pricingHtml = RentMyProductPricing.innerHTML
RentMyProductPricing.innerHTML = '';



const SHOW_START_DATE = storeContent?.site_specific?.confg?.show_start_date;
const SHOW_END_DATE = storeContent?.site_specific?.confg?.show_end_date;
const SHOWING_START_AND_END_DATE = SHOW_START_DATE && SHOW_END_DATE && product?.booking === false; //asdf
let endDate_useRangePicker = (RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_useRangePicker_for_endDate ?? false) && SHOW_END_DATE;
let startDate_useRangePicker = (RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_useRangePicker_for_startDate ?? false) && SHOW_END_DATE;

const rentmyComponent = defineAsyncComponent(
  () =>
    new Promise((resolve, reject) => {
      resolve({
        template: `<div>${pricingHtml}</div>`,
        components: {
          EmDateTimePicker,
        },
        data() {
          return {
            helper,
            globalLoader,
            startDatePicker,
            endDatePicker,
            startDate_useRangePicker,
            endDate_useRangePicker,
            SHOWING_START_AND_END_DATE,
            details,
            product,
            cartLoad,
            price_type,
            is_show_addtoCart_btn,
            cartItemList,
            addonsProductList,
            customFieldList,
            deliveryOptionList,
            customSelectFieldList,
            addonslabel,
            store,
            enable_due_date,
            enable_exact_time,
            extact_durations,
            extact_times,
            extact_times_values,
            exact_times,
            selectedDuration,
            is_showCustomDatetime_label,
            isShowPricingForExactTimeItem,
            is_show_not_available_message,
            endDateFlag,
            priceLimitShowFirst,
            isRentalDateShowAll,
            deliveryFlow,
            selectedDeliveryFlow,
            selectedDeliveryFlowId,
            isDisabledFulfillmentOption,
            isActiveMultiDistance,
            isNonrecurring,
            isShowFulfilmentOption,
            fulfilmentOptionList,
            fullfilment_type,
            isSameDateBooking,
            contents,
            description,
            delivery_settings,
            is_recurring_product,
            rentalEndDate,
            customFieldPrices,
            unitType,
            currencySymbol,
            store_config,
            fullfilment_option,
            deliveryLimitWarningLbl,
            deliveryLimitErrorLbl,
            isHideFulfillment,
            isAddToCartLoading,
            currentLocation,
            onlineStore,
            selectedLocation,
            current_quantity,
            quantity_limit,
            isDisplayMiniCart,
            isShowColorVariant,
            variantChains,
            rentEndDate,
            rentStartDate,
            min_date,
            images,
            featureImage,
            defaultImage,
            baseRadio,
            available,
            rentelPriceId,
            recurringPriceId,
            cartList,
            isDisableDatePicker,
            selectedPriceObj,
            selectedRecurringPrice,
            termFlag,
            selected_exact_time,
            selected_exact_duration,
            unavailableWhenExactStartTime,
            availableQ,
            actualAvailableQty,
            actualAvailableQtyForBooking,
            unavailableForRent,
            productOptionValueCheck,
            selectedCustomFields,
            isShowToday,
            isShowTomorrow,
            endDateErrorMessage,
            initialStartTime,
            isApply,
            startTime,
            isSeparateDate,
            startDateFlag,
            isActiveRecurring,
            isShowRecurrignWarn,
            isAutoSelectEarliestDate,
            autoSelectStartDate,
            temp_rent_price,
            temp_qty,
            cartDataFound,
            availableList,
            invalid,
            hourlyCalenderData,
            hourlyCalenderTable,
            editDate,
            prices,
            total,
            cart,
            productDetailsStore,
            currency,
            isEnableExactTime,
            isSelectedExactTime,
            show_checkout_availability_text,
            allowDyanmicContent,
            initialLoadSameDateBooking,
            wishlistStore,
            is_active_wish_list,
          }
        },
        methods: {
          log,
          InitFunction,
          currencyConvert,
          getDeliveryOptionList,
          onClickSelectDeliveryFlow,
          onClickSelectFulfilment,
          isDisabledDeliveryFlow,
          disabledDeliveryFlow,
          saveDeliveryFlow,
          getCustomFieldList,
          predefineRadioCustomfieldSeleted,
          customFieldSelector,
          customPricingDataFormat,
          getAddonsProductList,
          initPriceRecurring,
          onVariantQtyKeyup,
          showPricingOption,
          isAddonsProductCombinationOk,
          addTocart,
          redirectAddToCart,
          changeVariant,
          variantChain,
          showEndDate,
          showEndTime,
          showStartTime,
          showStartDate,
          getLastvariantChain,
          checkRecurring,
          validateQuantity,
          onQuantityKeyup,
          decreaseQty,
          increaseQty,
          modifyAddonsProductVariantQuantity,
          onDurationChange,
          changTime,
          getChangTime,
          isShowStartTimeSelection,
          formatPriceDurationTerm,
          getDurationUnit,
          onChangeBuyRentRadio,
          onChangeBuyRent,
          onFieldValueChange,
          onFieldValueChangeForDefault,
          _addToSelectedCustomField,
          selectField,
          onChangeBuyTypeSwitch,
          toggleStartDatePicker,
          toISOLocal,
          toggleEndDatePicker,
          toggleRentalPriceRangePicker,
          onClickRentalDateShowAll,
          checkRentTime,
          formatDate,
          isStandardPrpduct,
          hasRecurringPrice,
          checkFulfilmentOption,
          disabledFulfillmentOption,
          checkEarliestDateActive,
          _setRentalDate,
          isRentalDateNull,
          addItemToSubscribe,
          _subscribeProductQty,
          isActiveSubscription,
          onSelectLoc,
          onChangeLocation,
          showLocationAlert,
          load,
          changeFeatureImage,
          changeBuyRent,
          changeRent,
          calculateRentDate,
          getRentDate,
          _getOnlyDate,
          checkTodayTomorrow,
          checkPrice,
          getShowEndDate,
          getPriceValue,
          getQtyFromCart,
          checkAvailableForType,
          checkAvailabe,
          callInitPrice,
          initPrice,
          imageFormat,
          setTotalPrice,
          changeOnRent,
          changeOnBuy,
          formatBuyRent,
          changeVariantCall,
          formatVariant,
          formatVariantSetList,
          formatVariantList,
          variantChainRes,
          getLastvariantChainCall,
          addCartObj,
          diabledAddtoCart,
          getTimeFromManualChange,
          getDateFromManualChange,
          selectstartDateTime,
          _getDateForNoTime,
          selectendDateTime,
          _getRentalDate,
          getAvailableList,
          getHourlyData,
          splitArray,
          hasHourlyAvailabilty,
          buyCartGetDuration,
          hasBuyCartData, 
          doublePageDatepickerConfig,
          usingInPageCartWidget: document.querySelector('.RentMyWrapperInpageCartWidget'),
          bookingOptionFieldLabel: function(item){
            let text = item.value;
            if (item.price_amount && item.display_price) {
              text = text + " ("; // + amount + "" + type + ")";
              text = (item.price_amount < 0) ? (text + "-") : (text + "+");
              if (item.price_type == 1) { // type percentage
                text = text + Math.abs(item.price_amount) + "%)";
              } else {
                const priceWithSymbol = currency.currencyConvert(item.price_amount);
                text = text + priceWithSymbol + ")";
              }
            }
            return text;
          },
          bookingOptionFieldPrice: function(item){
              let price = total.price;
              let calculatedPrice = 0
              // item.showValue = customPricingDataFormat(item); // re-format price amount and price type to show in dropdown 
              
              if (item.price_type == 1) {
                if (item.price_amount > 0) {
                  calculatedPrice = price + ((price * item.price_amount) / 100);
                } else {
                  calculatedPrice = price - ((price * Math.abs(item.price_amount)) / 100);
                }
              } else {
                calculatedPrice = price + item.price_amount;
              } 
              return calculatedPrice
          },
          bookingOptionIncrDecr: function(index, i, sign /** plus | minus */){
            let { product_field_value } = customSelectFieldList.value[index]

            product_field_value.forEach((item) => {
              if('quantity' in item === false){
                item['quantity'] = 0
              }
            })

            let actualQty = this.actualAvailableQty
            if( !actualQty ) return

             
            if(sign === 'plus'){
              if(i == 0){ // Adult
                if(product_field_value[i]['quantity'] < actualQty){
                  product_field_value[i]['quantity'] += 1 
                } 
              }
              let parentQty = product_field_value[0]['quantity'];
  
              if(i > 0){ // childs
                if(product_field_value[i]['quantity'] < actualQty && product_field_value[i]['quantity'] < parentQty){
                  product_field_value[i]['quantity'] += 1 
                } 
              } 
            } 
            else if(sign === 'minus'){
              if(product_field_value[i]['quantity'] > 0){
                product_field_value[i]['quantity'] -= 1 
              }
            } 

            /**
             * Adjust child quantity if child is grea
             */
            let parentQty = product_field_value[0]['quantity']
            this.actualAvailableQtyForBooking = parentQty
            
            product_field_value.forEach((item, iIndex) => {
              item['price'] = this.bookingOptionFieldPrice(item)
              if(iIndex > 0 && parentQty){
                if(item.quantity > parentQty){
                  item.quantity = 0
                }
              }
            })
            
            if(parentQty){
              selectedCustomFields.value = product_field_value 
            } else {
              selectedCustomFields.value = [] 
            }
          }, 
        },
        computed: {
          getComputedBookingProductOptionsPrice: function(){
            if(customSelectFieldList.value.value || product.booking === false) return 0 
            let totalPrice = 0
            customSelectFieldList.value.forEach(item => {
              (item.product_field_value || []).forEach(item => {
                let quantity = item?.quantity || 0
                let price = this.bookingOptionFieldPrice(item)
                totalPrice += (quantity * price)
              })
            })
            return totalPrice
          }, 
        },
        async mounted(){   
          
          emitter.on('changed:reantalDate:from:inPageCartWidget', (data) => {
            if(!isDisableDatePicker.value){
              selectstartDateTime((data.startDate || '') + (data?.startTime ? ` ${data.startTime}` : ''));        
              selectendDateTime((data.endDate || '') + (data?.endTime ?  ` ${data?.endTime}` : '')); 
              checkPrice(); 
            }
          });
           
        },
        
      })
    })
)


const rentmy_template = RentMyProductPricing;

const datepickers = ref(null);
setInterval(() => {
  datepickers.value = wrapper.querySelectorAll('[DateTimePicker]');
}, 500);

let pickerTheme = RENTMY_GLOBAL?.emDateTimePicker?.theme || 'light';
let pickerColors = RENTMY_GLOBAL?.emDateTimePicker?.colors || {};

let detailsPage_startDatePicker_ajdustX = RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_startDatePicker_ajdustX || 0;
let detailsPage_startDatePicker_ajdustY = RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_startDatePicker_ajdustY || 0;

let detailsPage_endDatePicker_ajdustX = RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_endDatePicker_ajdustX || 0;
let detailsPage_endDatePicker_ajdustY = RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_endDatePicker_ajdustY || 0;

let detailsPage_startDatePicker_displayIn = RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_startDatePicker_displayIn || 'modal';
let detailsPage_endDatePicker_displayIn = RENTMY_GLOBAL?.emDateTimePicker?.detailsPage_endDatePicker_displayIn || 'modal';

let timePickerUi = RENTMY_GLOBAL?.emDateTimePicker?.timePickerUi || 'standard';
let timePickerButtons = RENTMY_GLOBAL?.emDateTimePicker?.timePickerButtons ?? true;


</script>
<template>
  <template v-if="loaded && rentmy_template">
    <teleport :to="rentmy_template">
      <component :is="rentmyComponent">
      </component>
    </teleport>
  </template>

  <template v-if="datepickers && datepickers.length && rentmyComponent">
    <template v-for="(datepicker, i) in datepickers">
      <template v-if="datepicker.querySelector('[StartDatePicker]')">
        <teleport :to="datepicker.querySelector('[StartDatePicker]')" :key="i">
          <!-- <p class="bg-black">Testing</p> -->
          <EmDateTimePicker ref="startDatePicker"
              :est_valid_dates="product?.est_valid_dates"
              @change="async (data) => { 
                selectstartDateTime(data.startDateTime);

                if(startDate_useRangePicker){
                  selectendDateTime((data.endDateTime)); 
                } 
                if(isEnableExactTime){ 
                  globalLoader.show()
                  await productDetailsStore.getExactTimes(product.id, data.startDate);
                  if(productDetailsStore.exactTimes.times?.length){
                    let duration = productDetailsStore.exactTimes.times[0]
                    await getChangTime(duration.time, duration)
                    duration['isActive'] = true,
                    getPriceValue(cart);
                    isSelectedExactTime = true; 
                  }
                  globalLoader.hide()
                }
                checkPrice();
              }"
              @nextPrev="getAvailableList"
              :availableList="availableList"
              :startDate="rentStartDate" 
              :rangePicker="startDate_useRangePicker"
              :timePicker="(showStartTime() && !isSeparateDate && product.booking === false)" 
              :minDate="min_date"
              :isDisabled="isDisableDatePicker || isNonrecurring"
              :invisible="true"
              :timePickerButtons="timePickerButtons"
              :use24FormatTimeForEvents="true"
              :theme="pickerTheme"
              :colors="pickerColors"
              :timePickerUi="timePickerUi"
              :adjustX="detailsPage_startDatePicker_ajdustX"
              :adjustY="detailsPage_startDatePicker_ajdustY"
              :displayIn="detailsPage_startDatePicker_displayIn"
              >
          </EmDateTimePicker>
        </teleport>
      </template>

      <template v-if="datepicker.querySelector('[EndDatePicker]')">
        <teleport :to="datepicker.querySelector('[EndDatePicker]')" :key="i">
          <EmDateTimePicker ref="endDatePicker"
              @change="(data) => { 

                if(endDate_useRangePicker){ 
                  selectstartDateTime(data.startDateTime);
                }
                selectendDateTime(data.endDateTime); 
                checkPrice();
                //getDateFromManualChange(data, startDateFlag, endDate_useRangePicker);
              }"
              @nextPrev="getAvailableList"
              :availableList="availableList"
              :startDate="rentStartDate" 
              :endDate="rentEndDate" 
              :rangePicker="endDate_useRangePicker"
              :timePicker="(showEndTime() && !isSeparateDate)" 
              :minDate="min_date"
              :isDisabled="isDisableDatePicker || isNonrecurring"
              :invisible="true"
              :timePickerButtons="timePickerButtons"
              :use24FormatTimeForEvents="true"
              :theme="pickerTheme"
              :timePickerUi="timePickerUi"
              :colors="pickerColors"
              :adjustX="detailsPage_endDatePicker_ajdustX"
              :adjustY="detailsPage_endDatePicker_ajdustY"
              :displayIn="detailsPage_endDatePicker_displayIn"
              >
          </EmDateTimePicker>
          
        </teleport>
      </template>

      <template v-if="datepicker.querySelector('[RentalDateRangePicker]')">
        <teleport :to="datepicker.querySelector('[RentalDateRangePicker]')" :key="i">
          <TimePicker :id="'time_picker'" 
          :timePickerUi="timePickerUi" 
          :timePickerButtons="timePickerButtons"
          :date="isAutoSelectEarliestDate ? autoSelectStartDate : rentStartDate"
            @date-model="($event, startDateFlag) => { getTimeFromManualChange($event, startDateFlag) }">
          </TimePicker>
        </teleport>
      </template>
    </template>
  </template>
  <LocationNoticeModal v-model="isShowModel">
    {{ model_msg }}
  </LocationNoticeModal>
</template>

<style>
#start_date_picker,
#start_time_picker,
#end_date_picker,
#time_picker {
  display: none;
}
</style>