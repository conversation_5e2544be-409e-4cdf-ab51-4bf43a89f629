<script setup>
import { ref, inject, onMounted, defineProps, provide, computed } from 'vue';
import { createComponentHTML } from "@utils/functions/withComponent";
let { helper, cookie, domElement, http, labelSelector, currency } = inject('utils');
let { wrapper, relatedProducts } = defineProps(['wrapper','relatedProducts']);
let globalLoader = inject('globalLoader');
let setWraperIsReady = inject('setWraperIsReady');
import { Toaster, emitter } from '@/import-hub';

import { useProductListStore } from '../../product-list/controllers/productList.controller';
import { useCartStore } from '../../cart/controllers/cart.controller';
const productListStore = useProductListStore();
const cartStore = useCartStore();

const log = console.log

import { useWishlistStore } from "@stores/wishlist";
const wishlistStore = useWishlistStore()


const site_specific = JSON.parse(localStorage.getItem('rentmy_contents'))?.site_specific;
const is_active_wish_list = site_specific?.confg?.inventory?.wishlist?.active

createComponentHTML(wrapper, [
    {
        selector: "[RentMyRelatedProducts]",
        attr: { 'v-if': 'relatedProducts?.length' },
    },
    {
        selector: "[RentMyRelatedProducts] [RentMyProductItem]",
        template: true,
        attr: { 'v-for': '(product, index) in relatedProducts', ':key': 'index' },
        child: [
            {
                selector: "a[RentMyProductImageUrl]",
                attr: { ':href': 'getProductDetailsURL(product)' },
            },
            {
                selector: "img[RentMyProductImage]",
                attr: { ':src': 'helper.getProductImage(product)', '@contextmenu': 'log(product)' },
            },
            {
                selector: "[RentMyProductName]",
                attr: { ':src': 'helper.getProductImage(cartData, product)' },
                text: `<a :href="getProductDetailsURL(product)">{{ product?.name }}</a>`,
            },
            {
                selector: "[RentMyProductPrice]",
                attr: {
                    ':class': `is_active_wish_list ? 'mb-0' : ''`
                },
                text: `{{ getProductPrice(product) }}`,
            },
            {
                selector: '[DetailsPageUrl]',
                attr: {
                    '@click.stop.prevent': 'gotToDetailsPage(product)',
                }
            },
            
            {
                selector: "[WishListBtnArea]",
                attr: { 'v-if': 'is_active_wish_list' },
                child: [
                    {
                        selector: '[RentMyAddToWishListBtn]',
                        attr: {
                            '@click.stop': 'addToWishList(product)'
                        }
                    },
                ]
            },
            {
                selector: "[ProductButtons]",
                attr: { 'v-if': '!is_active_wish_list' },
                child: [
                    {
                        selector: "[RentMyViewDetailsBtn]",
                        attr: { ':href': 'getProductDetailsURL(product)' },
                    },
                    {
                        selector: "[RentMyAddToCartBtn]",
                        attr: { 
                            'v-if': 'hasBuyPrice(product)', 
                            '@click.stop': 'createCart(product)' 
                        },
                    },
                ]
            },
        ]
    },
    // {
    //     selector: "[RentMyProductItem] a[RentMyProductImageUrl]",
    //     attr: { ':href': 'getProductDetailsURL(product)' },
    // },
    // {
    //     selector: "[RentMyProductItem] img[RentMyProductImage]",
    //     attr: { 
    //         ':src': 'getProductImage(product)'
    //      },
    // },
    // {
    //     selector: "[RentMyProductItem] [RentMyProductName]",
    //     attr: { ':src': 'getProductImage(product)' },
    //     text: `<a :href="getProductDetailsURL(product)">{{ product?.name }}</a>`,
    // },
    // {
    //     selector: "[RentMyProductItem] [RentMyProductPrice]",
    //     text: `{{ getProductPrice(product) }}`,
    // },
    // {
    //     selector: "[RentMyProductItem] [RentMyViewDetailsBtn]",
    //     attr: { ':href': 'getProductDetailsURL(product)' },
    // },
    // {
    //     selector: "[RentMyProductItem] [RentMyAddToCartBtn]",
    //     attr: { 'v-if': 'hasBuyPrice(product)', '@click.stop': 'createCart(product)' },
    // }
    
]);

let template = wrapper.innerHTML
wrapper.innerHTML = '';

let relatedProductsComponent = {
    template,
    data() {
        return {
            log,
            helper,
            currency,
            relatedProducts,
            is_active_wish_list,
        }
    },
    async mounted() {

    },
    computed: {

    },
    methods: {
        getProductImage: function(item){
            let { product } = item;
            if(!product) product = item;
            let image = product?.images?.[0];
            image = image?.image_small || image?.image_small_free || image?.image_large || image?.image_large_free;
            if(image){
                image = this.helper.generateImage(image, product?.id);
            } else {
                image = RENTMY_GLOBAL?.images?.default_product_image
            }
            return image;
        },
        getProductDetailsURL: function(product){   
            let detailsPageUrl = product?.type == 2 ? window.RENTMY_GLOBAL.page?.package_details : window.RENTMY_GLOBAL.page?.product_details;
            return helper.withURL.generateURL(detailsPageUrl, product)
        },
        getProductPrice: function(product){   
            let { currencyConfig } = cartStore;
            let priceLabel = currency.formatListPrice(product.prices?.[0], currencyConfig);
            return priceLabel?.price || '';
        },
        hasBuyPrice: function(product){   
            let { prices } = product;
            let _prices = helper.formatBuyRent(prices)
            let isBuyType = false
            if (_prices && Object.keys(_prices).length > 0) {
                if(_prices?.buy?.type) isBuyType = true;
            }   
            return !!isBuyType;
        },
        createCart: function(product){        
            globalLoader.show()
            productListStore.createCart(product).then(response => {
                globalLoader.hide()
                this.disabled = true;
                if (response.status == 'OK') {
                    if(response.result.error){
                        Toaster().error(response.result.error);
                        return;
                    }
                    Toaster().success('Item added to cart')
                    localStorage.setItem('user_cart', JSON.stringify(response.result.data));
                    let token = response.result.data?.token;
                    if(RENTMY_GLOBAL.ajax_url){
                        http.post(RENTMY_GLOBAL.ajax_url, {
                            token,
                            action:'rentmy_cdn_request',
                            method:'rentmy_cart_token',
                        })
                    }
                } else {
                    if(response.result.error){
                        Toaster().error(response.result.error);
                    }
                }
            })
        },
        addToWishList: async function(product) {
            globalLoader.show()
            await wishlistStore.addToList({product})
            globalLoader.hide()
        },
        gotToDetailsPage: async function(product) {
            let fullUrl = this.getProductDetailsURL(product)
            if(RENTMY_GLOBAL.using_in_cli_project){
                
            } else {
                window.open(fullUrl, '_self')
            }
        },
    },    
}

</script>

<template>
    <teleport :to="wrapper">
        <relatedProductsComponent></relatedProductsComponent>
    </teleport>
</template>