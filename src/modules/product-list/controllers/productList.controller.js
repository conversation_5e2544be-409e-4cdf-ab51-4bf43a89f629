import { defineStore } from 'pinia'
import { reactive, computed, ref, onMounted } from 'vue'
import productListService from '../services/productList.service'
import { useGlobalStore } from '@stores/global';
import { changeNullToEmpty } from '@utils/functions'
import cookie from '@utils/cookie';

export const useProductListStore = defineStore('productList', () => {

  const state = reactive({
    products: [],
    paginateData: null,
    currencyConfig: null,
    loopStartIndex: -1,
    lastProductivePage: 1,
    childCategories: null,
  })

  const globalStore = useGlobalStore();

  let products = computed(() => state.products);
  let paginateData = computed(() => state.paginateData);
  let loopStartIndex = computed(() => state.loopStartIndex);
  let childCategories = computed(() => state.childCategories);
  let lastProductivePage = computed(() => state.lastProductivePage);
  let currencyConfig = computed(() => globalStore.currency_config );
  let categories = computed(() => globalStore.categories );
  let tags = computed(() => globalStore.tags );

  function handleProductListResponse(response, usingPagination = false, payload={}){
    let fulllData = response.result;

    if(payload.search){
      state.loopStartIndex = -1;
      state.products = response.result.data;
      if(state.paginateData){
        state.paginateData.page = 1;
      }
      return;
    }

    fulllData.pages = Math.ceil(fulllData?.total / fulllData?.limit);
    fulllData.isLastpage = fulllData.pages == fulllData.page;

    state.paginateData = fulllData;

    if(response.result?.data?.length){
      let limit = payload?.limit;
      let total = response?.result?.total || 1;
      let totalPage = Math.ceil(total / limit);   
      state.lastProductivePage = totalPage || 1;     
    }

    if (usingPagination) {
      state.products = response.result?.data;
    } else {
      state.loopStartIndex = (state.products?.length - 1);
      state.products = [...state.products, ...response.result?.data];
    }
  }

  const clearProducts = () => {
    state.products = [];
    state.paginateData = null;
  }

  const getProductList = async (payload = null, usingPagination = false) => {
    let response = await productListService.getProductList(payload);
    handleProductListResponse(response, usingPagination, payload);
  }

  const getProductListByCategory = async (uuid, payload = null, usingPagination = false) => {
    let response = await productListService.getProductListByCategory(uuid, payload);
    handleProductListResponse(response, usingPagination, payload);
  }

  const getChildCategories = async (uuid) => {
    let response = await productListService.getChildCategories(uuid);
    state.childCategories = response?.result?.data;
    return state.childCategories;
  };

  async function init() {
    await globalStore.getCategories();
    await globalStore.getTags();
  }


  async function createCart(item) {
    const cart = {
      driving_license_required: false,
      deposit_amount: 0,
      deposite_tax: item.deposite_tax,
      sales_tax: item.sales_tax,
      product_id: Number(item.id),
      quantity: 1,
      variants_products_id: item.default_variant.variants_products_id,
      location: window.RENTMY_GLOBAL.locationId,
      price: item.prices[0].base.price,
      price_id: item.prices[0].base.id,
      rental_type: "buy",
      rental_duration: null,
      rent_start: null,
      rent_end: null,
      term: null,
      token: localStorage.getItem('user_cart') ? JSON.parse(localStorage.getItem('user_cart'))?.token : null 
    };    
    let response = await productListService.saveCart(changeNullToEmpty(cart));    
    return response;
  }


  return {
    init,
    products,
    paginateData,
    loopStartIndex,
    lastProductivePage,
    categories,
    childCategories,
    tags,

    getProductList,
    clearProducts,
    currencyConfig,
    getChildCategories,
    getProductListByCategory,
    createCart,
  }
})