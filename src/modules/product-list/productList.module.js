import listOfProducts from './views/listOfProducts.vue';
import { createComponentHTML } from "@utils/functions/withComponent";

export const GenerateCompoenentHtml = function(wrapper, allowDyanmicContent, printSelector){
    const site_specific = JSON.parse( localStorage.getItem("rentmy_contents") )?.site_specific;
    
    createComponentHTML(wrapper, [ 
        {
            selector: "[RentMyFilterArea] .RentMyFilterTitle",
            text: ` ${allowDyanmicContent ? site_specific?.others?.product_list_category_label: '__EXISTING_HTML__'}`
        },
        {
            selector: "[RentMyFilterArea] .RentMyFilterList .RentMyFilterTitle",
            text: ` ${allowDyanmicContent ? site_specific?.others?.product_list_filter_label: '__EXISTING_HTML__'}`
        },
        {
            selector: "[RentMyFilterArea] .RentMyPriceArea .RentMyFilterSubTitle",
            text: ` ${allowDyanmicContent ? site_specific?.others?.product_list_price_filter: '__EXISTING_HTML__'}`
        },
        {
            selector: "[RentMyFilterArea] [RentMyFilterByPrice] [RentMyFilterByPriceMinLabel]",
            text: ` ${allowDyanmicContent ? site_specific?.product_details?.lbl_min: '__EXISTING_HTML__'}`
        },
        {
            selector: "[RentMyFilterArea] [RentMyFilterByPrice] [RentMyFilterByPriceMaxLabel]",
            text: ` ${allowDyanmicContent ? site_specific?.product_details?.lbl_max: '__EXISTING_HTML__'}`
        },
        {
            selector: "[RentMyFilterArea] [RentMyFilterByPrice] [RentMyMinMaxSubmitBtn]",
            text: ` ${allowDyanmicContent ? site_specific?.product_details?.btn_submit: '__EXISTING_HTML__'}`
        },
        {
            selector: "[RentMyFilterArea] [RentMyFilterByPrice] [RentMyMinMaxClearBtn]",
            text: ` ${allowDyanmicContent ? site_specific?.product_details?.btn_clear: '__EXISTING_HTML__'}`
        },
        {
            selector: "[RentMyFilterArea] .RentMyTypeArea .RentMyFilterSubTitle",
            text: ` ${allowDyanmicContent ? site_specific?.others?.product_list_type_fileter: '__EXISTING_HTML__'}`
        },
        ...printSelector({
            selector: "[RentMyFilterArea] [RentMyFilterByRentalType] [RentMyFilterByLabelRent]",
            target: { prev_tag: 'input', next_tag: 'span' },
            text: ` ${allowDyanmicContent ? site_specific?.product_details?.rent: '__EXISTING_HTML__'}`
        }),
        ...printSelector({
            selector: "[RentMyFilterArea] [RentMyFilterByRentalType] [RentMyFilterByLabelBuy]",
            target: { prev_tag: 'input', next_tag: 'span' },
            text: ` ${allowDyanmicContent ? site_specific?.product_details?.buy: '__EXISTING_HTML__'}`
        }),
        ...printSelector({
            selector: "[RentMyFilterArea] [RentMyFilterByRentalType] [RentMyFilterByLabelAll]",
            target: { prev_tag: 'input', next_tag: 'span' },
            text: ` ${allowDyanmicContent ? site_specific?.product_details?.all: '__EXISTING_HTML__'}`
        }),
        {
            selector: ".RentMyProductArea .SortProduct label",
            text: ` ${allowDyanmicContent ? site_specific?.product_details?.lbl_sort_by: '__EXISTING_HTML__'}`
        }
    ]);
}

export default {
    listOfProducts,
}