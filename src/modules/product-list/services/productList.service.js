import http from  '@utils/http'

export default {
  async getProductList(payload) {
    let _payload = JSON.parse(JSON.stringify(payload));
    delete _payload.category;
    let response;
    if(_payload.search){
      response = await http.post('/search/products', _payload)
      // response = await http.get(`/products/search-product?search=${_payload.search}`);
    } else {
      response = await http.post('/products/online', _payload)
    }
    return response.data
  },
  async getProductListByCategory(categoryUUID, payload) {  
    let _payload = JSON.parse(JSON.stringify(payload));
    delete _payload.category;
    if(/^\d+$/.test(categoryUUID)){
      _payload.category_id = categoryUUID;
    } 
    const response = await http.post(`/category/products/${categoryUUID}`, _payload)
    return response.data
  },
  async getChildCategories(uuid) {
    const result = await http.get(`/get/child-categories/${uuid}`);
    return result?.data;   
  },
  async saveCart(data) {
    let offset = moment.parseZone(Date.now()).utcOffset() / 60;
    const timestamp = new Date().getTimezoneOffset();
    const timezone_offset_minutes = (timestamp === 0) ? 0 : -timestamp;
    data.zone = timezone_offset_minutes;
    const result = await http.post("carts/add-to-cart", data);
    return result?.data;  
  }
  
}