<script setup>
import debounce from 'lodash/debounce'
import { ref, inject, onMounted, defineEmits, h } from 'vue';
let { helper, domElement, currency } = inject('utils');

let wrapper = inject('wrapper');
let RentMyParams = inject('RentMyParams');
let productListStore = inject('productListStore');
let passedCategoryId = inject('passedCategoryId');

import { emitter } from '@/import-hub';

let FilterArea = inject('FilterArea');
let ShortByDropdown = wrapper.querySelector('[RentMyShortByDropdown]');

let myEmit = defineEmits(['changed-sort-dropdown', 'trigger-category', 'trigger-tag', 'trigger-price', 'trigger-rental-type']);
let category_li = null;

function toggleFilters(){
    try {
        
        if(window.innerWidth > 500) return; // only for mobile
        
        let catArea = document.querySelector('.RentMyCategory');
        let catMenuList = catArea.querySelector('.CategoryMenuList');
        if(catArea && catMenuList){

            catMenuList.hidden = true;
            catArea.style.marginBottom = '15px';
            catArea.style.cursor = 'pointer';

            catArea.addEventListener('click', event => {
                let isHidden = (!!catMenuList.hidden);
                if(!isHidden){
                    catArea.style.marginBottom = '15px';
                } else {
                    catArea.style.marginBottom = '0px';
                }
                catMenuList.hidden = !isHidden;
            })
        }
    
        let filterArea = document.querySelector('.RentMyFilterList') || {};
        let filterItemList = document.querySelector('.FilterCheckbox') || {};
        let RentMyPriceArea = document.querySelector('.RentMyPriceArea') || {};
        let RentMyTypeArea = document.querySelector('.RentMyTypeArea') || {};

        if(filterArea){

            filterItemList.hidden = true;
            RentMyPriceArea.hidden = true;
            RentMyTypeArea.hidden = true;
            filterArea.style.cursor = 'pointer';

            filterArea.addEventListener('click', event => {
                let isHidden = (!!filterItemList.hidden);
                filterItemList.hidden = !isHidden;
                RentMyPriceArea.hidden = !isHidden;
                RentMyTypeArea.hidden = !isHidden;
            })
        }
        
    } catch (error) {
        console.warn('toggleFilters_error', error);
    }
}


function get_product_list_by_category_url(fullUrl, category){
    let { products_list_by_category } = RENTMY_GLOBAL.page
    if(products_list_by_category){
        if(/\{\w{1,}\}/.test(products_list_by_category || '')){
            fullUrl = helper.withURL.generateURL(products_list_by_category, category)
            if(/\?/g.test(products_list_by_category) === false){ 
                fullUrl = fullUrl.split('?')[0]
            }
        }
    }
    return fullUrl
}

function set_RentMyBreadcrumbTitle(category, subcategory=null, page_url='#'){
    
    let head = document.querySelector('head')
    if(head){
        let title = head.querySelector('title')
        if(title){
            let text = title.innerHTML
            let splitter = ' :: '
            if(text.indexOf(' | ') > -1) splitter = ' | '
            if(text.indexOf(' || ') > -1) splitter = ' || '
            let parts = text.split(splitter)
            parts[1] = subcategory?.name || category.name
            title.innerHTML = parts.join(splitter)
        }
    } 


    let breadcrumb_for_parent_cat = document.querySelector('[RentMyBreadcrumbTitle="categoryProduct"]')
    let breadcrumb_for_child_cat = document.querySelector('[RentMyBreadcrumbTitle="subcategoryProduct"]')

    if(breadcrumb_for_parent_cat) {
        breadcrumb_for_parent_cat.innerHTML = category.name
    }
    
    if(subcategory){
        if(breadcrumb_for_parent_cat){
            breadcrumb_for_parent_cat.innerHTML = `<a href="${page_url}">${category.name}</a>`
        }
        if(breadcrumb_for_child_cat) breadcrumb_for_child_cat.innerHTML = subcategory.name 
    }else{
        if(breadcrumb_for_child_cat){
            breadcrumb_for_child_cat.innerHTML = '' 
        }
    }
}


function print_category_menu(){
    const CategoryArea = FilterArea.querySelector('[RentMyFilterByCategory]');
    if(CategoryArea){
        let category_list = productListStore.categories;            
        category_li = category_li || CategoryArea.children?.[0];
        const RemoveClassFromAll = function(selector='li', className='Selected'){
            let elements = Array.from(CategoryArea.querySelectorAll(selector) || []);
            elements.forEach(el => {
                if(className)
                    el.classList.remove(className)
            })
        }
        CategoryArea.innerHTML = '';

        const option = domElement.parseOptions(CategoryArea);

        /* -------------------------------------------------------------------------- */
        /*                               With default UI                              */
        /* -------------------------------------------------------------------------- */
        category_list?.forEach(category => {
            if(category_li){
                let main_category_item = category_li.cloneNode(true);
                main_category_item.setAttribute('parent-cat', 'true')
                main_category_item.setAttribute('rentmy-cat-uuid', category?.uuid)
                main_category_item.setAttribute('rentmy-cat-name', category?.name)
                main_category_item.setAttribute('rentmy-cat-id', category?.id)
                if(!option.radio){
                    let a = main_category_item?.querySelector('a');
                    let uuid = category?.uuid;
                    let parent_uuid = category?.uuid;
                    let href = helper.withURL.setQuery({category: uuid}, null, true); 
                    
                    href = get_product_list_by_category_url(href, category)

                    if(a){
                        a.href = href;
                        let span = a.querySelector('span');
                        if(span) span.textContent = category.name;
                        else a.innerHTML = category.name + ` <i class="fa fa-angle-right"></i>`;
                        if(!category.children?.length) main_category_item.querySelector('i')?.remove();
                    }
                    main_category_item.addEventListener('click', (event) => {
                        event.preventDefault(); 
                        let uid_is_exist_in_url = helper.withURL.hasCategoryUIDInUrl(uuid)
                        
                        let { products_list_by_category } = RENTMY_GLOBAL.page

                    
                        if(products_list_by_category){ 
                            passedCategoryId.value = uuid
                            let new_url = get_product_list_by_category_url(window.location.href, category) 
                            helper.withURL.setQuery({}, new_url);  
                            set_RentMyBreadcrumbTitle(category, null, new_url)
                        } else {
                            let urlWithQuery = helper.withURL.setQuery({category: uuid}, window.location.href, true);
                            set_RentMyBreadcrumbTitle(category, null, urlWithQuery)
                            helper.withURL.setQuery({}, urlWithQuery)
                        }
                        RemoveClassFromAll();             
                        main_category_item.classList.add('Selected');
                    
                        if(!uid_is_exist_in_url) myEmit('trigger-category', !uid_is_exist_in_url ? uuid : null);
                        
                        (Array.from(main_category_item.querySelectorAll('.rentmy-child-cat')))?.forEach(child_cat => { 
                            child_cat.hidden = !child_cat.hidden;
                            child_cat.setAttribute('showing', String(!child_cat.hidden))
                        });  
                        
                          
                    })
                    main_category_item?.querySelector('a')?.addEventListener('click', (event) => event.preventDefault());                    
                    /* -------------------------------------------------------------------------- */
                    /*                             with Subcategories                             */
                    /* -------------------------------------------------------------------------- */  
                    if(category.children?.length){
                        category.children?.forEach(subCategory => {
                            let sub_category_item = category_li.cloneNode(true);
                            sub_category_item.hidden = true;
                            sub_category_item.classList.add('rentmy-child-cat');
                            sub_category_item.setAttribute('parent-cat', category.uuid);
                            sub_category_item.setAttribute('parent-cat-uid', category.uuid);
                            sub_category_item.setAttribute('parent-cat-name', category.name);

                            sub_category_item.setAttribute('rentmy-cat-id', subCategory?.id)
                            sub_category_item.setAttribute('rentmy-cat-uuid', subCategory?.uuid)
                            sub_category_item.setAttribute('rentmy-cat-name', subCategory?.name)
                            sub_category_item.setAttribute('is-child-cat', 'true')

                        

                            sub_category_item.setAttribute('style', "padding:0px 0px 0px 10px !important");
                            let a = sub_category_item?.querySelector('a');
                            let uuid = subCategory?.uuid;
                            let href = helper.withURL.setQuery({category: uuid}, null, true);
                            href = get_product_list_by_category_url(href, subCategory)
                            if(a){
                                a.href = href;
                                let span = a.querySelector('span');
                                if(span) span.textContent = subCategory.name;
                                else a.innerHTML = subCategory.name;
                                sub_category_item.querySelector('i')?.remove();
                            } 
                            sub_category_item.addEventListener('click', (event) => {
                                event.preventDefault(); 
                                event.stopPropagation();
                                let { products_list_by_category } = RENTMY_GLOBAL.page

                                let uid_is_exist_in_url = helper.withURL.hasCategoryUIDInUrl(uuid) 

                                if(products_list_by_category){
                                    RemoveClassFromAll();
                                    sub_category_item.classList.add('Selected');
                                    passedCategoryId.value = uuid
                                    let new_url = get_product_list_by_category_url(window.location.href, subCategory)
                                    helper.withURL.setQuery({}, new_url);  
                                    let parent_cat_page_url = get_product_list_by_category_url(window.location.href, category)
                                    set_RentMyBreadcrumbTitle(category, subCategory, parent_cat_page_url)
                                    

                                    let parent_cat_url = get_product_list_by_category_url(window.location.href, category)
                                    sub_category_item.setAttribute('parent-cat-page-url', parent_cat_url)

                                }else{
                                    RemoveClassFromAll();
                                    sub_category_item.classList.add('Selected');
                                    passedCategoryId.value = null 
                                    let urlWithQuery = helper.withURL.setQuery({category: uuid}, window.location.href, true);
                                    set_RentMyBreadcrumbTitle(category, subCategory, urlWithQuery)
                                    helper.withURL.setQuery({}, urlWithQuery);

                                    let parent_cat_url = helper.withURL.setQuery({category: parent_uuid}, window.location.href, true);
                                    sub_category_item.setAttribute('parent-cat-page-url', parent_cat_url)
                                } 
                                if(!uid_is_exist_in_url) myEmit('trigger-category', uuid); 
                            })
                            sub_category_item?.querySelector('a')?.addEventListener('click', (event) => event.preventDefault());
                            main_category_item.appendChild(sub_category_item);
                        });
    
                    };
                    
                } 
                else if(option.radio) {                    
                    let radioInput = main_category_item?.querySelector('input[type=radio]');
                    let categoryNameEl = main_category_item?.querySelector('[categoryName]');
                    let uuid = category?.uuid;
                    let href = helper.withURL.setQuery({category: uuid}, null, true);

                    const uncheckAllRadioInputs = () => {
                        CategoryArea.querySelectorAll('input[type=radio]')?.forEach(function(radio){
                                radio.checked = false;
                        })
                    }

                    if(radioInput && categoryNameEl){     
                        
                        categoryNameEl.innerHTML = category.name;

                        main_category_item.addEventListener('click', (event) => {
                            event.preventDefault(); 
                            let uid_is_exist_in_url = helper.withURL.hasCategoryUIDInUrl(uuid)
                            if(uid_is_exist_in_url){
                                helper.withURL.deleteQuery('category'); 
                                uncheckAllRadioInputs(); 
                            }else{
                                helper.withURL.setQuery({category: uuid});
                                uncheckAllRadioInputs();  
                                radioInput.checked = true;  
                            }                            
                            myEmit('trigger-category', !uid_is_exist_in_url ? uuid : null);                         
                        });
                    }                    
                }
                CategoryArea.appendChild(main_category_item);
            };
        });
    }
}

onMounted(async ()=>{
    if(!FilterArea && !ShortByDropdown) return;

    toggleFilters();
    
    if(FilterArea){
        /* -------------------------------------------------------------------------- */
        /*                             With Category Menu                             */
        /* -------------------------------------------------------------------------- */
        print_category_menu();

        /* -------------------------------------------------------------------------- */
        /*                             With Tag Filtering                             */
        /* -------------------------------------------------------------------------- */
        const TagArea = FilterArea.querySelector('[RentMyFilterByTag]');
        if(TagArea){
            let tag_list = productListStore.tags;
            let tag_label = TagArea.children?.[0];
            TagArea.innerHTML = '';
            tag_list?.forEach(tag => {
                let cloned_tag = tag_label.cloneNode(true);
                cloned_tag.setAttribute('for', `tag${tag.id}`)
                let checkbox = cloned_tag.querySelector('input');
                checkbox.setAttribute('id', `tag${tag.id}`)

                let a = cloned_tag?.querySelector('a');
                if(a){
                    a.textContent = tag.name;
                    a.removeAttribute('href');
                }
               
                cloned_tag.addEventListener('click', (event) => {
                    event.stopPropagation();
                    event.preventDefault();
                    
                    RentMyParams.tag_id = (RentMyParams?.tag_id || '')?.split(',')?.filter(t => t);
                    let tag_index = RentMyParams.tag_id.findIndex(i => i == tag.id)
                     
                    if(tag_index > -1){
                        RentMyParams.tag_id.splice(tag_index, 1);
                        checkbox.checked = false;
                    }else{
                        RentMyParams.tag_id.push(tag.id);
                        checkbox.checked = true;
                    }
                    let uniqueTags = Array.from(new Set(RentMyParams.tag_id));
                    RentMyParams.tag_id = uniqueTags?.join(',');
                    if(!RentMyParams.tag_id) delete RentMyParams.tag_id;
                    myEmit('trigger-tag', RentMyParams);
                })
                TagArea.appendChild(cloned_tag);
            })
        }

        /* -------------------------------------------------------------------------- */
        /*                   with Price Filtering (for manual input)                  */
        /* -------------------------------------------------------------------------- */ 

        let priceFilterSection = FilterArea.querySelector('[RentMyFilterByPrice]');
        if(priceFilterSection){
            let RentMyMinPrice = priceFilterSection.querySelector('[RentMyMinPrice]');
            let RentMyMaxPrice = priceFilterSection.querySelector('[RentMyMaxPrice]');
            let RentMyMinMaxSubmitBtn = priceFilterSection.querySelector('[RentMyMinMaxSubmitBtn]');
            let RentMyMinMaxClearBtn = priceFilterSection.querySelector('[RentMyMinMaxClearBtn]');
            const emitPrice = (event) => {
                event.preventDefault();
                let min = RentMyParams.price_min = Number(RentMyMinPrice?.value);
                let max = RentMyParams.price_max = Number(RentMyMaxPrice?.value); 

                if(!min) delete RentMyParams.price_min; 
                if(!max) delete RentMyParams.price_max;

                myEmit('trigger-price', RentMyParams);                
            }

            const debounceEmitPrice = debounce(emitPrice, 500);

            const keepOnlyDigists = (event) => {
                if(event.target.value){
                    event.target.value = event.target.value.replace(/\D/g, '');
                }
            }

            RentMyMinPrice.addEventListener('keyup', keepOnlyDigists);
            RentMyMaxPrice.addEventListener('keyup', keepOnlyDigists);
            RentMyMinPrice.addEventListener('keyup', (event)=>{
                if(event.code === 'Enter'){
                    emitPrice(event);
                }
            });
            RentMyMaxPrice.addEventListener('keyup', (event)=>{
                if(event.code === 'Enter'){
                    emitPrice(event);
                }
            });
            RentMyMinPrice.addEventListener('paste', keepOnlyDigists);
            RentMyMaxPrice.addEventListener('paste', keepOnlyDigists);
      
            if(!RentMyMinMaxSubmitBtn){
                if(RentMyMinPrice) RentMyMinPrice.addEventListener('input', debounceEmitPrice);
                if(RentMyMaxPrice) RentMyMaxPrice.addEventListener('input', debounceEmitPrice);
            }            
            if(RentMyMinMaxSubmitBtn) RentMyMinMaxSubmitBtn.addEventListener('click', emitPrice);
            if(RentMyMinMaxClearBtn) RentMyMinMaxClearBtn.addEventListener('click', () => {
                RentMyMinPrice.value = '';
                RentMyMaxPrice.value = '';
                delete RentMyParams.price_min;
                delete RentMyParams.price_max;
                myEmit('trigger-price', RentMyParams);
                // uncheck statick price range list
                let StaticPiceRangeList = FilterArea.querySelector('[RentMyFilterByStaticPriceRange]');
                if(StaticPiceRangeList){
                    let inputs = Array.from(StaticPiceRangeList.querySelectorAll('input[type=radio]'));
                    inputs?.forEach(el => el.checked = false);
                }
            });
        }


        /* -------------------------------------------------------------------------- */
        /*                with Price Filtering (for static range list)                */
        /* -------------------------------------------------------------------------- */

        setTimeout(() => {
            const StaticPiceRangeList = FilterArea.querySelector('[RentMyFilterByStaticPriceRange]');      
            if(StaticPiceRangeList){
                let childs = StaticPiceRangeList.children;
                childs = Array.from(StaticPiceRangeList.children);
                if(childs?.length){
                    childs.forEach(function(el){
                        el.addEventListener('click', function(event){
                            event.preventDefault();                            
                            let priceRangeEl = el.querySelector('[priceRange]');
                            let priceRangeAttr = priceRangeEl.getAttribute('priceRange');
                            let [min, max] = priceRangeAttr.split('-').map(Number);
                            RentMyParams.price_min = Number(min); 
                            RentMyParams.price_max = Number(max);
                            if(!min){
                                delete RentMyParams.price_min; 
                            }
                            if(!max){
                                delete RentMyParams.price_max; 
                            }

                            if(!min && !max){
                                let RentMyMinMaxClearBtn = priceFilterSection.querySelector('[RentMyMinMaxClearBtn]');
                                if(RentMyMinMaxClearBtn) RentMyMinMaxClearBtn.click();
                            }

                            myEmit('trigger-price', RentMyParams);

                            // Uncheck all radio input first
                            let radioElements = Array.from(StaticPiceRangeList.querySelectorAll('input[type=radio]'));
                            radioElements.forEach(radioEl => radioEl.checked = false); 
                            
                            // Check current radio input
                            let radioEl = el.querySelector('input[type=radio]');
                            if(radioEl) radioEl.checked = true;
                            
                        });
                    });
                }           
            }
        }, 0);




        /* -------------------------------------------------------------------------- */
        /*                         with Rental Type Filtering                         */
        /* -------------------------------------------------------------------------- */
        let RentMyFilterByRentalType = FilterArea.querySelector('[RentMyFilterByRentalType]');
        if(RentMyFilterByRentalType){
            let RentalTypes = FilterArea.querySelectorAll('input[type=radio][name=RentalType]');                  
            if(RentalTypes?.length){
                (Array.from(RentalTypes)).forEach(radio => { 
                    radio.addEventListener('click', (event) => {
                        let value = event.target.value;
                        if(value == 'rent' || value == 'buy'){
                            RentMyParams.purchase_type = value;
                        } else {
                            if(RentMyParams.purchase_type) delete RentMyParams.purchase_type;
                        }
                        myEmit('trigger-rental-type', RentMyParams);
                    });
                });
            }
        }
    }
    

    /* -------------------------------------------------------------------------- */
    /*                           with short by dropdown                           */
    /* -------------------------------------------------------------------------- */
    if(ShortByDropdown){
        let sort_by = RentMyParams?.sort_by || null;
        let sort_dir = RentMyParams?.sort_dir || null;
        let options = ShortByDropdown.querySelectorAll('option');
        if(options?.length){
            let short_by_value = `sort_by=${sort_by}&sort_dir=${sort_dir}`;
            (Array.from(options)).forEach(option => {
                if(option.value == short_by_value) option.selected = true;
            })
        }

        ShortByDropdown.addEventListener('change', (event) => {

            emitter.emit('onchange__productlist__filter', true)
            
            let value = event.target.value;
            let parts = value.split('&');
            parts?.forEach(part => {
                let subParts = part?.split('=');
                RentMyParams[subParts[0]] = subParts[1];
            })
            myEmit('changed-sort-dropdown', RentMyParams);
        })
    }    
})



</script>

<template>
    <div></div>
</template>