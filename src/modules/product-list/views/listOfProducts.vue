<script setup>
import FilterProducts from './Filter.vue'
import { ref, inject, onMounted, defineProps, provide } from 'vue';
import { useProductListStore } from '../controllers/productList.controller';
import Pagination from '@components/Pagination.vue';
import { GenerateCompoenentHtml } from "../productList.module";
let { helper, cookie, domElement, http, labelSelector, currency } = inject('utils');
let { userService } = inject('services');
let { wrapper, index: wrapperIndex } = defineProps(['wrapper', 'index']);
let globalLoader = inject('globalLoader');
import { Toaster, emitter } from '@/import-hub';

import { useWishlistStore } from "@stores/wishlist";
const wishlistStore = useWishlistStore()

const site_specific = JSON.parse( localStorage.getItem("rentmy_contents") )?.site_specific;
const show_sidebar = site_specific?.confg?.category?.show_sidebar;
const is_active_wish_list = site_specific?.confg?.inventory?.wishlist?.active



wrapper.id = `ProductListContainer${wrapperIndex}`;
let default_data = inject('rentmy_default_data');
let allowed_keys = inject('rentmy_default_data_keys');
let aliasesKey = {
    /*Dom key  :  Api Key*/
    'products': 'products_id',
    'tags': 'tag_id',
    'sortBy': 'sort_by',
    'sort': 'sort_dir',
};
let RentMyParams = domElement.parseRentMyData(wrapper, default_data, allowed_keys, aliasesKey);
let usingPagination = RentMyParams?.onLoad == 'paginate';
let passedCategoryId = ref(RentMyParams?.category); 
provide('passedCategoryId', passedCategoryId)
let lazyloadByClick = RentMyParams?.lazyloadByClick;

delete RentMyParams.onLoad;
delete RentMyParams.lazyloadByClick;

if(RentMyParams.products_id){
    delete RentMyParams.tag_id;
    delete RentMyParams.category;
}
let temporary_off_lazy_loader = ref(false);

let sampleProduct = wrapper?.querySelector('[RentMyProductItemSample]');
let paginationAreaInDom = wrapper?.querySelector('#RentMyPagination');
let parentNode = sampleProduct?.parentNode;
parentNode.innerHTML = '';
const productListStore = useProductListStore();

const LAST_PRODUCT_ATTRIBUTE = 'rentmy_last_product';
let allowDyanmicContent = inject('allowDyanmicContent');
let printSelector = inject('printSelector');

/* ---------------------------------------- */
/*         Generating Template HTML         */
/* ---------------------------------------- */
GenerateCompoenentHtml(wrapper, allowDyanmicContent, printSelector);

/* ----- End Generating Template HTML ----- */

async function loadProducts({page, useLoader, search=helper.withURL.getQuery('search')}={}){  
    if(page) RentMyParams = {...RentMyParams, page_no: page}; 

    if( RentMyParams?.page_no > productListStore?.lastProductivePage) return; //all page already loaded    

    isLoading.value = true;
    if(useLoader) globalLoader.show(); 
    let categoryUUID = helper.withURL.getQuery('category') || passedCategoryId.value;      
    if(categoryUUID){
        // await productListStore.getChildCategories(categoryUUID);
        await productListStore.getProductListByCategory(categoryUUID, RentMyParams, usingPagination);
    } else {
        await productListStore.getProductList(helper.clone(RentMyParams, {remove: [search ? 'all' : '']}), usingPagination);    
    }  
    isLoading.value = false;
    temporary_off_lazy_loader.value = false;

    let btn = wrapper.querySelector('[lazyloadByClick]');
    if(btn){
        if(productListStore.products?.length == 0) btn.hidden = true;
        else btn.hidden = false;
    }
        
    if(usingPagination){
        parentNode.innerHTML = '';
        // productListStore.clearProducts();
    }
    {
        let lastProduct = wrapper.querySelector(LAST_PRODUCT_ATTRIBUTE);
        if(lastProduct) lastProduct.removeAttribute(LAST_PRODUCT_ATTRIBUTE)
    }
    productListStore.products?.forEach((product, index) => {

        if(index > productListStore.loopStartIndex){
            const { currencyConfig } = productListStore;
            let clonedHTML = sampleProduct.cloneNode(true);
            let detailsPageUrl = product?.type == 2 ? window.RENTMY_GLOBAL.page?.package_details : window.RENTMY_GLOBAL.page?.product_details;            
            let productDetailsPageFullUrl = helper.withURL.generateURL(detailsPageUrl, product);

            const selectors = {
                product_name : '[product_name]',
                product_price : '[product_price]',
            }
            labelSelector.labelToSelectorAll(selectors, clonedHTML);

            if(is_active_wish_list){
                labelSelector.getQuerySelectAll('[ProductButtons]', clonedHTML).forEach(el => {
                    el.remove()
                })
                labelSelector.getQuerySelectAll('[RentMyProductPrice]', clonedHTML).forEach(el => {
                    el.classList.add('mb-0')
                })
            } else {
                
                labelSelector.getQuerySelectAll('[WishListBtnArea]', clonedHTML).forEach(el => {
                    el.remove()
                })
            }
    
            labelSelector.getQuerySelectAll('[RentMyProductImageUrl]', clonedHTML)?.forEach(el =>  {
                if (RENTMY_GLOBAL?.using_in_cli_project) {
                    if (el.tagName == 'A') {
                        el.href = productDetailsPageFullUrl;
                    } 
                    el.addEventListener('click', (e) => {                        
                        e.preventDefault();
                        RentMyEvent.emit('cdn:navigate_product_details_page', product)
                    })
                } else {
                    if(el.tagName == 'A'){
                        el.href = productDetailsPageFullUrl;
                    } else {
                        el.addEventListener('click', (e) => {
                            let locationHref  = windowLocation();
                            locationHref = productDetailsPageFullUrl //noted
                        })
                    }
                }
            });        
            labelSelector.getQuerySelectAll('img[RentMyProductImage]', clonedHTML)?.forEach(img => {
                let image = product?.images?.[0];
                image = (image?.image_small || image?.image_small_free || image?.image_large || image?.image_large_free);           

                if(image){
                    image = helper.generateImage(image, product?.id);
                } else {
                    image = RENTMY_GLOBAL?.images?.default_product_image;
                }
                img.src = image;     
                img.setAttribute('track-src', image);     
                img.addEventListener('error', (event) => {
                   event.target.src = RENTMY_GLOBAL?.images?.default_product_image;
                })
            });      
            labelSelector.getQuerySelectAll('[product_name]', clonedHTML)?.forEach(el =>  {
                el.innerHTML = product?.name;
                if (RENTMY_GLOBAL?.using_in_cli_project) {
                    if (el.tagName == 'A') {
                        el.href = productDetailsPageFullUrl;
                    } 
                    el.addEventListener('click', (e) => {
                        e.preventDefault();
                        RentMyEvent.emit('cdn:navigate_product_details_page', product)
                    })
                } else {
                    if (el.tagName == 'A'){
                        el.href = productDetailsPageFullUrl;
                    } else {
                        el.addEventListener('click', (e) => {                  
                            window.open(productDetailsPageFullUrl, '_self');                        
                        })
                    }
                }
                
            } );
            labelSelector.getQuerySelectAll('[product_price]', clonedHTML)?.forEach(function(el){
                let priceLabel = currency.formatListPrice(product.prices?.[0], currencyConfig);
      
                if(priceLabel){
                    el.innerHTML = priceLabel?.price;
                } else {
                    el.innerHTML = '&nbsp;';
                }
            } ); 
            labelSelector.getQuerySelectAll('[RentMyProductPrices]', clonedHTML)?.forEach(function(pricesContainer){

                const prices = product.prices?.[0];
                const base = prices?.base;
                const hourly = prices?.hourly?.[0];
                const daily = prices?.daily?.[0];
                const weekly = prices?.weekly?.[0];
                const monthly = prices?.monthly?.[0];


                let base_el = pricesContainer.querySelector('[ProductPriceType="base"]');
                let hourly_el = pricesContainer.querySelector('[ProductPriceType="hourly"]');
                let daily_el = pricesContainer.querySelector('[ProductPriceType="daily"]');
                let weekly_el = pricesContainer.querySelector('[ProductPriceType="weekly"]');
                let monthly_el = pricesContainer.querySelector('[ProductPriceType="monthly"]');

                if(base && base_el){
                    let price_value_element = base_el.querySelector('[ProductPriceValue]');
                    if(price_value_element) price_value_element.innerHTML = currency.format(base?.price, currencyConfig);
                } else {
                    if(base_el) base_el.remove();
                }

                if(hourly && hourly_el){
                    let price_value_element = hourly_el.querySelector('[ProductPriceValue]');
                    if(price_value_element) price_value_element.innerHTML = currency.format(hourly?.price, currencyConfig);
                } else {
                    if(hourly_el) hourly_el.remove();
                }

                if(daily && daily_el){
                    let price_value_element = daily_el.querySelector('[ProductPriceValue]');
                    if(price_value_element) price_value_element.innerHTML = currency.format(daily?.price, currencyConfig);
                } else {
                    if(daily_el) daily_el.remove();
                }

                if(weekly && weekly_el){
                    let price_value_element = weekly_el.querySelector('[ProductPriceValue]');
                    if(price_value_element) price_value_element.innerHTML = currency.format(weekly?.price, currencyConfig);
                } else {
                    if(weekly_el) weekly_el.remove();
                }

                if(monthly && monthly_el){
                    let price_value_element = monthly_el.querySelector('[ProductPriceValue]');
                    if(price_value_element) price_value_element.innerHTML = currency.format(monthly?.price, currencyConfig);
                } else {
                    if(monthly_el) monthly_el.remove();
                }
            } ); 
            labelSelector.getQuerySelectAll('[RentMyViewDetailsBtn]', clonedHTML)?.forEach(el =>  {
                if(allowDyanmicContent) el.innerHTML = site_specific?.checkout_payment?.view_order_details_button_lbl;
                if (RENTMY_GLOBAL?.using_in_cli_project){
                    if (el.tagName == 'A') {
                        el.href = productDetailsPageFullUrl;
                    } 
                    el.addEventListener('click', (e) => {
                        e.preventDefault();
                        RentMyEvent.emit('cdn:navigate_product_details_page', product)
                    })
                } else {
                    if(el.tagName == 'A'){
                        el.href = productDetailsPageFullUrl;
                    }else{
                        window.open(productDetailsPageFullUrl, '_self'); 
                    }
                }
            });
            labelSelector.getQuerySelectAll('[RentMyAddToCartBtn]', clonedHTML)?.forEach(el =>  {
                if(allowDyanmicContent) el.innerHTML = site_specific?.product_details?.add_to_cart;
                let { prices } = product;
                let _prices = helper.formatBuyRent(prices)
                let isBuyType = false
                if (_prices && Object.keys(_prices).length > 0) {
                    if(_prices?.buy?.type) isBuyType = true;
                }   
                el.addEventListener('click', (event)=>{
                    event.preventDefault();                        
                    let that = this;
                    that.disabled = true;  
                    
                    if(is_active_wish_list || (!is_active_wish_list && !isBuyType)){
                        if(RENTMY_GLOBAL?.using_in_cli_project){

                        } else {
                            window.open(productDetailsPageFullUrl, '_self')
                        }
                        return
                    }
                    
                    globalLoader.show()                       

                    productListStore.createCart(product).then(response => {
                        globalLoader.hide()
                        that.disabled = true;
                        if (response.status == 'OK') {
                            if(response.result.error){
                                Toaster().error(response.result.error);
                                return;
                            }
                            let RentMyProductImg = clonedHTML.querySelector('.RentMyProductImg');
                            let RentMyMiniCart = document.querySelector('.RentMyMiniCart');             
                            // domElement.dropInCart(RentMyProductImg, RentMyMiniCart);
                            // Toaster().success('Item added to cart');

                            localStorage.setItem('user_cart', JSON.stringify(response.result.data));
                        } else {
                            if(response.result.error){
                                Toaster().error(response.result.error);
                            }
                        }
                    })
                })
            });  
            labelSelector.getQuerySelectAll('[DetailsPageUrl]', clonedHTML)?.forEach(el =>  {
                if(el.tagName === 'A'){
                    el.href = productDetailsPageFullUrl
                } else {
                    el.addEventListener('click', () => {
                        if(RENTMY_GLOBAL.using_in_cli_project){

                        } else {
                            window.open(productDetailsPageFullUrl, '_self')
                        }
                    })
                }
            });  
            // wishlistStore

            function itemIsInWishList(product){
                let wishlIst = JSON.parse(localStorage.getItem('user_wishlist') || "[]")
                if(wishlIst && wishlIst?.wish_list_items?.length){
                    const { variants_products_id } = product.default_variant
                    return wishlIst?.wish_list_items.filter(item => item.variants_products_id == variants_products_id)?.[0] || null
                }
                return false
            }


            labelSelector.getQuerySelectAll('[RentMyAddToWishListBtn]', clonedHTML)?.forEach(el =>  {
                
                if(itemIsInWishList(product)){
                    const icon = el.querySelector('i')
                    if(icon){
                        icon.classList.add('la-heart')
                        icon.classList.remove('la-heart-o')
                    }
                }
               
                el.addEventListener('click', async (event)=>{
                    event.preventDefault();
                    globalLoader.show()  
                    let itemInWishlist = itemIsInWishList(product)
                    const icon = el.querySelector('i')
                    if(itemInWishlist){
                        await wishlistStore.deleteItem(itemInWishlist.id)
                        if(icon){
                            icon.classList.add('la-heart-o')
                            icon.classList.remove('la-heart')
                        }
                    } else {
                        await wishlistStore.addToList({product})
                        if(icon){
                            icon.classList.add('la-heart')
                            icon.classList.remove('la-heart-o')
                        }
                    }               
                    globalLoader.hide()                   
                })     
            });  
            
            if(index == productListStore.products?.length - 1){
                clonedHTML.setAttribute(LAST_PRODUCT_ATTRIBUTE, 'true');               
            }
    
            parentNode.appendChild(clonedHTML);
        }
    });
    globalLoader.hide();
    sampleProduct?.remove();
}

let isLoading = ref(false);


function isElementInViewport(productItem, adjust=0) {
    const rect = productItem.getBoundingClientRect();
    return (
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
        rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
}

async function handleScroll(event){
    if(lazyloadByClick) return;
    if(temporary_off_lazy_loader.value) return;
    if(isLoading.value) return;
    const productItems = wrapper.querySelectorAll(`[${LAST_PRODUCT_ATTRIBUTE}]`);
    for (let i = 0; i < productItems.length; i++) {
        const productItem = productItems[i];
        if (isElementInViewport(productItem)) {
            if (i === productItems.length - 1) {   
                isLoading.value = true; 
                await loadMoreProducts({useLoader: true});
                isLoading.value = false;                 
            }
        }
    }
}

async function loadMoreProducts(optons={}){
    let isLastPage = productListStore.paginateData?.isLastPage;
    if(!isLastPage){
        RentMyParams.page_no = parseInt((RentMyParams.page_no || 1)) + 1; 
        await loadProducts(optons);
    }
}
let isMounted = ref(false);

onMounted(async ()=>{
    if(!sampleProduct) {        
        Toaster().error('Sample product container not found');
        return;
    };
    await productListStore.init();
    let { setQuery, getQuery, deleteQuery, parseCategoryUIDfromUrl } = helper.withURL;

    try {
        allowed_keys.forEach(key => {
            if(getQuery(key)){
                let value = getQuery(key);
                if(key == 'page_no') value = Number(value);
                if(key == 'sort') value = String(value)?.toUpperCase();
                if(key in aliasesKey) key = aliasesKey[key];
                RentMyParams[key] = value;
            }
        }); 
    } catch (error) {
        console.warn('allowed_keys_error::', error);
    }   

    passedCategoryId.value = parseCategoryUIDfromUrl(RentMyParams) || RentMyParams?.category || null
    if(passedCategoryId.value){
        let selector = `li[rentmy-cat-uuid="${passedCategoryId.value}"]`
        

        const selectCurrentCat_and_setBreadCrumb = () => {
            let category_element = document.querySelector(selector)
            if(category_element){
                category_element.classList.add('Selected')
                let childs = Array.from(category_element.querySelectorAll('.rentmy-child-cat'))
                let ha_childs = childs?.length
                if(ha_childs){ 
                    // this is a parent cat, so there are some childs
                    let breaCrumbTitle = document.querySelector('[rentmybreadcrumbtitle="categoryProduct"]')
                    if(breaCrumbTitle) breaCrumbTitle.innerHTML = category_element.getAttribute(['rentmy-cat-name'])
                    childs.forEach(child_cat => {
                        child_cat.hidden = false
                        child_cat.setAttribute('showing', 'true')
                    });
                } else {
                    // this is a child category 
                    category_element.hidden = false

                    category_element.click()


                    let parent_el = category_element.parentElement
                    let childs = parent_el.querySelectorAll('li[rentmy-cat-uuid]')
                    childs.forEach(function(el){
                        el.hidden = false
                    })
                    

                }
            }
        }
          
        setTimeout(selectCurrentCat_and_setBreadCrumb, 1500);  
        setTimeout(selectCurrentCat_and_setBreadCrumb, 2000);  
    }
    
    if(RentMyParams.onLoad && RentMyParams.onLoad=='paginate'){
        usingPagination = true;
        delete RentMyParams.onLoad;
    }

    if(!usingPagination){
        deleteQuery('page_no');
        window.addEventListener('scroll', handleScroll);      
    }    
    await loadProducts();
    domElement.setWraperIsReady(wrapper);
    if(!isMounted.value){
        RentMyEvent.emit('cdn:product_list_page:mounted', true);
        isMounted.value = true;
    }

    if(lazyloadByClick){
        let btn = wrapper.querySelector('[lazyloadByClick]');
        if(btn){
            btn.addEventListener('click', async(event) => {
                event.preventDefault();
                delete RentMyParams.search;
                await loadMoreProducts({useLoader: true});
            })
        }
    }

    emitter.on('from-widget-search-product', (search) => {
        if(!search){
            deleteQuery('search');
            delete RentMyParams.search;
        }else{
            let setSearchParams = RentMyEvent.apply_filters('cdn:afterSearch:set:search:param:inQuery', true);
            if(setSearchParams) setQuery({search});
            RentMyParams.search = search;
        }
        parentNode.innerHTML = '';
        productListStore.clearProducts();
        loadProducts({useLoader: true, page: 1, search: true});
    })
})

let FilterArea = wrapper.querySelector('[RentMyFilterArea]');
if(FilterArea && show_sidebar === false) { 
    FilterArea.remove();
}  

provide('wrapper', wrapper);
provide('RentMyParams', RentMyParams);
provide('productListStore', productListStore);
provide('FilterArea', FilterArea);

function sortProducts(params){
    RentMyParams = { ...RentMyParams, ...params };
    RentMyParams.search = undefined

    parentNode.innerHTML = '';
    productListStore.clearProducts();
    temporary_off_lazy_loader.value = true;
    loadProducts({useLoader: true, page: 1});
}
function filterByCategory(uuid){
    parentNode.innerHTML = '';
    productListStore.clearProducts();
    temporary_off_lazy_loader.value = true;
    loadProducts({useLoader: true, page: 1});
}
function filterBy_tag_price_rentalType(params){
    RentMyParams = params;
    parentNode.innerHTML = '';
    productListStore.clearProducts();
    temporary_off_lazy_loader.value = true;
    loadProducts({useLoader: true, page: 1});
}

</script>

<template>
    <FilterProducts
    v-if="isMounted && show_sidebar"
    @changed-sort-dropdown="sortProducts" 
    @trigger-category="filterByCategory"
    @trigger-tag="filterBy_tag_price_rentalType"
    @trigger-price="filterBy_tag_price_rentalType"
    @trigger-rental-type="filterBy_tag_price_rentalType"
    >
    </FilterProducts>

    <template v-if="!productListStore.products?.length && RentMyParams.page_no <= 1 ">
        <teleport :to="parentNode">
            <p class="RentMyNoProductFoundMessage mb-5">{{ isLoading ? '' : 'No Product Found' }} </p>
        </teleport>
    </template>

    
    <template v-if="usingPagination">
        <template v-if="paginationAreaInDom">
            <teleport :to="paginationAreaInDom">
                <Pagination v-model="productListStore.paginateData" @jump-now="(page) => loadProducts({page, useLoader: true})" />
            </teleport>
        </template>
        <template v-else>
            <teleport :to="wrapper">
                <div id="RentMyPagination"> 
                    <Pagination v-model="productListStore.paginateData" @jump-now="(page) => loadProducts({page, useLoader: true})" />
                </div>
            </teleport>
        </template>    
    </template>    
    
</template>