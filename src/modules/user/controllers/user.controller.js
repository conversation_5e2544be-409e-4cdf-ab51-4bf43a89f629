import { defineStore } from 'pinia'
import { computed, reactive } from 'vue'
import userService from '../services/user.service'
import cookie from '@utils/cookie'
import helper from '@utils/helper'
import { Toaster, emitter } from '@/import-hub';

export const useUserStore = defineStore('users', () => {

  const state = reactive({
    isLogin: false,
    customer_profile: null,
    address_list: null,
  })

  const isLogin = computed(() => state.isLogin)
  const customer_profile = computed(() => state.customer_profile)
  const address_list = computed(() => state.address_list)
  const address_list_by_group = computed(() => helper.listGroupBy(state.address_list, 'type'))
  const locationId = RENTMY_GLOBAL.locationId


  const logoutCustomer = async () => {
    return await userService.logoutCustomer();
  }

  const getCustomerProfile = async () => {
    const customer_profile = await userService.getCustomerProfile()
    if(customer_profile?.status == 'OK') {
      state.customer_profile = customer_profile?.result?.data;
    } else {
      state.customer_profile = {};
    }
    return state.customer_profile;
  }

  const updateCustomerAvater = async (data) =>{
    return await userService.changeCustomerAvater(data);
  }

  const getCustomerAddress = async () => {
    const response = await userService.getCustomerAddress()
    if(response?.status == 'OK') {
      state.customer_address = response?.result?.data
      state.address_list = response.result.data;
    } else {
      state.address_list = {}
    }
  }

  const deleteAddress = async (addressId) => {
    const response = await userService.deleteAddress(addressId)
    if(response?.status == 'OK') {
      Toaster().success(response?.result?.message || 'Address deleted')
    } else {
      Toaster().error(response?.result?.message || 'Delete failed!')
    }
  }

  const addAddress = async ( type, payload) => {
    const response = await userService.addAddress(type, payload);
    if(response?.status == 'OK') {
      Toaster().success(response?.result?.message || 'Address added successful')
    } else {
      Toaster().error(response?.result?.message || 'Address added failed')
    }
  }

  const updateAddress = async (addressId, type, payload) => {
    const response = await userService.updateAddress(addressId, type, payload);
    if(response?.status == 'OK') {
      Toaster().success(response?.result?.message || 'Address deleted')
    } else {
      Toaster().error(response?.result?.message || 'Delete failed!')
    }
  }

  const getOrderList = async (params={}) =>{
    return await userService.getOrderList(params);
  }

  const getOrderDetails = async (order_id) => {
    return await userService.getOrderDetails(order_id);
  }

  const getOrderDetails2 = async (order_id) => {
    return await userService.getOrderDetails2(order_id);
  }

  const searchProduct = async (searchText) => {
    if(isLogin && locationId) {
      return await userService.searchProduct(searchText,locationId);
    }
    return null;
  }

  const searchProduct2 = async (searchText) => {
      return await userService.searchProduct2(searchText) ;   
  }

  const addOrderNotes = async (order_id,formData) => {
    return await userService.addOrderNotes(order_id,formData);
  }

  const getOrderNotes = async (order_id) => {
    return await userService.getOrderNotes(order_id);
  }

  const getOrderPayments = async (order_id) => {
    return await userService.getOrderPayments(order_id);
  }

  const deleteNote = async (note_id) => {
    return await userService.deleteNote(note_id);
  }

  const deletePayment = async (payment_id) => {
    return await userService.deletePayment(payment_id);
  }


  const updateItem = async (data) => {
    return await userService.updateItem(data);
  }

  const getProductForOrder = async (data) => {
    return await userService.getProductForOrder(data);
  }

  const addItemToOrder = async (data) => {
    return await userService.addItemToOrder(data);
  }

  const removeOrderItem = async (itemid) => {
    return await userService.removeOrderItem(itemid);
  }

  const getCurrencyConfig = async () => {
    return await userService.getCurrencyConfig();
  }

  const getOrderStatusList = async () => {
    return await userService.getOrderStatusList();
  }

  const changeCustomerPassword = async (data) => {
    return await userService.changeCustomerPassword(data);
  }

  const updateCustomerProfile = async (data) => {
    const rentmy_customer_token = cookie.getCookie('rentmy_customer_info')?.token;
    let response = await userService.updateCustomerProfile(data);
    if(response?.result?.status == 'OK'){
      if(response?.result?.message) {
        Toaster().success(response?.result?.message)
      }
      if(response?.result?.data){
        response.result.data['token'] = rentmy_customer_token;
        cookie.setCookie('rentmy_customer_info', response?.result?.data);
        window.RENTMY_GLOBAL.rentmy_customer_info = response?.result?.data;
        return true;
      }
    } else{
      if(response?.result?.message) {
        Toaster().error(response?.result?.message)
      }
      return false;
    }
  }

  const deleteOrder = async (order_id, type) => {
    return await userService.deleteOrder(order_id, type);
  }

    
  return {
    getCustomerProfile,
    isLogin,
    customer_profile,
    getCustomerAddress,
    deleteAddress,
    addAddress,
    updateAddress,
    updateCustomerAvater,
    getOrderList,
    getOrderDetails,
    getOrderDetails2,
    searchProduct,
    searchProduct2,
    addOrderNotes,
    getOrderNotes,
    getOrderPayments,
    deleteNote,
    deletePayment,
    updateItem,
    getProductForOrder,
    addItemToOrder,
    removeOrderItem,
    getCurrencyConfig,
    getOrderStatusList,
    changeCustomerPassword,
    updateCustomerProfile,
    deleteOrder,
    logoutCustomer,
    address_list,
    address_list_by_group,
  }
})