import http from  '@utils/http'
import { httpAuth } from  '@utils/http'
import helper from '@utils/helper'


export default {
  async getCustomerProfile() {
    const response = await httpAuth.get('customers/profile')
    return response.data
  },
  async updateCustomerProfile(data) {
    const response = await httpAuth.post('customers/profile',data)
    return response.data
  },
  async getCustomerAddress() {
    const response = await httpAuth.get('customers/address')
    return response.data
  },
  async deleteAddress(addressId) {
    const response = await httpAuth.delete(`customers/address/${addressId}`)
    return response.data
  },
  async addAddress(type, payload) {
    const response = await httpAuth.post(`customers/address?type=${type}`, payload)
    return response.data
  },
  async updateAddress(addressId, type, payload) {
    const response = await httpAuth.post(`customers/address/${addressId}?type=${type}`, payload)
    return response.data
  },
  async logoutCustomer() {
    localStorage.removeItem('rentmy_customer_info')
    helper.redirect('products_list')
    return true;
  },
  async changeCustomerAvater(formData) {
    const response = await httpAuth.post('customers/change-avatar',formData, { formData: true })
    return response.data
  },
  async changeCustomerPassword(data) {
    const response = await httpAuth.post('customers/change-password',data)
    return response.data
  },
  async getOrderList(params={}) {
    const url = `customers/orders`
    const response = await httpAuth.get(url, { params })
    return response.data
  },
  async getOrderDetails(order_id) {
    const url = `customers/orders/${order_id}`
    const response = await httpAuth.get(url)
    return response.data
  },
  async getOrderDetails2(order_id) {
    const url = `orders/customer/${order_id}`
    const response = await httpAuth.get(url)
    return response.data
  },
  async searchProduct(searchText, locationId) { // For auth required
    const url = `products/search?location=${locationId}&search=${searchText}`
    const response = await httpAuth.get(url)
    return response.data
  },
  async searchProduct2(searchText) { // No auth required
    const url = `products/search-product?search=${searchText}`
    const response = await http.get(url)
    return response.data
  },
  async addOrderNotes(order_id,formData) {
    const url = `orders/${order_id}/notes`
    const response = await httpAuth.post(url, formData, { formData: true })
    return response.data
  },
  async getOrderNotes(order_id) {
    const url = `orders/${order_id}/notes?page=1&limit=25&type=note&source=online`
    const response = await httpAuth.get(url)
    return response.data
  },
  async getOrderPayments(order_id) {
    const url = `order/${order_id}/payments?is_paid=true&list=false&log=false&memo=true`
    const response = await httpAuth.get(url)
    return response.data
  },
  async deleteNote(note_id) {
    const url = `notes/${note_id}`
    const response = await httpAuth.delete(url)
    return response.data
  },
  async deletePayment(payment_id) {
    const url = `payments/${payment_id}`
    const response = await httpAuth.delete(url)
    return response.data
  },
  async updateItem(data) {
    const url = `orders/update-item`
    const response = await httpAuth.post(url,data)
    return response.data
  },
  async getProductForOrder(data) {
    const order_id = data.order_id
    const variants_products_id = data.variants_products_id
    const location = data.location
    const start_date = data.start_date
    const end_date = data.end_date
    const url = `products/view/variant-product/${variants_products_id}?location=${location}&start_date=${start_date}&end_date=${end_date}&source=online&type=order&order_id=${order_id}`
    const response = await httpAuth.get(url)
    return response.data
  },
  async addItemToOrder(data) {
    const url = `orders/add-item`
    const response = await httpAuth.post(url,data)
    return response.data
  },
  async removeOrderItem(itemid) {
    const url = `orders/item/${itemid}/delete`
    const response = await httpAuth.delete(url)
    return response.data
  },
  async getCurrencyConfig() {
    const url = `currency-config`
    const response = await httpAuth.get(url)
    return response.data
  },
  async getOrderStatusList() {
    const url = `order/status`
    const response = await httpAuth.get(url)
    return response.data
  },
  async deleteOrder(order_id,type) {
    const url = `orders/${order_id}/status/${type}`
    const response = await httpAuth.get(url)
    return response.data
  },
}