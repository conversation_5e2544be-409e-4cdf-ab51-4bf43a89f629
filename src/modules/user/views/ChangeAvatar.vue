<script setup>
import { onMounted, ref, defineProps, computed, inject, defineComponent } from 'vue';
import { useUserStore } from '@modules/user/controllers/user.controller';
import { createComponentHTML } from "@utils/functions/withComponent";
import { Toaster, emitter } from '@/import-hub';
import Loader from '@components/Loader.vue';
let { domElement, labelSelector, helper } = inject('utils');
const assetURL = RENTMY_GLOBAL?.env?.ASSET_URL || import.meta.env.VITE_ASSET_URL
const setWraperIsReady = inject('setWraperIsReady');
const { wrapper } = defineProps(['wrapper']);
const userStore = useUserStore();
const globalLoader = inject('globalLoader');

labelSelector.autoAttriute(wrapper);

const site_specific = JSON.parse( localStorage.getItem("rentmy_contents") )?.site_specific;
const customer_portal_activated = site_specific?.confg?.customer?.active;
let customerPortalHTML = inject('customerPortalHTML'); 

if(!RENTMY_GLOBAL.rentmy_customer_info){
    window.location.replace(RENTMY_GLOBAL?.page?.login);
}

createComponentHTML(wrapper, [
    // Left Sidebar
    {
        selector: "[SideBar]",
        child: [
            {
                selector: "img[ProfileImage]",
                attr: { ':src': 'profileImage' },
            },
            {
                selector: "[customer_name]",
                text: `{{ userStore.customer_profile?.first_name + ' ' + userStore.customer_profile?.last_name }}`,        
            },
        ]    
    },
    {
        selector: '[FileUploadArea]',
        attr: { ':class': '{ active: payload.file }' },
        child: [
            {
                selector: '[FileName]',
                text: `{{ payload.file?.name ? payload.file?.name : "__EXISTING_HTML__" }}`
            },
            {
                selector: '[type=file]',
                attr: { 
                    'ref': 'upload_file', 
                    'accept': 'image/*', 
                    ':disabled': 'uploading', 
                    '@change': `onChangeFile` 
                }
            },
        ]
    },  
    {
        selector: '#RentMyFileUploadSubmitBtn',
        attr: { '@click.stop.prevent': 'onUploadFile($refs)' },
        text: `__EXISTING_HTML__ <span v-if="uploading" class="RrentMyBtnLoader"></span>`,
    },
    {
        selector: 'img[PreviewImage]',
        attr: { 'v-if': 'payload.file && base64String', ':src': 'base64String' },
    },
]);
let template = wrapper.innerHTML;
wrapper.innerHTML = '';

let ChangeAvatar = defineComponent({
    template,
    data(){
        return {
            userStore,
            uploading: false,
            base64String: null,
            payload: {
                old_password: null,
                password: null,
                confirm_password: null,
            },
            error: null,
        }
    },
    computed: {
        profileImage: function(){
            const { customer_profile } = userStore;            
            return customer_profile?.image ? `${assetURL}customers/${customer_profile.store_id}/${customer_profile.image}` : RENTMY_GLOBAL?.images?.default_profile_image;
        },
    },
    async mounted(){
        await userStore.getCustomerProfile();
        domElement.bindLogoutEvent(globalLoader);
        setWraperIsReady(wrapper);
        if(!customer_portal_activated){
            wrapper.innerHTML = customerPortalHTML;
        } 
    },
    methods: {
        onUploadFile: async function(){ 
            if(this.uploading) return;
            if(!this.$refs.upload_file.files?.length){
                Toaster().warning('Please select a file');
                this.payload.file = null;
                return;
            }
            this.uploading = true;
            const response = await userStore.updateCustomerAvater(this.payload);
            if(response?.status == 'OK') {
                await userStore.getCustomerProfile();
                setTimeout(() => {
                    Toaster().success('Avatar Changed Successfully');
                    this.uploading = false;
                    this.payload.file = null;
                    this.$refs.upload_file.value = null;
                }, 200);
            } else{
                this.uploading = false;
                this.error = response?.result?.message;
            }
            
        },
        onChangeFile: function({target}){
            try {
                let file = target.files?.[0];
                this.payload.file = file;

                let reader = new FileReader();   
                reader.onload = (e)=>{
                    this.base64String = e.target.result;
                };
                reader.readAsDataURL(file);
            } catch(error) {
                this.payload.file = null;
                this.base64String = null;
            }
        },
    }
});



</script>
<template>

    <teleport :to="wrapper">
        <ChangeAvatar></ChangeAvatar>
    </teleport>

</template>