<script setup>
import { onMounted, ref, defineProps, computed, inject, defineComponent } from 'vue';
import { useUserStore } from '@modules/user/controllers/user.controller';
import { createComponentHTML } from "@utils/functions/withComponent";
import { Toaster, emitter } from '@/import-hub';
import Loader from '@components/Loader.vue';
let { domElement, labelSelector, helper } = inject('utils');
const assetURL = RENTMY_GLOBAL?.env?.ASSET_URL || import.meta.env.VITE_ASSET_URL;
const setWraperIsReady = inject('setWraperIsReady');
const { wrapper } = defineProps(['wrapper']);
const userStore = useUserStore();
const globalLoader = inject('globalLoader');

labelSelector.autoAttriute(wrapper);

if(!RENTMY_GLOBAL.rentmy_customer_info){
    window.location.replace(RENTMY_GLOBAL?.page?.login);
}

const site_specific = JSON.parse( localStorage.getItem("rentmy_contents") )?.site_specific;
const customer_portal_activated = site_specific?.confg?.customer?.active;
let customerPortalHTML = inject('customerPortalHTML'); 

createComponentHTML(wrapper, [
    // Left Sidebar
    {
        selector: "[SideBar]",
        child: [
            {
                selector: "img[ProfileImage]",
                attr: { ':src': 'profileImage' },
            },
            {
                selector: "[customer_name]",
                text: `{{ userStore.customer_profile?.first_name + ' ' + userStore.customer_profile?.last_name }}`,        
            },
        ]    
    },    
    {
        selector: ".RentMyAlertMessage",
        attr: { '@click': 'error = ""' },
        text: `{{ error }}`,        
    },
    {
        selector: "#RentMyChangePasswordForm",
        attr: { '@submit.prevent': 'OnSubmitResetPasswordForm' },  
        child: [
            {
                selector: '[name=old_password]',
                attr: { 'v-model': 'payload.old_password' }
            },
            {
                selector: '[name=password]',
                attr: { 'v-model': 'payload.password' }
            },
            {
                selector: '[name=confirm_password]',
                attr: { 'v-model': 'payload.confirm_password' }
            },
            {
                selector: '#RentMyChangePasswordSubmit',
                attr: { '@click.prevent.stop': 'OnSubmitResetPasswordForm' },
                text: `
                    __EXISTING_HTML__
                    <span v-if="changing" class="RrentMyBtnLoader"></span>
                `
            },
        ],  
    },
]);

let template = wrapper.innerHTML;
wrapper.innerHTML = '';

let PasswordChange = defineComponent({
    template,
    data(){
        return {
            userStore,
            changing: false,
            payload: {
                old_password: null,
                password: null,
                confirm_password: null,
            },
            error: null,
        }
    },
    computed: {
        profileImage: function(){
            const { customer_profile } = userStore;            
            return customer_profile?.image ? `${assetURL}customers/${customer_profile.store_id}/${customer_profile.image}` : RENTMY_GLOBAL?.images?.default_profile_image
        },
    },
    async mounted(){
        await userStore.getCustomerProfile();
        domElement.bindLogoutEvent(globalLoader);
        setWraperIsReady(wrapper);
        if(!customer_portal_activated){
            wrapper.innerHTML = customerPortalHTML;
        } 
    },
    methods: {
        OnSubmitResetPasswordForm: async function(){
            let { old_password, password, confirm_password } = this.payload;
            if(!old_password || !password || !confirm_password){
                this.error = 'Please fill the form correctly';
                return;
            } else {
                this.error = null;
            }
            this.changing = true;
            const response = await userStore.changeCustomerPassword(this.payload);
            this.changing = false;
            if(response?.status == 'OK') {
                Toaster().success(response?.result?.message); 
                this.payload.old_password = null;
                this.payload.password = null;
                this.payload.confirm_password = null;
            } else {
                this.error = response?.result?.message;
            }
        },
    }
});

</script>
<template>
    <teleport :to="wrapper">
        <PasswordChange></PasswordChange>
    </teleport>
</template>