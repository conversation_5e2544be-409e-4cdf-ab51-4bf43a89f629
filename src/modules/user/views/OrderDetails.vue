<script setup>
import http from '@utils/http';
import { onMounted, ref, reactive, computed, toRaw, inject, defineProps } from 'vue';
import Confirmation from '@components/modal/Confirmation.vue';
let { domElement, labelSelector, helper, currency } = inject('utils');
import { getOrderStatus } from '@utils/functions';
import { useUserStore } from '../controllers/user.controller';
import ProductSearchForOrderDetails from '@components/ProductSearchForOrderDetails.vue';
import NoticeBar from '@components/NoticeBar.vue';
const setWraperIsReady = inject('setWraperIsReady');
const assetURL = RENTMY_GLOBAL?.env?.ASSET_URL || import.meta.env.VITE_ASSET_URL
const globalLoader = inject('globalLoader');
const site_specific = JSON.parse(localStorage.getItem('rentmy_contents'))?.site_specific;
const confg = JSON.parse(localStorage.getItem('rentmy_contents'))?.site_specific?.confg;
const order_sequence = !!confg?.order?.order_sequence;
const order_number_prefix = confg?.order?.order_number_prefix;

// const allowDyanmicContent = inject('allowDyanmicContent')

const log = console.log
// const dynamicLabesOf_checkout_payment = {
//     lbl_name: allowDyanmicContent ? '' : ''
// }

const customerPortalHTML = inject('customerPortalHTML')

if(!RENTMY_GLOBAL.rentmy_customer_info){
    window.location.replace(RENTMY_GLOBAL?.page?.login);
}

 
const customer_portal_activated = confg?.customer?.active;
 

let props = defineProps({
    orderId: {
        default: null,
        required: false,
        type: [String, Number],
    },
    wrapper: {
        default: null
    }
})

const order_summery = [
    'order_id','order_subtotal','order_quantity','order_delivery_charge_total',
    'order_payment_status','order_status','order_date_time','order_total_discount',
    'order_sales_tax','order_de','order_grand_total'
]

const billing_address = [
    'billing_name','billing_email','billing_mobile','billing_country',
    'billing_address_line_1','billing_address_line_2','billing_city','billing_zip',
    'billing_state','billing_company','billing_address_info','signature',
]

const payment_label = [
    'total_payment_amount','total_paid_amount','total_due_amount'
]

const asset_url = RENTMY_GLOBAL?.env?.ASSET_URL || import.meta.env.VITE_ASSET_URL;
const AdditionalInfoSection = props.wrapper.querySelector('[AdditionalInfoSection]')
const AdditionalFields = props.wrapper.querySelector('[AdditionalInfoSection] [AdditionalFields]')

const ShippingInfoSection = props.wrapper.querySelector('[ShippingInfoSection]')
const ShippingInfoSectionContent = props.wrapper.querySelector('[ShippingInfoSection] [ShippingInfoSectionContent]')

const order_details = ref(null)
const order_signature = ref(null)
const order_id = ref(null)
const selectors = ref(null)
const noteList = ref([])
const note_serial = ref([])
const note_attributes = ref({})

const paymentData = ref({})
const paymentList = ref([])
const payment_serial = ref([])
const payment_attributes = ref({})


const inventoryList = ref([])
const inventory_serial = ref([])
const inventory_attributes = ref({})

const inventorySearchProduct = ref(null)
const customer_profile = ref({})

const currencyConfig = ref(null)
const orderStatusList = ref([])

const customer_selectors = {
    customer_name : '[customer_name]',
    rentmy_customer_profile_image: '[rentmy_customer_profile_image]',
}
labelSelector.labelToSelectorAll(customer_selectors);
const withPaymentAddStatus = ref(null);


async function getOrderPayments(userStore){
    const payment_response = await userStore.getOrderPayments(order_id.value)
    if(payment_response?.status =='OK') {
        paymentData.value = payment_response.result.data
        paymentList.value = payment_response.result.data.list            
        if(Number(paymentData.value?.memo?.due) <= 0){
            let paymentButton = document.getElementById('RentMyAddPaymentBtn');
            // if(paymentButton) paymentButton.hidden = true;
        }
    }
}

function displayOrderDate(orderDetails){
    let element = document.querySelector('[rentmy-order-date-time]');
    let show_start_time = confg.show_start_time;
    let show_end_time = confg.show_end_time;

    let start_date = show_start_time ? orderDetails.start_date : orderDetails?.start_date?.split(' ')?.[0];
    let end_date = show_end_time ? orderDetails?.end_date : orderDetails.end_date?.split(' ')?.[0];
    
    let date_range = [helper.formatDate(start_date, confg?.date_format), helper.formatDate(end_date, confg?.date_format)].join(' - ');
    
    if(element){
        if(start_date && end_date){        
            element.innerHTML =`<span>${date_range}</span>`;
        } else {
            element.style.display = 'none';
        }
    }
}

onMounted(async ()=>{

    if(!domElement.userIsAuthenticated()) return;
    let payStatus = helper.withURL.getQuery('paymentStatus');
    if(payStatus && (['success', 'failed']).includes(payStatus)){
        let message = (payStatus == 'success') ? 'Your payment is successful' : 'Your payment is failed';
        withPaymentAddStatus.value = {
            status: payStatus,
            message: message, 
        }
        helper.withURL.deleteQuery('paymentStatus');
    }

    const orderDetailsSelectors = {
        order_summery : await labelSelector.createSelectorFromArray(order_summery),
        billing_address : await labelSelector.createSelectorFromArray(billing_address),
        payment: await labelSelector.createSelectorFromArray(payment_label)
    }
    for (const item in orderDetailsSelectors) {
        if (Object.hasOwnProperty.call(orderDetailsSelectors, item)) {
            labelSelector.labelToSelectorAll(orderDetailsSelectors[item])
        }
    }
    selectors.value = orderDetailsSelectors

    initAddNoteForm();
    initNoteTable();
    initPaymentTable();
    initInventoryTable();

    const userStore = await useUserStore()
    customer_profile.value = await userStore.getCustomerProfile()
    const customer_images = customer_profile.value.image ? `${assetURL}customers/${customer_profile.value.store_id}/${customer_profile.value.image}` : RENTMY_GLOBAL?.images?.default_profile_image;
    labelSelector.updateImage(customer_selectors.rentmy_customer_profile_image, customer_images);

    order_id.value =  await helper.withURL.getQuery('order_id') || props.orderId
    if(order_id?.value) {
        const order_response = await userStore.getOrderDetails(order_id.value)
        if(order_response?.status =='OK') {
            order_details.value = order_response.result.data
            if(order_details.value?.custom_values){
                order_details.value.custom_values = JSON.parse(order_details.value.custom_values)
            }
            displayOrderDate(order_details.value);
            inventoryList.value = await formatInventoryTable(order_response.result.data.order_items)
        }
        //  Details calling for signature
        userStore.getOrderDetails2(order_id.value).then(order_response2 => {
            if(order_response2?.status =='OK') {
                order_signature.value = order_response2.result.data?.signature;
            }
        })
        
        const note_response = await userStore.getOrderNotes(order_id.value)
        if(note_response?.status =='OK') {
            noteList.value = note_response.result.data
        }

        await getOrderPayments(userStore);

        const currency_response = await userStore.getCurrencyConfig();
        if(currency_response?.status =='OK') {
            currencyConfig.value =  currency_response.result.data
        }

        const order_status_response = await userStore.getOrderStatusList();
        if(order_status_response?.status =='OK') {
            if(order_status_response?.result?.data && order_status_response?.result?.data?.length) {
                order_status_response?.result?.data.map((status, i) => {
                    if (status.label !== 'Paid Other') {
                        orderStatusList.value.push(status);
                    }
                    if (status.child) {
                        status.child.map((child, j) => {
                        const mod_child = {
                            id: child.id,
                            label: child.label,
                            isChild: true
                        };
                        orderStatusList.value.push(mod_child);
                        });
                    }
                });
            }
        }
    }

    // With Payment Button
    const addPaymentButton = document.getElementById('RentMyAddPaymentBtn');
    if(addPaymentButton){
        addPaymentButton.addEventListener('click', (event)=>{
            const order_id = helper.withURL.getQuery('order_id') || props.orderId;
            const customer_id = window.RENTMY_GLOBAL.rentmy_customer_info?.customer_id
            const location_id = window.RENTMY_GLOBAL.locationId;
            const store_slug = window.RENTMY_GLOBAL.store_name;
            const payment_source = 'online_checkout';
            const encodedData = helper.withURL.encodeString(`o=${order_id}&s=${store_slug}&l=${location_id}&c=${customer_id}&p=${payment_source}`).replace(/=/g, '');
            const PAYMENT_DOMAIN = RENTMY_GLOBAL?.env?.PAYMENT_DOMAIN || import.meta.env.VITE_PAYMENT_DOMAIN;
            // Example: http://localhost/cdn/rentmy-orders/?order_id=77696&paymentStatus=failed
            let makeUrl = (params={}) => helper.withURL.urlEncoded(helper.withURL.setQuery(params, null, true));
            const successUrl = makeUrl({paymentStatus: 'success'});
            const cancelUrl = makeUrl({paymentStatus: 'failed'});
            const paymentURL = `${PAYMENT_DOMAIN}payments/${encodedData}/?success=${successUrl}&cancel=${cancelUrl}`;
            window.open(paymentURL);            
        })
    }
    setWraperIsReady(props.wrapper)
    if(!customer_portal_activated){
        props.wrapper.innerHTML = customerPortalHTML;
    }
})

async function formatInventoryTable(data = []) {
    let result = []
    if(data.length) {
        data.forEach((inventory)=>{
            let item = {}
            if(inventory?.product) {
                item['product'] = inventory?.product;
            }
            if(inventory?.product?.name) {
                item['product_discription'] =  inventory?.product?.name 
            }
            if(inventory?.product?.variant_chain && inventory?.product?.variant_chain != 'Unassigned: Unassigned') {
                item['product_discription_variant_chain'] =  inventory?.product?.variant_chain 
            }
            item['price'] = inventory?.total
            item['subtotal'] = inventory?.sub_total
            item['quantity'] =  inventory?.quantity
            item['item_id'] =  inventory?.id
            item['product_id'] =  inventory?.product_id
            item['variants_products_id'] =  inventory?.variants_products_id

            result.push(item)
        })
    }
    return result;
}

function initAddNoteForm() {
    const addNoteForm = {
    formId: 'RentMyNoteAddForm',
    addNoteBtn: 'RentMyNoteAddBtn',
    submitBtn: 'RentMyNoteSubmitBtn',
    cancelBtn: 'RentMyNoteCancelBtn'
}

domElement.toggleSection(addNoteForm.formId);

const formId = document.getElementById(addNoteForm.formId)
const submitBtn = document.getElementById(addNoteForm.submitBtn)
const cancelBtn = document.getElementById(addNoteForm.cancelBtn)
const addNoteBtn = document.getElementById(addNoteForm.addNoteBtn);

if(addNoteBtn) {
    addNoteBtn.addEventListener('click', ()=>{
        formId.hidden = false;
    })
}


if(formId && submitBtn) {
    submitBtn.addEventListener('click', async ()=>{
        submitBtn.disabled = true;
        const text = document.getElementById('RentMyNote');
        const file = document.getElementById('RentMyFile');
        const formData = new FormData();
        formData.append('is_private',false);
        if(file && file?.files?.length) {
            const selectedFile = file.files[0];
            formData.append('file', selectedFile);
        }
        if(text && text?.value) {
            formData.append('note',text.value)
        }
        if(text?.value || file?.files?.length) {
            const userStore = await useUserStore()
            const add_note = await userStore.addOrderNotes(order_id.value,formData)
            if(add_note?.status =='OK') {
                const note_response = await userStore.getOrderNotes(order_id.value)
                    submitBtn.disabled = false;
                if(note_response?.status =='OK') {
                    noteList.value = note_response.result.data
                    text.value = '';
                    file.value = '';
                    formId.hidden = true;
                }

            }
            submitBtn.disabled = false;
            
        }
    })
}

if(formId && cancelBtn) {
    cancelBtn.addEventListener('click', ()=>{
        const text = document.getElementById('RentMyNote');
        const file = document.getElementById('RentMyFile');
        if(text) text.value = '';
        if(file) file.value = '';
        formId.hidden = true;
    })
}
}

const noteTableData = document.getElementById('RentMyOrderNotes');
const noteTbody = noteTableData?.querySelector('table tbody')
function initNoteTable() {
    const table_tr = noteTbody?.querySelector('tr')
    if(table_tr && table_tr?.cells && table_tr?.cells?.length) {
        for (var i = 0; i < table_tr.cells?.length; i++) {
            let content = table_tr.cells[i].textContent.replace(/\s/g, "").replace('{{','').replace('}}','');
            if (content) {
                note_serial.value.push(content);
            }       
            note_attributes.value[content] = domElement.getAttributes(table_tr?.cells[i])
        }
    }
    table_tr?.remove()
}

async function deleteNote(note) {
    if(!note?.id) return;
    const userStore = await useUserStore()
    const response = await userStore.deleteNote(note.id)
    if(response?.status =='OK') {
        const note_response = await userStore.getOrderNotes(order_id.value)
        if(note_response?.status =='OK') {
            noteList.value = note_response.result.data
        }
    }
}

const paymentTableData = document.getElementById('RentMyOrderPayment');
const paymentTbody = paymentTableData?.querySelector('table tbody')
function initPaymentTable() {
    const table_tr = paymentTbody?.querySelector('tr')
    if(table_tr && table_tr?.cells && table_tr?.cells.length) {
        for (var i = 0; i < table_tr.cells.length; i++) {
            let content = table_tr.cells[i].textContent.replace(/\s/g, "").replace('{{','').replace('}}','');
            if (content) {
                payment_serial.value.push(content);
            }       
            payment_attributes.value[content] = domElement.getAttributes(table_tr.cells[i])
        }
    }
    table_tr?.remove()
}

const inventoryTableData = document.getElementById('RentMyOrderInventory');
const inventoryTbody = inventoryTableData?.querySelector('table tbody')
function initInventoryTable() {
    const table_tr = inventoryTbody?.querySelector('tr')
    if(table_tr && table_tr?.cells && table_tr?.cells.length) {
        for (var i = 0; i < table_tr.cells.length; i++) {
            let content = table_tr.cells[i].textContent.replace(/\s/g, "").replace('{{','').replace('}}','');
            if (content) {
                inventory_serial.value.push(content);
            }       
            inventory_attributes.value[content] = domElement.getAttributes(table_tr.cells[i])
        }
    }
    table_tr?.remove()
}


async function deletePayment(payment) {
    if(!payment?.id) return;
    const userStore = await useUserStore()
    const response = await userStore.deletePayment(payment.id)
    if(response?.status =='OK') {
        const payment_response = await userStore.getOrderPayments(order_id.value)
        if(payment_response?.status =='OK') {
            paymentData.value = payment_response.result.data
            paymentList.value = payment_response.result.data.list
        }
    }
}
const showModal = ref(false)
const noteImagePopup = ref('')
const actions = {
    noteImagePopup: function(note){
        showModal.value = true,
        noteImagePopup.value = `<img src="${note.file_path}" alt="${note.content_type}">`
    }
}
const placeHolderImages = RENTMY_GLOBAL?.images?.default_product_image;
function errorImage(e) {
  e.target.src = placeHolderImages
 }

 const processing = ref(false)
async function decreaseQty(inventory) {
    if(processing.value) return;
    if(inventory.quantity == 1) return;
    let data = {
        item_id: inventory.item_id,
        product_id: inventory.product_id,
        order_id: order_id.value,
        quantity: inventory.quantity - 1,
        variants_products_id: inventory.variants_products_id,
        add_to_order_total: 1
        }
        if(globalLoader.isShowing()) return;
        globalLoader.show();
        await updateQuantaty(data);
        globalLoader.hide();
}

async function increaseQty(inventory) {
    if(processing.value) return;
    let data = {
        item_id: inventory.item_id,
        product_id: inventory.product_id,
        order_id: order_id.value,
        quantity: inventory.quantity + 1,
        variants_products_id: inventory.variants_products_id,
        add_to_order_total: 1
        }
        if(globalLoader.isShowing()) return;
        globalLoader.show();
        await updateQuantaty(data);
        globalLoader.hide();
}

async function updateQuantaty(data) {
    processing.value = true;
    const userStore = await useUserStore()
    const response = await userStore.updateItem(data)
    if(response?.status =='OK') {
        const order_response = await userStore.getOrderDetails(order_id.value)
        if(order_response?.status =='OK') {
            order_details.value = order_response.result.data
            inventoryList.value = await formatInventoryTable(order_response.result.data.order_items)
            await getOrderPayments(userStore);
        }
    processing.value = false;
    } else {
        processing.value = false;
    }

}
const showSearchBox = ref(false)
const addInventorySearchBtn = document.getElementById('RentMyAddInventoryItemBtn');
const addInventorySearch = document.querySelector('[rentmy-add-inventory-search]');
const productDetails = '[rentmy-order-product-details]'

const productDetialsDiv = document.querySelector(productDetails)
if(productDetialsDiv) productDetialsDiv.classList.add('my-3');

if(addInventorySearchBtn) {
    addInventorySearchBtn.addEventListener('click',()=>{
        showSearchBox.value = !(showSearchBox.value)
    })
}

let searchProductQty = ref(1);
let orderType = ref(null);
let afterSearch = reactive({
    product: null,
    variants: null,
    customFields: null,
    rent_start: null,
    rent_end: null,
    price: null,
});
const locationId = RENTMY_GLOBAL.locationId
async function getProductFromSearch(value) {
    afterSearch.product = value;
    const userStore = await useUserStore();
    orderType.value = value.type
    const product_id = value.data.id;
    const variant_id = value.data.variants_products_id;
    const searcrhData = {
        order_id: order_id.value,
        variants_products_id: variant_id,
        location: locationId,
        start_date: order_details.value.start_date,
        end_date: order_details.value.end_date
    }
    if(value.type === 'rent'){
        let variantResponse = await http.get(`/products/view/variant-product/${variant_id}`);
        if(variantResponse.data.status == 'OK'){
            afterSearch.variants = variantResponse.data?.result?.data;
            let prices = afterSearch.variants?.prices?.[0];
            if(prices){
                let isSelectedFirst = false;
                for(let key in prices){
                    let price = prices[key][0];
                    if(name !== 'base' && !isSelectedFirst){
                        afterSearch.price = price;

                        price.selected = true;
                        isSelectedFirst = true;
                    }
                }
            }
        }
        let customFieldResponse = await http.get(`/products/custom-fields/values/${product_id}`);
        if(customFieldResponse.data.status == 'OK'){
            afterSearch.customFields = customFieldResponse.data?.result?.data;
        }
    }
    const response = await userStore.getProductForOrder(searcrhData)
    if(response.status == 'OK') {
        inventorySearchProduct.value = response.result.data
        searchProductQty.value = 1
    }
}

let calculatePrice = computed(() => {
    if(afterSearch.price?.price){
        return afterSearch.price?.price;
    } else {
        return  inventorySearchProduct.value?.prices[0]?.base?.price
    }
})

async function updateProductQty(type) {
    if(type == 'increase') {
        searchProductQty.value += 1
    }
    if(type == 'decrease' ) {
        if(searchProductQty.value == 1) return
        searchProductQty.value -= 1
    }
}

async function removeOrderItem(product) {
    if(!product?.item_id) return;
    const userStore = await useUserStore();
    const response = await userStore.removeOrderItem(product?.item_id)
    if(response?.status == 'OK') {
        const order_response = await userStore.getOrderDetails(order_id.value)
        if(order_response?.status =='OK') {
            order_details.value = order_response.result.data
            inventoryList.value = await formatInventoryTable(order_response.result.data.order_items)
            await getOrderPayments(userStore);
        }
    }
}

async function addItemToOrder(product) {
    const type = orderType.value
    let price = 1
    if(type == 'rent'){
        price = calculatePrice.value * searchProductQty.value;
    } else {
        if(product?.prices[0]?.base?.price) {
            price = searchProductQty.value * product?.prices[0]?.base?.price;
        } else if (product?.rental_price) {
            price = searchProductQty.value * product?.rental_price;
        }
    }


    let data = {
        quantity: searchProductQty.value,
        order_id: order_id.value,
        variants_products_id: product.quantity.variants_products_id,
        sales_tax: product.sales_tax,
        product_id: product.id,
        price:  price,
        rental_type: type,
        location: locationId,
        rent_start: afterSearch.rent_start || order_details.value.rent_start,
        rent_end: afterSearch.rent_end || order_details.value.rent_end,
        additional: 'add',
        source: 'onlineAfterOrder',
        custom_fields: []
    }
    const userStore = await useUserStore();
    const response = await userStore.addItemToOrder(data)
    if(response?.status == 'OK') {
        const order_response = await userStore.getOrderDetails(order_id.value)
        if(order_response?.status =='OK') {
            order_details.value = order_response.result.data
            inventoryList.value = await formatInventoryTable(order_response.result.data.order_items)
        }
    }
    inventorySearchProduct.value = null;
}

function getProductImage(product) {
    if(!product?.images?.length) return placeHolderImages
    const images = product?.images
    let imageUrl = placeHolderImages
    for (let i = 0; i < images.length; i++) {
        const image = images[i];
        if(image?.image_large) {
            imageUrl = image.image_large
            break
        } else if (image?.image_small){
            imageUrl = image.image_small
            break
        } else if (image?.image_original){
            imageUrl = image.image_original
            break
        }        
    }
    const type = 'products'
    const product_id = product.id
    const src_url = `${asset_url}${type}/${customer_profile.value.store_id}/${product_id}/${imageUrl}`
    return src_url;

}

function getPaymentStatus(statusId) {
    const payment_status = [{ id: 1, label: 'Paid'},{ id: 2, label: 'Unpaid' },{ id: 3, label: 'Partial Paid' }];
    const status = payment_status.find(x => statusId == x.id);
    if (status) {
      return status.label;
    } else {
      return '';
    }
}

async function onChangeDatepicker(dates) {
    let { startDate, endDate, picker } = dates;
    
    
    const type = orderType.value;
    const product = inventorySearchProduct.value;

    afterSearch.rent_start = startDate;
    afterSearch.rent_end = endDate;

    let payload = {
        quantity: searchProductQty.value,
        order_id: order_id.value,
        variants_products_id: product.quantity.variants_products_id,
        sales_tax: product.sales_tax,
        product_id: product.id,
        price: calculatePrice.value,
        rental_duration: type,
        rental_type: type,
        term: 1,
        price_id: afterSearch.price?.id,
        location: locationId,
        rent_start: startDate,
        rent_end: endDate,
        deposit_amount: 0,
        deposite_tax: "false",
        is_apply: true,
        custom_fields: []
    };
    let response = await http.post('/get-price-value', payload);
    if(response.data.status == 'OK'){
        let { start_date, end_date } = response.data.result;
        afterSearch.rent_start = start_date;
        afterSearch.rent_end = end_date;
        picker.setStartDate(start_date);
        picker.setEndDate(end_date);
    }
}

const customer_name_list = document.querySelectorAll(customer_selectors.customer_name);
</script>

<template>

    <NoticeBar v-model="withPaymentAddStatus" :usingPopup="!!orderId"></NoticeBar>

    <!-- {{ customer_name }} -->
    <template v-if="customer_profile?.first_name && customer_profile?.last_name">
        <template v-if="customer_name_list && customer_name_list?.length"> 
            <template v-for="(customer_name, index) in customer_name_list" :key="index">
                <Teleport :to="customer_name"> 
                    {{ customer_profile?.first_name +' '+ customer_profile?.last_name }}
                </Teleport>
            </template>
        </template>
    </template>
    
    <template v-if="selectors?.order_summery">
        <template v-for="(value, key) in selectors.order_summery" :key="key">
            <template v-if="value">
                <template v-if="labelSelector.getQuerySelectAll(value)?.length"> 
                    <template v-for="element in labelSelector.getQuerySelectAll(value)" :key="element">
                        <Teleport :to="element">
                            <template v-if="key =='order_id'">
                                {{ order_sequence ? (order_number_prefix + order_details?.id) : order_details?.id }}
                            </template>
                            <template v-if="key =='order_quantity'">
                                {{ order_details?.total_quantity }}
                            </template>
                            <template v-if="key =='order_payment_status'">
                                {{ getPaymentStatus(order_details?.payment_status) }}
                            </template>
                            <template v-if="key =='order_status'">
                                {{ getOrderStatus(order_details?.status) }}
                            </template>
                            <template v-if="key =='order_date_time'">
                                {{ order_details?.created }}
                            </template>
                            <template v-if="key =='order_subtotal'">
                                {{ currency.format(order_details?.sub_total,currencyConfig) }}
                            </template>
                            <template v-if="key =='order_delivery_charge_total'">
                                {{ currency.format(order_details?.delivery_charge,currencyConfig) }}
                            </template>
                            <template v-if="key =='order_total_discount'">
                                {{ currency.format(order_details?.total_discount,currencyConfig) }}
                            </template>
                            <template v-if="key =='order_sales_tax'">
                                {{ currency.format(order_details?.tax?.total || 0 ,currencyConfig) }}
                            </template>
                            <template v-if="key =='order_grand_total'">
                                {{ currency.format(order_details?.total,currencyConfig) }}
                            </template>
                            <!--                             
                            <template v-if="key =='order_de'">
                                {{ currency.format(order_details?.tax?.regular[0].total,currencyConfig) }}
                            </template> -->
                        </Teleport>
                    </template>
                </template>
            </template>
        </template>
    </template>


    <template v-if="selectors?.billing_address">
        <template v-for="(value, key) in selectors.billing_address" :key="key">
            <template v-if="value">
                <template v-if="labelSelector.getQuerySelectAll(value)?.length"> 
                    <template v-for="element in labelSelector.getQuerySelectAll(value)" :key="element">
                        <Teleport :to="element">
                            <template v-if="key =='billing_name'">
                                {{ order_details?.first_name }}
                                {{ order_details?.last_name }}
                            </template>
                            <template v-if="key =='billing_mobile'">
                                {{ order_details?.mobile }}
                            </template>
                            <template v-if="key =='billing_address_line_1'">
                                {{ order_details?.address_line1 }}
                            </template>
                            <template v-if="key =='billing_city'">
                                {{ order_details?.city }}
                            </template>
                            <template v-if="key =='billing_state'">
                                {{ order_details?.state_id || order_details?.state }}
                            </template>
                            <template v-if="key =='billing_company'">
                                {{ order_details?.company_name }}
                            </template>
                            <!-- additional info  -->
                            <!-- <template v-if="key =='billing_address_info'">
                                {{ order_details?.id }}
                            </template>  -->
                            <template v-if="key =='signature' && order_signature">
                                <img v-if="order_signature" class="mt-3 border" :src="`${assetURL}orders/${order_signature}`">
                            </template>
                            <template v-if="key =='billing_email'">
                                {{ order_details?.email }}
                            </template>
                            <template v-if="key =='billing_country'">
                                {{ order_details?.country_id }}
                            </template>
                            <template v-if="key =='billing_address_line_2'">
                                {{ order_details?.address_line2 }}
                            </template>
                            <template v-if="key =='billing_zip'">
                                {{ order_details?.zipcode }}
                            </template>
                        </Teleport>
                    </template>
                </template>
            </template>
        </template>
    </template>


    <template v-if="order_details?.custom_values?.length && AdditionalInfoSection && AdditionalFields">
        <teleport :to="AdditionalFields">
            <template v-for="item in order_details?.custom_values">
                <p class="RentMyDestitle" data-rentmyattr="SingleField">
                    <strong>{{ item?.field_label }}: </strong> 
                    <span data-rentmyattr="fieldValue" >{{ item?.field_values }}</span>
                </p>
            </template>
        </teleport>
    </template>


 
    <template v-if="order_details && ShippingInfoSection && ShippingInfoSectionContent">
        <teleport :to="ShippingInfoSectionContent">
             
            <div class="RentMyRow">
                <div class="RentMyHalfwidth">
                    <p class="RentMyDestitle"><strong>Name on Card:</strong> <span data-rentmyattr="shippingFullName" > {{ order_details?.shipping_first_name || '' }} {{ order_details?.shipping_last_name || '' }} </span></p>
                    <p class="RentMyDestitle"><strong>Email:</strong> <span data-rentmyattr="shippingFullName" > {{ order_details?.shipping_email || '' }} </span></p>
                    <p class="RentMyDestitle"><strong>Address Line 1:</strong> <span data-rentmyattr="shippingFullName" > {{ order_details?.shipping_address1 || '' }} </span></p>
                    <p class="RentMyDestitle"><strong>City:</strong> <span data-rentmyattr="shippingFullName" > {{ order_details?.shipping_city || '' }} </span></p>
                    <p class="RentMyDestitle"><strong>Zipcode:</strong> <span data-rentmyattr="shippingFullName" > {{ order_details?.shipping_zipcode || '' }} </span></p>
                </div> 
                <div class="RentMyHalfwidth">
                    <p class="RentMyDestitle"><strong>Phone:</strong> <span data-rentmyattr="shippingFullName" > {{ order_details?.shipping_mobile || '' }} </span></p>
                    <p class="RentMyDestitle"><strong>Address Line 2:</strong> <span data-rentmyattr="shippingFullName" > {{ order_details?.shipping_address2 || '' }} </span></p>
                    <p class="RentMyDestitle"><strong>State:</strong> <span data-rentmyattr="shippingFullName" > {{ order_details?.shipping_state || '' }} </span></p>
                    <p class="RentMyDestitle"><strong>Country:</strong> <span data-rentmyattr="shippingFullName" > {{ order_details?.shipping_country || '' }} </span></p>
                </div> 
            </div>

        </teleport> 
    </template> 
    

    <template>
        <template v-if="addInventorySearch && showSearchBox">
            <Teleport :to="addInventorySearch">
                <ProductSearchForOrderDetails @getProductData="getProductFromSearch" :showBuyRent="true" style="margin-bottom: 15px"></ProductSearchForOrderDetails>
            </Teleport>
        </template>
    </template>

    <template>
        <template v-if="productDetails">
            <Teleport :to="productDetails">
                <div v-if="inventorySearchProduct" class="row">
                    <div class="col-xl-4 col-lg-4 col-md-12">
                        <img :src="getProductImage(inventorySearchProduct)" alt="Product Image" class="img-fluid img-orgin" @error="errorImage" />
                    </div>
                    <div class="col-xl-8 col-lg-8 col-md-12 col-sm-12">
                        <h4 class="client-productname">{{inventorySearchProduct.name}}</h4>
                        <div class="rent">
                            <div v-if="orderType=='rent'" class="row mt-2">

                                <template v-if="afterSearch.variants?.chain_name?.length && afterSearch.variants?.variant_sets">
                                    <div class="m-radio-list col-12 mb-3">
                                        <template v-for="(chainName, key) in afterSearch.variants?.chain_name" :key="key">                                          
                                            <label v-if="chainName" class="w-100 m-radio">
                                                 <strong> {{ chainName }}: </strong>
                                                 {{ Object.entries(afterSearch.variants?.variant_sets)?.[key]?.[1] }}
                                            </label>
                                        </template>
                                    </div>
                                </template>

                                <template v-if="afterSearch.variants?.prices">
                                    <div class="m-radio-list col-12 mb-3">
                                        <template v-for="(_, key) in afterSearch.variants?.prices?.[0]" :key="key">
                                            <template v-if="key !== 'base'">
                                                <label class="w-100 m-radio">
                                                    <input type="radio" name="rent" class="ng-untouched ng-pristine ng-valid" 
                                                    @click="afterSearch.price = afterSearch.variants?.prices?.[0][key][0]"
                                                    :checked="afterSearch.variants?.prices?.[0][key][0].selected"> 
                                                    {{ currency.format(afterSearch.variants?.prices?.[0][key][0].price, currencyConfig) }} 
                                                    / 
                                                    {{ afterSearch.variants?.prices?.[0][key][0].duration }} 
                                                    {{ afterSearch.variants?.prices?.[0][key][0].label }}
                                                    <span></span>
                                                </label>
                                            </template>
                                        </template>
                                    </div>
                                </template>

                                <div class="col-12 quant">
                                    <label class="colorPurpel mb-0">Rental date range:</label>
                                    <DatePicker
                                        @change="onChangeDatepicker"
                                        :singleDatePicker="(confg.show_start_date && !confg.show_end_date) || (!confg.show_start_date && confg.show_end_date)" 
                                        :startDate="order_details?.start_date" 
                                        :endDate="order_details?.end_date" 
                                        :timePicker="(confg.show_start_time || confg.show_end_time)" 
                                        :disabledPicker="!!(order_details?.start_date || order_details?.end_date)" 
                                        mindate="false"
                                        width="300px"
                                        >
                                    </DatePicker>
                                </div>
                               </div>
                            <div class="quant row mt-3">
                                <div class="col-12">
                                    <label class="colorPurpel mb-0">Quantity</label>
                                    <div class="quantity admin-quantity clearfix">
                                        <span @click="updateProductQty('decrease')" class="btn btn-sm btn-dark no-m px-3">-</span>
                                        <input v-model="searchProductQty" type="text" autocomplete="off" name="qty" min="1" class="input-qty text-center" disabled />
                                        <span @click="updateProductQty('increase')" class="btn btn-sm btn-dark no-m px-3">+</span>
                                    </div>
                                </div>
                                
                                <div v-if="inventorySearchProduct?.prices[0]?.base?.price" class="col-12 price pt-4 mt-3">
                                    <h4>
                                        Price: {{ currency.format(calculatePrice * searchProductQty, currencyConfig) }}
                                    </h4>
                                </div>
                                <div v-else-if="inventorySearchProduct?.rental_price" class="col-12 price pt-4 mt-3"><h4>Price: {{currency.format(inventorySearchProduct?.rental_price * searchProductQty,currencyConfig)}}</h4></div>
                            </div>
                            <div class="mt-3 mb-4">
                                <button @click="addItemToOrder(inventorySearchProduct)" class="btn btn-md btn-dark">Add Item</button> &nbsp;&nbsp; 
                                <button @click="inventorySearchProduct = null" class="btn btn-md btn-danger">Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </Teleport>
        </template>
    </template>


    <template v-if="noteTbody">
        <Teleport :to="noteTbody">                             
            <tr v-for="(note, index) in noteList" :key="index">       
                <template v-for="(_key, i) in note_serial" :key="i">
                    <td v-bind="note_attributes?.[note_serial?.[i]]">
                        <div class="note-pragraph">
                            <p>{{note?.[note_serial?.[i]]}}</p>
                            <template v-if="note_serial?.[i] == 'note' && note?.file_type == 'image'">
                                <img @click.stop="actions.noteImagePopup(note)" height="40px" class="img pointer" style="cursor: pointer;" :src="note.file_path" :alt="note.content_type">
                            </template>
                        </div>
                    </td>
                </template>
                <td>
                    <a @click.stop="deleteNote(note)" class="float-left text-danger" style="cursor: pointer;"><i class="fa fa-trash"></i></a>
                </td>
            </tr>
            
        </Teleport>
    </template>

    <template v-if="paymentTbody">
        <Teleport :to="paymentTbody">                             
            <tr v-for="(payment, index) in paymentList" :key="index">       
                <template v-for="(_key, i) in payment_serial" :key="i">
                    <td v-bind="payment_attributes?.[payment_serial?.[i]]">
                        <div class="payment-pragraph">
                            <template v-if="payment_serial?.[i] == 'payment_amount'">
                                <p>{{ currency.format(payment?.[payment_serial?.[i]],currencyConfig) }}</p>
                            </template>
                            <template v-else-if="payment_serial?.[i] == 'status'">
                                <p>{{ getPaymentStatus(payment?.[payment_serial?.[i]]) }}</p>
                            </template>
                            <template v-else>
                                <p>{{payment?.[payment_serial?.[i]]}}</p>
                            </template>
                        </div>
                    </td>
                </template>
                <!-- <td>
                    <a v-if="payment.type == 2" @click.stop="deletePayment(payment,index)" style="cursor: pointer;" class="float-left text-danger"><i class="fa fa-trash"></i></a>
                </td> -->
            </tr>
            
        </Teleport>
    </template>


    <template v-if="inventoryTbody">
        <Teleport :to="inventoryTbody">                             
            <tr v-for="(inventory, index) in inventoryList" :key="index">       
                <template v-for="(_key, i) in inventory_serial" :key="i">
                    <td v-bind="inventory_attributes?.[inventory_serial?.[i]]">
                        <div class="inventory-pragraph">

                            <template v-if="inventory_serial?.[i] == 'product_discription'">

                                {{inventory?.[inventory_serial?.[i]]}}

                                <p v-if="inventory?.['product_discription_variant_chain']">
                                    <small> {{ '('+ inventory?.['product_discription_variant_chain'] + ')'}}</small>
                                </p>
                            </template>

                            <template v-else-if="inventory_serial?.[i] == 'product_images'">
                                <img :src="getProductImage(inventory?.['product'])" @error="errorImage">
                            </template>

                            <template v-else-if="inventory_serial?.[i] == 'quantity_functions'">
                                <div class="d-flex align-items-center">
                                    <button @click.stop="decreaseQty(inventory)" class="btn btn-sm bg-dark text-white rounded-0"><i class="fas fa-minus"></i></button>
                                    <input type="text" disabled :value="inventory?.['quantity']" class="quantity rounded-0 border">
                                    <button @click.stop="increaseQty(inventory)" class="btn btn-sm bg-dark text-white rounded-0"><i class="fas fa-plus"></i></button>
                                </div>
                            </template>

                            <template v-else-if="inventory_serial?.[i] == 'price'">
                                {{ currency.format(inventory?.[inventory_serial?.[i]],currencyConfig) }}
                            </template>

                            <template v-else-if="inventory_serial?.[i] == 'subtotal'">
                                {{ currency.format(inventory?.[inventory_serial?.[i]],currencyConfig) }}
                            </template>

                            <template v-else>
                                {{inventory?.[inventory_serial?.[i]]}}
                            </template>
                        </div>
                    </td>
                </template>
                <td>
                    <a @click="removeOrderItem(inventory)" style="cursor: pointer;" class="float-left text-danger"><i class="fa fa-trash"></i></a>
                </td>
            </tr>
            
        </Teleport>
    </template>

    <template v-if="selectors?.payment">
        <template v-for="(value, key) in selectors.payment" :key="key">
            <template v-if="value">
                <template v-if="labelSelector.getQuerySelectAll(value).length"> 
                    <template v-for="(element, index) in labelSelector.getQuerySelectAll(value)" :key="index">
                        <Teleport :to="element">
                            <template v-if="key =='total_payment_amount'">
                                {{ currency.format(paymentData?.memo?.total,currencyConfig) }}
                            </template>
                            <template v-if="key =='total_paid_amount'">
                                {{ currency.format(paymentData?.memo?.paid,currencyConfig) }}
                            </template>
                            <template v-if="key =='total_due_amount'">
                                {{ currency.format(paymentData?.memo?.due,currencyConfig) }}
                            </template>
                        </Teleport>
                    </template>
                </template>
            </template>
        </template>
    </template>
                           
    <Confirmation v-model="showModal">
        <template v-if="noteImagePopup">
            <div v-html="noteImagePopup"></div>
        </template>
    </Confirmation>
</template>

<style>
</style>