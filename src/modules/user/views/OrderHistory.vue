<script setup>
import { defineProps, computed, inject, defineComponent } from 'vue';
import { useUserStore } from '@modules/user/controllers/user.controller';
import { createComponentHTML } from "@utils/functions/withComponent";
import Confirmation from '@components/modal/Confirmation.vue';
import { Toaster, emitter } from '@/import-hub';
import { getOrderStatus } from '@utils/functions';
import Loader from '@components/Loader.vue';
import Pagination from '@components/Pagination.vue';
let { domElement, currency, labelSelector, helper } = inject('utils');
const setWraperIsReady = inject('setWraperIsReady');
const baseURL = RENTMY_GLOBAL?.env?.API_BASE_URL || import.meta.env.VITE_API_BASE_URL
const assetURL = RENTMY_GLOBAL?.env?.ASSET_URL || import.meta.env.VITE_ASSET_URL


if(!RENTMY_GLOBAL.rentmy_customer_info){
    window.location.replace(RENTMY_GLOBAL?.page?.login);
}


const page = RENTMY_GLOBAL.page;
const { wrapper, popupMode } = defineProps({
    wrapper: {
        default: false,
        required: true,
    },
    popupMode: {
        default: false,
        required: false,
    }
});

let myEmits = defineEmits(['view-order'])
const userStore = useUserStore();
const globalLoader = inject('globalLoader');

const confg = JSON.parse(localStorage.getItem('rentmy_contents'))?.site_specific?.confg;
const customer_portal_activated = confg?.customer?.active;
let customerPortalHTML = inject('customerPortalHTML');
const order_sequence = !!confg?.order?.order_sequence;
const order_number_prefix = confg?.order?.order_number_prefix || '';

labelSelector.autoAttriute(wrapper);

createComponentHTML(wrapper, [
    // Left Sidebar
    {
        selector: "[SideBar]",
        child: [
            {
                selector: "img[ProfileImage]",
                attr: { ':src': 'profileImage' },
            },
            {
                selector: "[customer_name]",
                text: `{{ userStore.customer_profile?.first_name + ' ' + userStore.customer_profile?.last_name }}`,        
            },
        ]    
    },
    {
        selector: '[OrderListTable] tbody',
        template: true,
        attr: { 'v-if': 'orderList?.length'},
        child: [            
            {
                selector: 'tr',
                attr: { 'v-for': '(order, index) in orderList', ':key': 'index'},
                child: [
                    {
                        selector: '[order_id]',
                        text: '{{ order_number_prefix + order?.id }}',
                    },
                    {
                        selector: '[order_address]',
                        text: '{{ order?.address }}',
                    },
                    {
                        selector: '[total_quantity]',
                        text: '{{ order?.total_quantity }}',
                    },
                    {
                        selector: '[total_price]',
                        text: '{{ currency.format(order?.total_price, currencyConfig) }}',
                    },
                    {
                        selector: '[status]',
                        text: '{{ getOrderStatus(order?.status) }}',
                    },
                    /* -------------------------------------------------------------------------- */
                    /*                                With Actions                                */
                    /* -------------------------------------------------------------------------- */

                    // viewDetails Icon
                    {
                        selector: '[ViewDetails]',
                        attr: { 
                            'v-if': 'popupMode',
                            '@click.stop': `$emit('view-order', order?.id);helper.withURL.setQuery({order_id: order?.id}, page?.customer_order_history)`,
                        }
                    },
                    {
                        selector: '[ViewDetails]',
                        attr: { 
                            'v-else': 'popupMode',
                            ':href': `helper.withURL.setQuery({order_id: order?.id}, page?.order_details, true)`,
                        }
                    },
                    {
                        selector: '[DownloadPDF]',
                        attr: { 
                            ':href': `baseURL+ 'pages/pdf?order_id=' + order?.id`,
                        }
                    },
                    // Donwload PDF
                    {
                        selector: '[DeleteOrder]',
                        attr: { 
                            'v-if': `+order?.status != 1`,
                            '@click.stop': `prepareToDelete(order)`,
                        },
                        text: `
                            <span v-if="orderDeleting && order?.id == orderToDelete?.id" class="RrentMyBtnLoader" style="--c:#333;padding:0 2px"></span>
                            <span v-else>__EXISTING_HTML__</span>
                        `
                    },
                ],
            },
        ]
    },
    {
        selector: '[OrderListTable] tbody',
        template: true,
        attr: { 'v-else': ''},
        child: [
            {
                selector: 'tr td',
                skipIcon: true,
                removeSiblings: true,
                attr: { 
                    'colspan': '6', 
                    'style': 'text-align:center'
                },
                text: '{{ !isMounted ? "Loading..." : "No order found" }}'
            }
        ]
    },
    {
        selector: '#RentMyPagination',
        text: `
            <Pagination v-model="paginateData" @jump-now="(page) => getOrderList(page)" />
        `
    }
]);
let template = wrapper.innerHTML;
// console.log(wrapper.outerHTML);
wrapper.innerHTML = '';

let OrderHistory = defineComponent({
    components: {
        Loader,
        Pagination,
        Confirmation,
    },
    template: `
        ${template}
        <teleport to="body">
            <Confirmation v-model="showConfirmationModal" 
            title="Do you want to delete the order?"
            @clicked-yes="deleteOrder" @clicked-no="orderToDelete = null" labelNo="No"></Confirmation>
        </teleport>
    `,
    data(){
        return {
            confg,
            popupMode,
            helper,
            currency,
            order_sequence,
            order_number_prefix,
            userStore,
            page,
            baseURL,
            assetURL,
            paginateData: null,
            isMounted: false,
            showConfirmationModal: false,
            orderList: null,
            currencyConfig: null,
            orderToDelete: null,
            orderDeleting: false,
            orderStatusList: [],
        }
    },
    computed: {
        profileImage: function(){
            const { customer_profile } = userStore;            
            return customer_profile?.image ? `${assetURL}customers/${customer_profile.store_id}/${customer_profile.image}` : RENTMY_GLOBAL?.images?.default_profile_image;
        },
    },
    async mounted(){
        await userStore.getCustomerProfile();
        await this.getOrderList();
        await this.getStatusList();
        await this.getCurrencyConfig();
        domElement.bindLogoutEvent(globalLoader);
        setWraperIsReady(wrapper);
        this.isMounted = true;
        if(!customer_portal_activated){
            wrapper.innerHTML = customerPortalHTML;  
        }
    },
    methods: {
        getOrderStatus,
        getOrderList: async function(page_no=1){ 
            const response = await userStore.getOrderList({page_no})
            if(response.status =='OK') {
                this.paginateData = response.result
                this.orderList = response.result.data
            }
        },
        getStatusList: async function(){ 
            const order_status_response = await userStore.getOrderStatusList();
            if(order_status_response?.status =='OK') {
                if(order_status_response?.result?.data && order_status_response?.result?.data?.length) {
                    order_status_response?.result?.data.map((status, i) => {
                        if (status.label !== 'Paid Other') {
                            this.orderStatusList.push(status);
                        }
                        if (status.child) {
                            status.child.map((child, j) => {
                            const mod_child = {
                                id: child.id,
                                label: child.label,
                                isChild: true
                            };
                            this.orderStatusList.push(mod_child);
                            });
                        }
                    });
                }
            }
        },
        getCurrencyConfig: async function(){ 
            const currency_response = await userStore.getCurrencyConfig();
            if(currency_response?.status =='OK') {
                this.currencyConfig =  currency_response.result.data
            }
        },
        prepareToDelete: async function(order){ 
            this.showConfirmationModal = true;
            this.orderToDelete = order;
        },
        deleteOrder: async function(){ 
            let { id } = this.orderToDelete;
            this.orderDeleting = true;
            let response = await userStore.deleteOrder(id, 1);          
            if(response?.status == 'OK'){
                this.orderList.forEach(order => {
                    if(order.id == id){
                        order.status = "1"
                    }
                })            
                Toaster().success(response?.result?.message)
            }
            this.orderDeleting = false;
        },
    }
});



</script>
<template>

    <teleport :to="wrapper">
        <OrderHistory></OrderHistory>
    </teleport>

</template>