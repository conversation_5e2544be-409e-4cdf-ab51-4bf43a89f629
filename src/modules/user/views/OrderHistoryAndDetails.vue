<script setup>
import { onMounted, ref, inject } from 'vue';
import { Toaster } from '@/import-hub';
import OrderDetailsModal from '@components/modal/OrderDetailsModal.vue';
import OrderHistory from './OrderHistory.vue';
import OrderDetails from './OrderDetails.vue';
const setWraperIsReady = inject('setWraperIsReady');
let { helper, domElement } = inject('utils');

const selectors = {
    teleportTo: '#unique',
    orderDetailsId: '#RentMyCustomerOrderDetails',
    destinationID: '#RentmyOrderDetailsTeleportArea',
}

function teleportOrderDetailsContents(){
    document.querySelector(`${selectors.orderDetailsId} .RentMyLeftSidebarmenu`)?.remove();
    const sourceElement = document.querySelector(`${selectors.orderDetailsId}`);   
    sourceHTML.value =  sourceElement.outerHTML;
    const destinationElement = document.querySelector(selectors.destinationID);
    if(sourceElement && destinationElement){
        destinationElement.appendChild(sourceElement);     
    }
}

let orderId = ref(null)
let sourceHTML = ref(null);
let showModal = ref(false);
let loadComponent = ref(false);

onMounted(() => {
    if(!domElement.userIsAuthenticated()) return;
    teleportOrderDetailsContents();
    setWraperIsReady(null, {auth: true});
    let orderID =  helper.withURL.getQuery('order_id')
    if(orderID){
        setTimeout(() => {
            openModal(orderID)
        }, 1500)
    }
})

function openModal(order_id){
    const destinationElement = document.querySelector(selectors.destinationID);
    destinationElement.innerHTML = sourceHTML.value

    orderId.value=order_id;
    showModal.value=true
}

function closeModal(){
    orderId.value = null;
    helper.withURL.deleteQuery('order_id');
    showModal.value = false;
}

</script>

<template>
    <OrderHistory @view-order="openModal" :popupMode="true"></OrderHistory>
    <OrderDetailsModal v-model="showModal" @close="closeModal()"></OrderDetailsModal>
    <OrderDetails v-if="orderId" :orderId="orderId"></OrderDetails>
</template>