<script setup>
import { ref, defineProps, inject, defineComponent } from 'vue';
import { useUserStore } from '@modules/user/controllers/user.controller'
import { createComponentHTML } from "@utils/functions/withComponent";
import Confirmation from '@components/modal/Confirmation.vue';
import { useGlobalStore } from '@stores/global';
import initAutocomplete from "@/googleMap";
import { Toaster, emitter } from '@/import-hub';
let { domElement, labelSelector, helper } = inject('utils');
const assetURL = RENTMY_GLOBAL?.env?.ASSET_URL || import.meta.env.VITE_ASSET_URL
const setWraperIsReady = inject('setWraperIsReady');
const { wrapper } = defineProps(['wrapper']);
const userStore = useUserStore();
const globalStore = useGlobalStore();

labelSelector.autoAttriute(wrapper);
const globalLoader = inject('globalLoader');
let targetAddress = ref(null);
let showConfirmationModal = ref(false);
let addressDeleting = ref(false);

const site_specific = JSON.parse( localStorage.getItem("rentmy_contents") )?.site_specific;
const customer_portal_activated = site_specific?.confg?.customer?.active;
let customerPortalHTML = inject('customerPortalHTML');  

if(!RENTMY_GLOBAL.rentmy_customer_info){
    window.location.replace(RENTMY_GLOBAL?.page?.login);
}

createComponentHTML(wrapper, [
    // Left Sidebar
    {
        selector: "[SideBar]",
        child: [
            {
                selector: "img[ProfileImage]",
                attr: { ':src': 'profileImage' },
            },
        ]    
    },
    /* -------------------------------------------------------------------------- */
    /*                              Customer Details                              */
    /* -------------------------------------------------------------------------- */
    {
        selector: "#RentmyCustomerDetailsSection",
        attr: { 'v-if': '!profileEditMode', ':class': '{fadeUp: !profileEditMode}' },
        child: [
            {
                selector: "[EditButton]",
                attr: { '@click.stop': 'onClickEditButton' },
            },
            {
                selector: "[customer_name]",
                text: `{{ userStore.customer_profile?.first_name + ' ' + userStore.customer_profile?.last_name }}`,        
            },
            {
                selector: "[customer_email]",
                text: `{{ userStore.customer_profile?.email }}`,        
            },
            {
                selector: "[customer_company_name]",
                text: `{{ userStore.customer_profile?.company }}`,        
            },
            {
                selector: "[customer_phone]",
                text: `{{ userStore.customer_profile?.mobile }}`,        
            },
        ],
    },
    /* -------------------------------------------------------------------------- */
    /*                       Profile Information Update Form                      */
    /* -------------------------------------------------------------------------- */
    {
        selector: "#RentmyCustomerEditForm",
        attr: { 'v-if': 'profileEditMode', '@submit.prevent': 'onSubmitCustomerEditForm', ':class': '{fadeUp: profileEditMode}' },
        child: [
            {
                selector: "[name=first_name]",
                attr: { 
                    'v-model': 'profilePayload.first_name',
                    ':required': '!profilePayload.first_name',
                },
            },
            {
                selector: "[name=last_name]",
                attr: { 
                    'v-model': 'profilePayload.last_name',
                    ':required': '!profilePayload.last_name',
                },
            },
            {
                selector: "[name=email]",
                attr: { 
                    'v-model': 'profilePayload.email',
                    ':required': '!profilePayload.email',
                },
            },
            {
                selector: "[name=mobile]",
                attr: { 
                    'v-model': 'profilePayload.mobile',
                    ':required': 'false',
                },
            },
            {
                selector: "[name=company]",
                attr: { 
                    'v-model': 'profilePayload.company',
                    ':required': 'false',
                },
            },
            {
                selector: "#RentmyCustomerSubmitBtn",
                attr: { '@click.stop.prevent': 'onSubmitCustomerEditForm' },    
                text: '__EXISTING_HTML__ <span v-if="profileUpdating" class="RrentMyBtnLoader"></span>',
            },
            {
                selector: "#RentmyCustomerCancelBtn",
                attr: { '@click.stop.prevent': 'onCancelProfileEditMode' },
            },
        ]        
    },
    /* -------------------------------------------------------------------------- */
    /*                                Address List                                */
    /* -------------------------------------------------------------------------- */
    {
        selector: '#RentmyCustomerAddessList',
        attr: { 'v-if': '!profileEditMode' },
        child: [
            {
                selector: '[AddAddressBtn]',
                attr: { '@click.stop': 'onClickAddAddressButton' },
            },
            {
                selector: '[AddressBody]',
                attr: { 
                    'v-if': '!showAddressForm'
                },
                child: [
                    {
                        selector: '[AddressGroup]',
                        attr: { 
                            'v-for': '(adresses, key) in userStore.address_list_by_group', ":key": 'key',
                            ':class': '{fadeUp: !showAddressForm}',
                        },
                        child: [
                            {
                                selector: '[AddressType]',
                                text: '{{ key }}',                
                            },
                            {
                                selector: '[Address]',
                                template: true,
                                attr: { 
                                    'v-for': '(addr, index) in adresses', ":key": 'index',
                                },
                                child: [
                                    {
                                        selector: '__SELF__',
                                        attr: { 
                                            ':class': '{fadeUp: !showAddressForm}',
                                            ':style': '"--duration:" + ((index + 1) * 300) + "ms"',
                                        },
                                        child: [
                                            {
                                                selector: 'label',
                                                text: '{{ addr?.full_address }}',                
                                            },
                                            {
                                                selector: '[IconTrash]',
                                                attr: { '@click.stop.prevent': 'targetAddress=addr;showConfirmationModal=true' },
                                                text: `
                                                    <span v-if="addressDeleting && targetAddress?.id == addr.id" class="RrentMyBtnLoader" style="--c:#333"></span>
                                                    <span v-else>__EXISTING_HTML__</span>
                                                `,              
                                            },
                                            {
                                                selector: '[IconEdit]',
                                                attr: { '@click': 'prepareAddreEditForm(addr)' },
                                            },
                                        ],          
                                    },
                                ],
                            },
                        ],              
                    },            
                ],
            },
        ],
    },
    /* -------------------------------------------------------------------------- */
    /*                          Address Add & Update Form                         */
    /* -------------------------------------------------------------------------- */
    {
        selector: '#RentmyAddressAddEditForm',
        attr: { 'v-if': '!profileEditMode && showAddressForm', '@submit.prevent': 'onSubmitAddressForm', ':class': '{fadeUp: showAddressForm}' },
        text: '__EXISTING_HTML__ <span v-if="addressUpdating" class="RrentMyBtnLoader"><span>',
        child: [
            {
                selector: '[name=type]',
                attr: { 'v-model': 'addressPayload.type', ':required': 'false' },
            },
            {
                selector: '[name=mobile]',
                attr: { 'v-model': 'addressPayload.mobile', ':required': 'false' },
            },
            {
                selector: '[name=country]',
                attr: { 'v-model': 'addressPayload.country', ':required': 'true' },
                child: [
                    {
                        selector: 'option',
                        template: true,
                        attr: { 'v-for': '(country, i) in countries', ':key': 'i' },
                        child: [
                            {
                                selector: '__SELF__',
                                attr: { ':value': 'country?.code' },
                                text: '{{ country?.name }}'
                            }
                        ],
                    }
                ],
            },
            {
                selector: '[name=address_line1]',
                attr: { 'v-model': 'addressPayload.address_line1', ':required': 'false', 'ref': 'address_line1' },
            },
            {
                selector: '[name=city]',
                attr: { 'v-model': 'addressPayload.city', ':required': 'false' },
            },
            {
                selector: '[name=state]',
                attr: { 'v-model': 'addressPayload.state', ':required': 'false' },
            },
            {
                selector: '[name=zipcode]',
                attr: { 'v-model': 'addressPayload.zipcode', ':required': 'false' },
            },
            {
                selector: '#RentmyAddressAddBtn',
                attr: { 'v-if': 'addressAddMode', '@click.stop.prevent': 'onSubmitAddressForm' }, 
                text: '__EXISTING_HTML__ <span v-if="addressAdding" class="RrentMyBtnLoader"></span>', 
            },
            {
                selector: '#RentmyAddressUpdateBtn',
                attr: { 'v-if': '!addressAddMode', '@click.stop.prevent': 'onSubmitAddressForm' }, 
                text: '__EXISTING_HTML__ <span v-if="addressUpdating" class="RrentMyBtnLoader"></span>', 
            },            
            {
                selector: '#RentmyAddressCancelBtn',
                attr: { '@click.stop.prevent': 'onCancelAddressEditForm' }, 
            },
        ] 
    },
]);
let template = wrapper.innerHTML;
wrapper.innerHTML = '';

let userProfile = defineComponent({
    template,
    data(){
        return {
            emitter,
            userStore,
            targetAddress,
            showConfirmationModal,
            page: window.RENMTY,
            countries: [],
            addressDeleting,
            profileEditMode: false,
            profileUpdating: false,
            addressAdding: false,
            addressUpdating: false,
            showAddressForm: false,
            addressAddMode: false,
            profilePayload: {
                first_name: '',
                last_name: '',
                email: '',
                mobile: '',
                company: '',
            },
            addressPayload: {
                type: null,
                mobile: null,
                country: null,
                address_line1: null,
                city: null,
                state: null,
                zipcode: null,
            },
        }
    },
    async mounted() {
        this.countries = await globalStore.getCountries();
        await userStore.getCustomerProfile();
        await userStore.getCustomerAddress(); 
        domElement.bindLogoutEvent(globalLoader);   
        setWraperIsReady(wrapper);

        if(!customer_portal_activated){
            wrapper.innerHTML = customerPortalHTML;
        }

        emitter.on('auto_fill_address', (address) => {
            this.addressPayload.address_line1 = address.location;
            this.addressPayload.country = address.country_short_name;
            this.addressPayload.city = address.city;
            this.addressPayload.state = address.state;
            this.addressPayload.zipcode = address.zipcode;
        });
    },
    computed: {        
        profileImage: function(){
            const { customer_profile } = userStore;            
            return customer_profile?.image ? `${assetURL}customers/${customer_profile.store_id}/${customer_profile.image}` : RENTMY_GLOBAL?.images?.default_profile_image;
        },
        addresListByGroup: function(){
            return listGroupBy(userStore.address_list);
        },
    },
    methods: {
        onClickAddAddressButton: function(){
            this.addressAddMode = true;
            this.showAddressForm = true;

            this.addressPayload.type =  null;
            this.addressPayload.mobile =  null;
            this.addressPayload.country =  'US';
            this.addressPayload.address_line1 =  null;
            this.addressPayload.city =  null;
            this.addressPayload.state =  null;
            this.addressPayload.zipcode =  null; 

            setTimeout(() => {
                initAutocomplete(this.$refs.address_line1);                
            }, 100);                        
        },
        onClickEditButton: function(){
            this.profileEditMode = true;
            this.profileEditMode = true;
            const { customer_profile } = userStore;
            this.profilePayload.first_name = customer_profile.first_name;            
            this.profilePayload.last_name = customer_profile.last_name;            
            this.profilePayload.email = customer_profile.email;            
            this.profilePayload.mobile = customer_profile.mobile;            
            this.profilePayload.company = customer_profile.company;            
        },
        onSubmitCustomerEditForm: async function(){            
            this.profileEditMode = true;
            let { first_name, last_name, email } = this.profilePayload;
            if(!first_name || !last_name || !email) {
                Toaster().success('Please fulfill required fields')
                return;
            }
            this.profileUpdating = true;
            let updated = await userStore.updateCustomerProfile(this.profilePayload);
            if(updated){
                this.profileEditMode = false;
            }
            this.profileUpdating = false;
            await userStore.getCustomerProfile();
            await userStore.getCustomerAddress(); 
        },
        onCancelProfileEditMode: function(){
            this.profileEditMode = false;
        },
        prepareAddreEditForm: function(address=null){
            if(address) this.targetAddress = address;

            this.addressPayload.type = this.targetAddress?.type;
            this.addressPayload.mobile = this.targetAddress?.mobile;
            this.addressPayload.country = (this.targetAddress?.country || '').toLocaleUpperCase();
            this.addressPayload.address_line1 = this.targetAddress?.address_line1;
            this.addressPayload.city = this.targetAddress?.city;
            this.addressPayload.state = this.targetAddress?.state;
            this.addressPayload.zipcode = this.targetAddress?.zipcode;

            this.showAddressForm = true;

            setTimeout(() => {
                initAutocomplete(this.$refs.address_line1);                
            }, 100);
        },
        onSubmitAddressForm: async function(){
            let { type, country, address_line1, city, state, zipcode } = this.addressPayload;
            if(this.addressAddMode){
                if(!country || !address_line1 || (!city && !state && !zipcode)) return Toaster().warning('Please Enter a valid address');
                this.addressAdding = true;
                await userStore.addAddress(type || 'Other', this.addressPayload)
                await userStore.getCustomerAddress();
                this.addressAdding = false;
                this.addressAddMode = false;
                this.showAddressForm = false;
            } else {
                let { id } = this.targetAddress;
                this.addressUpdating = true;
                await userStore.updateAddress(id, type, this.addressPayload);
                await userStore.getCustomerAddress();
                this.addressUpdating = false;
                this.showAddressForm = false;
            }
        },
        onCancelAddressEditForm: function(){
            this.addressAddMode = false;
            this.showAddressForm = false;
        },
    }
})
</script>

<template>
    <teleport :to="wrapper">
        <userProfile></userProfile>
    </teleport> 


    <Teleport to="body">   
        <Confirmation v-model="showConfirmationModal" 
        title="Do you want delete this address?"
        @clicked-yes="async () => {
            addressDeleting = true;
            await userStore.deleteAddress(targetAddress?.id);
            await userStore.getCustomerAddress();  
            addressDeleting = false;
        }" @clicked-no="false" labelNo="No"></Confirmation>
    </Teleport>
    
</template>