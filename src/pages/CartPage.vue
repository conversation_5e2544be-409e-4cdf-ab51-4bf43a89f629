<script setup>
import Modules from '../modules';
import { ref, inject, onMounted } from 'vue';
const Cart = Modules.cartModule.cart;
let emitter = inject('emitter');

const cartWrappers = ref([]);

function updateVariables(){    
    let elements = document.querySelectorAll('.RentMyCartWrapper:not([mounted])');
    elements.forEach(function(element){
        element.setAttribute('mounted', 'true')
    })
    cartWrappers.value = [...cartWrappers.value, ...elements];    
}

onMounted(()=>{
    updateVariables();
    emitter.on('observed', updateVariables);
})
</script>

<template>

    <template v-if="cartWrappers?.length">
        <template v-for="(wrapper, index) in cartWrappers" :key="index">
            <Cart :wrapper="wrapper" :isInpageWidget="false" ></Cart>
        </template>
    </template>

</template>
