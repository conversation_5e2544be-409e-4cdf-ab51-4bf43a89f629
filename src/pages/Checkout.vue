<script setup>
import Modules from '../modules';
import { ref, inject, onMounted } from 'vue';
const CheckoutComponent = Modules.checkoutModule.CheckoutCompo;
let emitter = inject('emitter');
let checkoutPageWrappers = ref([]);

function updateVariables(){
    let element = document.querySelector('#RentMyCheckoutWrapper:not([mounted])');
    if(element){
        element.setAttribute('mounted', 'true') 
        checkoutPageWrappers.value.push(element);
    }
}
onMounted(()=>{
    updateVariables();
    emitter.on('observed', updateVariables);
})
</script>

<template>    
    <template v-if="checkoutPageWrappers?.length">
        <template v-for="(checkoutPageWrapper, index) in checkoutPageWrappers" :key="index">
            <CheckoutComponent :wrapper="checkoutPageWrapper"></CheckoutComponent>
        </template>
    </template>
</template>
