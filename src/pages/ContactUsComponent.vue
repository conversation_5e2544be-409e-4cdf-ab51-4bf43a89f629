<script setup>
import { defineProps, defineEmits, ref, inject, onMounted, defineComponent, watch, getCurrentInstance } from 'vue';
import { Toaster } from '@/import-hub';
import { createComponentHTML } from "@utils/functions/withComponent";
let { cookie, helper, domElement, http } = inject('utils'); 
let globalLoader = inject('globalLoader');
let { BtnLoader } = inject('components');
const log = console.log


const { wrapper } = defineProps(['wrapper']);
 

let successMessage = RentMyEvent.apply_filters('contactus_success_message', 'Submitted Successfully')

/**
  How to modify
  =============
  RentMyEvent.add_filter('contactus_payload', (payload) => {  
    return {...payload, extra_field: 'Default Value'}
  })
*/
let contactPaylaod = RentMyEvent.apply_filters('contactus_payload', {
    first_name: null,
    last_name: null,
    phone: null, 
    email: null, 
    message: null, 
    answer: null, 
    attachment: [], 
})

/**
  How to modify 'contactus_required_keys'
  =============
  RentMyEvent.add_filter('contactus_required_keys', (fields) => {  
    return {...fields, extra_field: 'Please inster something'}
   })
*/
let requiredKeys = RentMyEvent.apply_filters('contactus_required_keys', { 
    email: 'Email is Requried',  // email: null
    answer: null,
})


/**
  How to modify 'contactus_checkbox_fields'
  =============
 
    RentMyEvent.add_filter('contactus_checkbox_fields', (checkbox_fields_object) => {
        checkbox_fields_object.interested_in = {
            values: ['Wedding Floral Rental', 'Wedding Decor Rental', 'Local Decorating Services', 'Other'],
        }
        return checkbox_fields_object
    })
*/
let radio_and_checkboxed = RentMyEvent.apply_filters('contactus_checkbox_fields',  {})

let cloned__requiredKeys = helper.clone(requiredKeys)

let mappedOption = Object.entries(contactPaylaod).flatMap(([key, value]) => {
    let option = {
        selector: `[${key}]`,
        attr: {
            'v-model': `payload.${key}`, 
        },
    }

    if(key === 'attachment'){
        delete option.attr['v-model']  
        option.attr['v-if'] = `showAttachment`
        option.attr['@change'] = `(event) => {
            payload.attachment = event.target.files[0]
        }`
    }

    if(radio_and_checkboxed?.[key] && radio_and_checkboxed?.[key]?.values?.length){
        const options = radio_and_checkboxed?.[key].values.map(value => {
            let __option = {
                selector: `[${key}][value="${value}"]`,
                attr: {
                    'v-model': `payload.${key}`,
                    'value': value, 
                },
            }
            return __option
        })
        return options
    }

    return option
})
 
 
createComponentHTML(wrapper, [
    ...mappedOption,
    {
        selector: '[CaptchaCodeArea]',
        attr: {
            'v-if': 'captchaImage', 
        },
        child: [
            {
                selector: 'img[CaptchaCode]',
                attr: {
                    ':src': 'captchaImage'
                }
            },
            {
                selector: '[answer]',
                attr: {
                    'v-model': 'payload.answer'
                }
            },
        ]
    },
    
    {
        selector: 'form', 
        attr: {
            '@submit.prevent': 'submitForm'
        }
    },
    {
        selector: '[submitButton]',
        attr: {
            '@click.stop': 'submitForm', 
        },
        text: ` __EXISTING_HTML__ <btn-loader v-if="calling"></btn-loader>`
    },
     
])

let template = wrapper.innerHTML
wrapper.innerHTML = ''


let ContactUsCompo = defineComponent({
    components: {
        BtnLoader
    },
    template,
    data(){
        return { 
            calling: false, 
            showAttachment: true, 
            payload: { 
                ...contactPaylaod 
            },
            captchaImage: null,
            requiredKeys,
        }
    },
    setup(){ 
        domElement.setWraperIsReady(wrapper) 

    },
    mounted(){
         this.getCaptcha()
    }, 
    methods: {
        log, 
        getCaptcha(){
            let params = {}
            if(localStorage.getItem('last_captcha')){
                params.token = localStorage.getItem('last_captcha')
            }

            http.get('/captcha', { params }).then(captcha => { 
                    let image_url = captcha.data.result?.data?.image_url
                    let token = captcha.data.result?.data?.token
                    this.captchaImage = image_url
                    localStorage.setItem('last_captcha', token)
                    
                }).catch(err => {
                    Toaster().success(successMessage)
                    this.clearPayload()
                    this.calling = false
                })
        },
        submitForm(){
            let passed = true
            let { payload } = this

            Object.entries(this.requiredKeys).forEach(([key, message]) => { 
                if(!payload?.[key] && passed){
                    passed = false
                    console.log(payload);
                    Toaster().error(message || `${this.ucfirst(key)} is required`)
                    // return   
                }

            })

            if(!passed) return 

            if(!this.isValidEmail(payload.email)){
                return Toaster().error('Email is not valid')
            } 

            payload.tempPhone = payload.phone
            payload.token = localStorage.getItem('last_captcha')

            this.calling = true
 
            http.post('/contactus', payload, { formData: true }).then(response => { 
                if(response.data.result?.message){
                    Toaster().error(response.data.result?.message)
                    this.calling = false
                    return
                }
                Toaster().success(successMessage)
                this.clearPayload()
                this.calling = false
            }).catch(err => {
                console.log({err}); 
                this.calling = false
            })
        },
        ucfirst: (str) => {
            if(!str) return ''
            str = String(str);
            return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
        },
        isValidEmail(email) {
            const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return regex.test(email); 
        },
        clearPayload: function (str) {
            Object.entries(this.payload).forEach(([key, value]) => {
                if(radio_and_checkboxed?.[key]){
                    this.payload[key] = []
                } else {
                    this.payload[key] = null
                }
            })
            this.showAttachment = false
            setTimeout(() => {
                this.showAttachment = true
            }, 0);
            this.getCaptcha()
        },
    }
})


  

</script>
<template>

    <teleport :to="wrapper">
        <ContactUsCompo></ContactUsCompo>
    </teleport>
     
</template>