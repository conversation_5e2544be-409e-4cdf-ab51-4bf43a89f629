<script setup>
import { ref, inject, onMounted } from 'vue';
import CustomerLogin from '@modules/login-register/views/CustomerLogin.vue'
import CustomerRegistration from '@modules/login-register/views/CustomerRegistration.vue'

let emitter = inject('emitter');
let login_pages = ref([]);
let registration_pages = ref([]);

function updateVariables(){
    
    {
        let elements = document.querySelectorAll('#RentMyCustomerLoginContainer:not([mounted])');
        elements.forEach(function(element){
            element.setAttribute('mounted', 'true')
        })
        login_pages.value = [...login_pages.value, ...elements];
    }

    {
        let elements = document.querySelectorAll('#RentMyCustomerRegistrationContainer:not([mounted])');
        elements.forEach(function(element){
            element.setAttribute('mounted', 'true')
        })
        registration_pages.value = [...registration_pages.value, ...elements]; 
    }    
    
}

onMounted(()=>{
    updateVariables();
    emitter.on('observed', updateVariables);
})

</script>

<template>
    <!-- Login Page -->
    <template v-if="login_pages?.length">
        <template v-for="(login_page, index) in login_pages" :key="index">
            <teleport :to="login_page">
                <CustomerLogin :wrapper="login_page" :forPartner="login_page.classList.contains('PartnerLogin')"></CustomerLogin>
            </teleport>
        </template>
    </template>

    <!-- Registration Page -->
    <template v-if="registration_pages?.length">
         <template v-for="(registration_page, index) in registration_pages" :key="index">
            <teleport :to="registration_page">
                <CustomerRegistration :wrapper="registration_page" :forPartner="registration_page.classList.contains('PartnerRegister')" ></CustomerRegistration>
            </teleport>
        </template>
    </template>
</template>