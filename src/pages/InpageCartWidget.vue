<script setup>
import Modules from '../modules';
import { ref, inject, onMounted, defineProps } from 'vue';
const Cart = Modules.cartModule.cart;

let { wrapper } = defineProps(['wrapper']);

if(!(wrapper.textContent.length > 100)){
    let showBackButton = RENTMY_GLOBAL.useBackButtonFromInpageCartWidget === true
    
    wrapper.innerHTML = `<div InsideContainer> <section id="InpageCartWidgetLauncher" class="closed" InpageCartWidgetLauncher > <div class="branded" id="launcher-icon"> <i class="branded las la-luggage-cart" aria-hidden="true"></i> </div> <div class="Summary"> <div class="DateRange"> <span> <strong DateText >08-22-24 09:00 AM - 08-22-24 09:00 AM</strong> </span> </div> <hr> <div class="SummaryText"> <span><strong TotalQuantity>2 items</strong></span> <span class="TotalAmount"><strong TotalAmount>$7,998.00</strong></span> </div> </div> </section> <section id="Sidebar" Sidebar> <div class="SidebarInner open"> 
        <div class="SideBarHeader"> 
        ${showBackButton ? `<a class="WidgetBackButton" backButton> <i class='bx bx-arrow-back' ></i> </a>` : ``}
        <button class="branded CloseSidebar" SidebarCloseIcon> <i class="fas fa-times"></i> </button> <div class="branded HeaderTitle">My booking</div> <div class="branded" RenatalDateArea> <div class="DateRangeTextAndIcon" ToggleDatePicker> <div class="d-flex" DateRange> <span DateText>08-22-2024, 09:00 AM</span> </div> <i class="las la-calendar cp" ></i> </div> <div class="RentMyDatePicker" id="RentMyDatePicker" DatePicker> <!-- Date picker will show here --> </div> </div> </div> <div class="CartItemsArea active"> <ul class="CartItems"> <li CartItem> <div class="Item"> <div ImageArea> <img class="bq-product-image" src="https://cdn3.booqable.com/uploads/d92c0cd18e0d603924b778ef23c1a309/photo/photo/d4802cc4-4bb5-43f3-ab07-a1baf18054eb/large_photo.jpg"> </div> <div class="CartLine"> <div class="ItemName my-2" ItemNameArea>Two-Stall Lux Restroom Trailer</div> <div class="d-flex justify-content-between align-items-center mt-2"> <div class="QuantityArea" IncrDecrArea> <button class="QuantityToggler" DecrementBtn>-</button> <span class="Quantity" QuantityText>1</span> <button class="QuantityToggler" IncrementBtn>+</button> </div> <strong class="ItemPrice" ItemPrice>$3,999.00</strong> </div> </div> <button class="RemoveProduct" DeleteIconArea><i class="fas fa-times"></i></button> </div> </li> </ul> </div> <div> <div class="SidebarSummery" RentMySummeryTable> <div class="DeviderLine"></div> <div class="Details"> <strong>Subtotal</strong> <strong SubtotalAmount>$7,998.00</strong> </div> <div class="ButtonGroup"> <a class="branded Button" href="#" CartPageUrl>View cart</a> <button class="branded Button" ProceedCheckoutBtn>Checkout</button> </div> </div> </div> </div> </section> </div>`;  
}

</script>

<template>
    <Cart :wrapper="wrapper" :isInpageWidget="true" ></Cart>  
</template>
