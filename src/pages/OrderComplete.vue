<script setup>
import { ref, inject, defineProps, defineComponent } from 'vue';
import Modules from '../modules';
let setWraperIsReady = inject('setWraperIsReady');
let { wrapper } = defineProps(['wrapper']);
import { createComponentHTML } from "@utils/functions/withComponent";
let { helper, cookie, domElement, http, labelSelector, currency } = inject("utils");
let showLoginModal = inject("showLoginModal");
let globalLoader = inject("globalLoader");
let allowDyanmicContent = inject('allowDyanmicContent');
import { useGlobalStore } from "@stores/global";
import { downloadFile } from "@utils/functions";
const globalStore = useGlobalStore();

const site_specific = JSON.parse( localStorage.getItem("rentmy_contents") )?.site_specific;
const filters = {         
        lbl_shipping: RentMyEvent.apply_filters('Checkout:cart:lbl_shipping:label', site_specific.cart.lbl_shipping), 
    }


createComponentHTML(wrapper, RentMyEvent.apply_filters('options:order_complete_page', [
  {
    selector: '[Tabs]',
    attr: { 'v-if': '!showDetails' },
      child: [
        {
        selector: '[PdfImageDiv]',
        attr: { '@click.stop': 'onClickPDF' },
        child: [
          {
              selector: 'img',
              attr: { ':src': 'RENTMY_GLOBAL?.images?.download_pdf_image' },
          },
        ]
    },

    {
        selector: '[CalendarImageDiv]',
        attr: { '@click.stop': 'onClickCalendar' },
        child: [
           {
            selector: 'img',
            attr: { ':src': 'RENTMY_GLOBAL?.images?.download_calendar_image' },
          },
        ]
    },

    {
        selector: '[DetailsImageDiv]',
        attr: { '@click.stop': 'showDetails = true' },
        child: [
           {
            selector: 'img[ImageViewDetails]',
            attr: { ':src': 'RENTMY_GLOBAL?.images?.check_list_image' },
          },
        ]
    },
      ]
  },
  {
    selector: '[OrderSummary] .OrderSummaryTitle',
    text: `{{ allowDyanmicContent ? site_specific?.checkout_info?.title_order_summary : '__EXISTING_HTML__' }}`
  },
  {
    selector: '[OrderSummary]',
    child: [
            {
                selector: "thead th:nth-child(2)",
                text: `{{ allowDyanmicContent ? site_specific?.cart?.th_product : '__EXISTING_HTML__' }}`
            },
            {
                selector: "thead th:nth-child(3)",
                text: `{{ allowDyanmicContent ? site_specific?.cart?.th_unit_price : '__EXISTING_HTML__' }}`
            },
            {
                selector: "thead th:nth-child(4)",
                text: `{{ allowDyanmicContent ? site_specific?.cart?.th_quantity : '__EXISTING_HTML__' }}`
            },
            {
                selector: "thead th:nth-child(5)",
                text: `{{ allowDyanmicContent ? site_specific?.cart?.th_subtotal : '__EXISTING_HTML__' }}`
            },
        ]
  },
  {
    selector: '[OrderSummary]',
    attr: { 'v-if': 'showDetails' },
    child: [

      {
        selector: '[OrderItem]',
        template: true,
        attr: { 
          'v-for': '(item, index) in order?.order_items || []', ':key' : 'index'
         },
        child: [
          {
            selector: '[ImageArea] img',
            attr: { ':src': 'helper.getProductImage(item)', '@error': 'setDefaultImage' },
          },
          {
            selector: '[ItemNameArea]',
            attr: { ':src': 'helper.getProductImage(item)', '@error': 'setDefaultImage' },
            text: `
              <strong class="CartItemTitle">{{ item?.product?.name }}</strong>

              <!--Variant Chain-->
              <template v-if="item?.product?.variant_chain && item?.product?.variant_chain?.indexOf('Unassigned') < 0">
                  <div class="CartItemVariantName"><small>{{ item.product.variant_chain }}</small></div>
              </template>

              <!--custom fields-->
              <template v-if="item?.cart_product_options?.length">
                  <ul class="ProductCustomFields">
                      <template v-for="(fields, index) in item?.cart_product_options" :key="index">
                          <template v-if="fields.options && fields.options.length > 0">
                              <template v-for="(option, key) in fields?.options" :key="key">
                                  <template v-if="option.label && option.value">
                                      <li v-if="['rich', 'Delivery Only'].includes(option.label) == false">
                                          {{ option.label }}: {{ option.value }}{{ key === fields.options.length - 1 ? '' : ', ' }}
                                      </li>
                                  </template>
                              </template>
                          </template>
                      </template>
                  </ul>
              </template>
            `,        
          },
          {
            selector: '[ItemPrice]',
            text: `{{  currency.format(item.price) }}`,        
          },
          {
            selector: '[Quantity]',
            text: `{{  item.quantity }}`,        
          },
          {
            selector: '[ItemPriceArea]',
            text: `{{ currency.format(item?.price * item?.quantity) }} <span v-if="getCouponDiscountText(item)">({{ getCouponDiscountText(item) }} Coupon applied)</span>`,        
          },
        ]
      },


      // OptionalService
      {
         selector: '[OptionalService]',
         child: [
                 {
                     selector: "thead th:nth-child(1)",
                     text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_additional_charge : '__EXISTING_HTML__' }}`
                 }
             ]
      },
      {
        selector: '[OptionalService]',
        attr: { 
          'v-if': 'optionalCharges?.length',
          },
        child: [
          {
            selector: '[Service]',
            attr: { 
              'v-for': '(service, index) in optionalCharges', ':key' : 'index',
              },
            child: [
              {
                selector: '[Name]',
                text: `{{ service?.note || service?.name }}`,        
              },
              {
                selector: '[Price]',
                text: `{{ currency.format(service?.amount) }}`,        
              },
            ]     
          },
        ]
      },

      // Rental Date
      {
        selector: '[RentalDate]',
        text: `
          {{ order?.rent_start }}
          {{ order?.rent_end ? '-' : '' }}
          {{ order?.rent_end }}
        `
      },

      // Order Summary Table
      {
        selector: ".RentMyCartTotal",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_total : '__EXISTING_HTML__' }}`
      },
      {
        selector: '[SummaryTable] [SubtotalLabel]',
        text: `{{ allowDyanmicContent ? site_specific?.cart?.th_subtotal : '__EXISTING_HTML__' }}`
      },
      {
        selector: "[SummaryTable] [TaxesFeesLabel] td:nth-child(1)",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_taxes_and_fees : '__EXISTING_HTML__' }}`
      },
      {
        selector: "[SummaryTable] [OptionalServicesLabel] td:nth-child(1)",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_additional_charge : '__EXISTING_HTML__' }}`
      },
      {
        selector: "[SummaryTable] [DepositeAmountLabel]",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_total_deposite : '__EXISTING_HTML__' }}`
      },
      {
        selector: "[SummaryTable] [DepositeAmountRow]",
        attr: { 
          'v-if': 'site_specific?.cart?.lbl_total_deposite'
         },
      },
      {
        selector: "[SummaryTable] [ TaxName]",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_tax : '__EXISTING_HTML__' }}`
      },
      {
        selector: "[SummaryTable] [ TotalLabel]",
        text: `{{ allowDyanmicContent ? site_specific?.cart?.lbl_total : '__EXISTING_HTML__' }}`
      },
      {
        selector: '[SummaryTable]',
        child: [
          {
            selector: '[SubtotalAmount]',
            text: `{{ currency.format(order?.sub_total || 0) }}`,
          },
          {
               selector: "[OptionalServicesLabel]",
               attr: { 
                       'v-if': '!site_specific?.confg?.checkout?.combine_additional_charges_with_taxes && site_specific?.cart?.lbl_additional_charge && optionalCharges?.length > 0'
                     }
          },
          {
            selector: '[OptionalServices]',
             text: '{{ currency.format( optionalCharges?.reduce((a, b) => a + b?.amount, 0) || 0 ) }}',
          },
          {
               selector: "[TaxesFeesLabel]",
               attr: { 
                       'v-if': 'site_specific?.confg?.checkout?.combine_additional_charges_with_taxes'
                     }
          },
          {
            selector: '[TaxesFees]',
             text: '{{ currency.format(((+order?.tax?.total) || 0) + optionalCharges?.reduce((a, b) => a + b?.amount, 0)) }}',
          },
          {
            selector: '[DepositeAmount]',
             text: '{{ currency.format(order?.total_deposit || 0) }}',
          },

          // TaxAmountRow
          // {
          //   selector: '[TaxAmountRow]',
          //   template: true,
          //   attr: { 'v-if': 'order?.tax?.regular?.length'},
          //   child: [
          //     {
          //       selector: '__SELF__',
          //       attr: { 'v-for': '(tax, index) in order?.tax?.regular', ':key': 'index' },
          //       child: [
          //           {
          //             selector: '[TaxName]',
          //             text: `{{ tax?.name }} ({{ tax?.rate + '%' }})`,
          //           },
          //           {
          //             selector: '[TaxAmount]',
          //             text: `{{ currency.format(tax?.total || 0) }}`,
          //           },
          //       ]
          //     },
          //   ]
          // },
          // {
          //   selector: '[TaxAmountRow]',
          //   template: true,
          //   attr: { 'v-else': ''},
          //   child: [
          //     {
          //       selector: '[TaxName]',
          //       text: 'Tax Amount',
          //     },
          //     {
          //       selector: '[TaxAmount]',
          //       text: '{{ currency.format(order?.tax?.total || 0) }}',
          //     }
          //   ]
          // },
          {
               selector: "[TaxAmountRow]",
               attr: { 
                       'v-if': '!site_specific?.confg?.checkout?.combine_additional_charges_with_taxes && site_specific?.cart?.lbl_tax'
                     }
          },
          {
             selector: "[TaxAmount]",
             text: '{{ currency.format(order?.tax?.total || 0) }}',
          },

          // Shipping Charge
          {
            selector: '[TotalAmount]',
            text: `{{ currency.format(order?.total || 0) }}`,
          },

          // Total
          {
            selector: '[lbl_shipping]',
            text: `${ allowDyanmicContent ? filters.lbl_shipping : '__EXISTING_HTML__'}`,
          },
          {
            selector: '[lbl_shipping_row]',
            attr: { 
              'v-if': 'site_specific?.cart?.lbl_shipping',
             },
          },
          {
            selector: '[ShippingCharge]',
            text: `{{ currency.format(order?.delivery_charge || 0) }}`,
          },
        ],
      },      

      // Footer content and back button

      {
        selector: '[AfterOrderPageFooter]',
        attr: { 
          'v-if': 'site_specific?.checkout_payment?.after_order_page_footer',
          'v-html': 'site_specific?.checkout_payment?.after_order_page_footer'
         },
      },

      {
        selector: '[Back]',
        attr: { '@click.stop': 'showDetails = false' },
      }
    ]
  }
    
]));

let template = wrapper.innerHTML;
wrapper.innerHTML = "";

const OrderDetailsComponent = defineComponent({
  template,
  data() {
    return {
      helper,
      globalStore,
      currency,
      RentMyEvent,
      RENTMY_GLOBAL,
      uid: null,
      site_specific: null,
      isMounted: false,      
      order: null,      
      payment: null,      
      optionalCharges: null,      
      showDetails: false,   
      allowDyanmicContent   
    };
  },
  async mounted() {
    this.site_specific = JSON.parse( localStorage.getItem("rentmy_contents") )?.site_specific;
    const uid = helper.withURL.getQuery('uid') || localStorage.getItem('last_order_uid');
    if(uid){ 
      await this.getOrderDetails(uid);
      if(this.order){
        await this.getOrderPayment(this.order?.id);
        await this.getOrderViewChanges(this.order?.id);
      }
    } else {
      wrapper.innerHTML = `<h1 class="text-center text-danger my-5"> Order UID not found </h1>`;
    }
    this.isMounted = true;
    setWraperIsReady(wrapper);
    RentMyEvent.emit('cdn:checkout_page:mounted', this.order ? true : false);
  },  
  methods: {
    async getOrderDetails(uid){
      try {
        let response = await http.get(`/orders/${uid}/complete`);
        if(response.data.status == 'OK'){
          const data = response.data.result?.data;
          if(Array.isArray(data) && data?.length == 0){
            this.order = null;
          }else{
            this.order = data;
          }
        }
      } catch (error) {
        
      }
    },
    async getOrderPayment(id){
      try {
        let response = await http.get(`/order/${id}/payments?is_paid=true&list=false&log=false&memo=true`);
        if(response.data.status == 'OK'){
          const data = response.data.result?.data;
          this.payment = data;
        }
      } catch (error) {
        
      }
    },
    async getOrderViewChanges(id){
      let response = await http.get(`/orders/view-charges/${id}?type=order`);
      if(response.data.status == 'OK'){
        const data = response.data.result?.data;
        this.optionalCharges = data;
      }
    },
    onClickPDF(){
      const locationId = this.RENTMY_GLOBAL?.locationId;
      const baseURL = RENTMY_GLOBAL?.env?.API_BASE_URL || import.meta.env.VITE_API_BASE_URL; 
      let aTag = document.createElement('a');
      aTag.href = `${baseURL}pages/pdf?order_id=${this.order?.id}&location=${locationId}`;
      document.body.appendChild(aTag);
      globalLoader.show();
      aTag.click();
      aTag.remove();
      setTimeout(() => {
        globalLoader.hide();
      }, 1500)
    },
    onClickCalendar(){
      const uid = helper.withURL.getQuery('uid') || localStorage.getItem('last_order_uid');
      globalLoader.show();
      http.get(`orders/${uid}/ical-download`, { responseType: 'blob' })
      .then(response => {
        let fileBlob = response.data; 
        return fileBlob;       
      })
      .then(fileBlob => {
        downloadFile(fileBlob, this.order.id + '_calendar.ics');
      }).finally(()=>{
        globalLoader.hide();
      })
    },
    // Helper Functions
    setDefaultImage(event){
      event.target.src = RENTMY_GLOBAL?.images?.default_product_image;
    },
    getCouponDiscountText(item){
      if(item.discount?.coupon_amount){
        return currency.format(item.discount?.coupon_amount);
      }else{
        return null;
      }
    },
  },
})

</script>

<template>
    <teleport :to="wrapper">
        <OrderDetailsComponent></OrderDetailsComponent>
    </teleport>
</template>
