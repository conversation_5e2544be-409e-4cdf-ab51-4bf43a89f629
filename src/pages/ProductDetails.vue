<script setup>
import { ref, inject, onMounted, provide } from 'vue';
import Modules from '../modules';
const detailsOfProduct = Modules.ProductDetailsModule.detailsOfProduct;
let { helper } = inject('utils');
let emitter = inject('emitter');
let wrappers = ref([]);

function updateVariables(){
    let elements = document.querySelectorAll('.RentMyWrapperProductDetails:not([mounted])');
    elements.forEach(function(element){
        element.setAttribute('mounted', 'true')
    })
    wrappers.value = [...wrappers.value, ...elements];
}

onMounted(()=>{
    updateVariables();
    emitter.on('observed', updateVariables);
})

</script>

<template>

    <template v-if="wrappers?.length">
        <template v-for="(wrapper, index) in wrappers" :key="index">
            <teleport :to="wrapper">
                <detailsOfProduct :wrapper="wrapper"></detailsOfProduct>
            </teleport>
        </template> 
    </template> 

</template>
