<script setup>
import { defineProps, defineEmits, ref, inject, onMounted, defineComponent, watch, getCurrentInstance } from 'vue';
import { Toaster } from '@/import-hub';
import { createComponentHTML } from "@utils/functions/withComponent";
let { cookie, helper, domElement, http } = inject('utils'); 
let globalLoader = inject('globalLoader');
let { BtnLoader } = inject('components');
const log = console.log


const { wrapper } = defineProps(['wrapper']);
 
 
createComponentHTML(wrapper, [
    
    {
        selector: '[myCheckbox]',
        all: true,
        all: {
            values: ['Car', 'Bike', 'Boat', 'Plane']
        },
        attr: {
            'v-model': 'output', 
        },
    },
    {
        selector: 'a[detailspageLink]',
        all: {
            length: 3
        }, 
        attr: {
            'href': 'https://www.youtube.com/watch?v=xWxsmP48ZmY', 
            'target': '_blank'
        },
    },
     
     
])

let template = wrapper.innerHTML
log('outer HTML::', wrapper.outerHTML)
wrapper.innerHTML = ''


let testCompo = defineComponent({
    components: {
        BtnLoader
    },
    template,
    data(){
        return { 
            output: []
        }
    },
    setup(){ 
        domElement.setWraperIsReady(wrapper) 

    },
    mounted(){
        log('asdfasdf')    
    }, 
    methods: {
        log, 
    }
})


  

</script>
<template>

    <teleport :to="wrapper">
        <testCompo></testCompo>
    </teleport>
     
</template>