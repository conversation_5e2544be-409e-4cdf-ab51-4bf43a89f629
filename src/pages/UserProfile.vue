<script setup>
import { ref, provide, inject, onMounted } from 'vue';
import Modules from '../modules';
import { Toaster } from '@/import-hub'
const OrderHistory = Modules.UserModule.OrderHistory;
const OrderDetails = Modules.UserModule.OrderDetails;
const Profile = Modules.UserModule.Profile;
const ChangePassword = Modules.UserModule.ChangePassword;
const ChangeAvatar = Modules.UserModule.ChangeAvatar;
const OrderHistoryAndDetails = Modules.UserModule.OrderHistoryAndDetails;
let emitter = inject('emitter');
let { helper, cookie } = inject('utils');

let profileContainer = ref(null);
let changePasswordContainer = ref(null);
let changeAvatarContainer = ref(null);
let orderHistoryAndDetailsContainer = ref(null);
let orderHistoryContainer = ref(null);
let orderDetailsContainer = ref(null);
let currentContainer = ref(null)

function updateVariables(){    
    {
        let element = document.querySelector('#RentMyCustomerProfileContainer:not([mounted])');
        if(element){
            element.setAttribute('mounted', 'true') 
            profileContainer.value = element;
        }
    }
    {
        let element = document.querySelector('#RentMyCustomerChangePasswordContainer:not([mounted])');
        if(element){
            element.setAttribute('mounted', 'true') 
            changePasswordContainer.value = element;
        }
    }
    {
        let element = document.querySelector('#RentMyCustomerChangeAvatarContainer:not([mounted])');
        if(element){
            element.setAttribute('mounted', 'true') 
            changeAvatarContainer.value = element;
        }
    }
    {
        let element = document.querySelector('#RentMyCustomerOrderHistoryAndDetails:not([mounted])');
        if(element){
            element.setAttribute('mounted', 'true') 
            orderHistoryAndDetailsContainer.value = element;
        }
    }
    {
        let element = document.querySelector('#RentMyCustomerOrderHistory:not([mounted])');
        if(element){
            element.setAttribute('mounted', 'true') 
            orderHistoryContainer.value = element;
        }
    }
    {
        let element = document.querySelector('#RentMyCustomerOrderDetails:not([mounted])');
        if(element){
            element.setAttribute('mounted', 'true') 
            orderDetailsContainer.value = element;
        }
    }
    currentContainer.value = profileContainer.value
                            || changePasswordContainer.value
                            || changeAvatarContainer.value
                            || orderHistoryAndDetailsContainer.value
                            || orderHistoryContainer.value
                            || orderDetailsContainer.value
}

onMounted(()=>{
    updateVariables();
    emitter.on('observed', updateVariables);
})

</script>

<template>

    <template v-if="profileContainer">
        <Profile :wrapper="currentContainer"></Profile>
    </template>

    <template v-else-if="changePasswordContainer">
        <ChangePassword :wrapper="currentContainer"></ChangePassword>
    </template>

    <template v-else-if="changeAvatarContainer">
        <ChangeAvatar :wrapper="currentContainer"></ChangeAvatar>
    </template>

    <template v-else-if="orderHistoryAndDetailsContainer">
        <OrderHistoryAndDetails :wrapper="currentContainer"></OrderHistoryAndDetails>
    </template>

    <template v-else-if="orderHistoryContainer">
        <OrderHistory :wrapper="currentContainer"></OrderHistory>
    </template>

    <template v-else-if="orderDetailsContainer">
        <OrderDetails :wrapper="currentContainer"></OrderDetails>
    </template>

</template>
