<script setup>
import { defineProps, defineEmits, ref, inject, onMounted, defineComponent, watch } from 'vue';
import { Toaster } from '@/import-hub';
import { createComponentHTML } from "@utils/functions/withComponent";
let { cookie, helper, domElement } = inject('utils'); 
let globalLoader = inject('globalLoader');
const log = console.log

import { useWishlistStore } from "@stores/wishlist";
const wishlistStore = useWishlistStore()

const showLoginModal = inject('showLoginModal') 


let shoppingBagImage = RentMyEvent.apply_filters('Cart:ShoppingBagImage', RENTMY_GLOBAL?.images?.emptybag_image);
let shoppingPageURL = RentMyEvent.apply_filters('Cart:ContinueShoppingUrl', RENTMY_GLOBAL?.page?.products_list);
let isAuthenticatedUser = RENTMY_GLOBAL.rentmy_customer_info

const { wrapper } = defineProps(['wrapper']);


createComponentHTML(wrapper, [
    {
        selector: "[InsideContainer]",
        template: true,
        attr: { 'v-if': 'wishlistStore.listof_wishlist?.wish_list_items?.length || !wishlistStore.list_api_called' },
        child: [
            // For Desktop
            {
                selector: '.WishlistDesktopBtnArea [WishlistSaveBtn]',
                console: true,
                attr: {
                    ':class': `{'GrayScale NoCursor': isAuthenticatedUser}`,
                    '@click.stop': 'onClickSave()',
                }
            },
            {
                selector: '.WishlistDesktopBtnArea [WishlistCopyBtn]',
                attr: {
                    '@click.stop': 'onClickCopy()'
                }
            },

            // For Mobile
            {
                selector: '.WishlistMobileBtnArea [WishlistSaveBtn]',
                attr: {
                    ':class': `{'GrayScale NoCursor': isAuthenticatedUser}`,
                    '@click.stop': 'onClickSave()',
                }
            },
            {
                selector: '.WishlistMobileBtnArea [WishlistCopyBtn]',
                attr: {
                    '@click.stop': 'onClickCopy()'
                }
            },
            


            {
                selector: '[WishlistItem]',
                attr: {
                    'v-for': '(item, i) in wishlistStore.listof_wishlist?.wish_list_items'
                },
                child: [
                    {
                        selector: 'img',
                        attr: {
                            ':src': `getImageUrl(item)`
                        },
                    },
                    {
                        selector: '[WishlistProductName]',
                        text: `{{ item?.product?.name }}`
                    },
                    {
                        selector: '[WishlistDeleteBtn]',
                        attr: {
                            '@click': 'deleteItem(item)'
                        }
                    },
                    {
                        selector: '[ProductPageFullUrl]',
                        attr: {
                            ':href': 'getDetailsPageUrl(item)'
                        }
                    }, 
                    {
                        selector: '[ProductFullUrl]',
                        attr: {
                            ':href': 'getDetailsPageUrl(item)'
                        }
                    }, 
                    {
                        selector: '[QuantytyBox]',
                        child: [
                            {
                                selector: '[WishlistMunisBtn]',
                                attr: { 
                                    ':disabled': 'calling',
                                    '@click.stop': `updateItemQuantity(item, '-')`,
                                }
                            },
                            {
                                selector: '[WishlistPlusBtn]',
                                attr: { 
                                    ':disabled': 'calling',
                                    '@click.stop': `updateItemQuantity(item, '+')`,
                                }
                            },
                            {
                                selector: '[WishlistQuantityCount]',
                                text: `{{ item?.quantity || 0 }}`
                            },
                        ]
                    }, 
                ]

            },
            {
                selector: '[BtnContinueShipping]',
                attr: {
                    '@click.stop': 'goToProductListPage()'
                }
            }, 
            {
                selector: '[BtnMakeQuote]',
                attr: {
                    '@click.stop': 'makeQuote()',
                    '@auxclick.stop': 'makeQuote({new_page: true})',
                }
            },
        ],
    },
    {
        selector: "[InsideContainer]",
        template: true,
        attr: { 'v-else': '' },
        skipIcon: true,
        text: `          
            <div class="text-center fadeIn">
                <img src="${shoppingBagImage}" class="RentMyEmptyBagImage">
                <h4 class="my-4"> Your Wish List is Empty </h4>
                <a class="RentMyBtn ContinueShoppingOnEmptyCart" href="${shoppingPageURL}">Continue Shopping</a>
            </div>
        `,
    },
])

let template = wrapper.innerHTML
wrapper.innerHTML = ''

let WishlistCompo = defineComponent({
    template,
    data(){
        return {
            wishlistStore,
            calling: false,
            isAuthenticatedUser,
        }
    },
    setup(){
        watch(()=>wishlistStore.list_api_called, (a) => { 
            domElement.setWraperIsReady(wrapper)
        })
    },
    mounted(){
        // domElement.setWraperIsReady(wrapper)
    },
    methods: {
        log,
        onClickSave: function(){
            if(isAuthenticatedUser) return
            showLoginModal.value = true 
        },
        onClickCopy: function(){
            helper.copyToClipboard(wishlistStore.generateWishListPageUrl(true))
            Toaster().success('Copied the wish list link')
        },
        getImageUrl: function({ product }){
            let image = product?.images?.[0];
            image = (image?.image_small || image?.image_small_free || image?.image_large || image?.image_large_free);           

            if(image){
                image = helper.generateImage(image, product?.id);
            } else {
                image = RENTMY_GLOBAL?.images?.default_product_image;
            }

            return image
        },
        deleteItem: async function(item){
            this.calling = true
            globalLoader.show()
            await wishlistStore.deleteItem(item.id)
            globalLoader.hide()
            this.calling = false
        },
        updateItemQuantity: async function(item, sign){
            this.calling = true
            globalLoader.show()
            await wishlistStore.updateItemQuantity(item, sign)
            globalLoader.hide()
            this.calling = false
        },
        goToProductListPage: async function(){
            if(RENTMY_GLOBAL?.using_in_cli_project){
                RentMyEvent.emit('cdn:goto:products_list_page', RENTMY_GLOBAL.page.products_list);
            } else {
                window.open(RENTMY_GLOBAL?.page?.products_list, '_self');    
            }
        },
        makeQuote: async function({new_page=false}={}){
            let url = helper.withURL.setQuery({ order_type: 'quote', with: 'wishlist' }, RENTMY_GLOBAL?.page?.checkout, true)
            if(!new_page) window.open(url, '_self');    
            else window.open(url, '_blank');    
        },
        getDetailsPageUrl: function({ product }){
            let detailsPageUrl = product?.type == 2 ? window.RENTMY_GLOBAL.page?.package_details : window.RENTMY_GLOBAL.page?.product_details;     
            let productDetailsPageFullUrl = helper.withURL.generateURL(detailsPageUrl, product); 
            return productDetailsPageFullUrl
        },
    }
})


  

</script>
<template>

    <teleport :to="wrapper">
        <WishlistCompo></WishlistCompo>
    </teleport>
     
</template>