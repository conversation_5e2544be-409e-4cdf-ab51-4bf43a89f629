export const usingPages = (keys = []) => {
    let usingPages = [];
    keys?.forEach(key => {
        let pages = RENTMY_GLOBAL?.page;
        if(pages && (typeof pages == 'object')){
            if (pages[key]) {
                let page = {}
                page.url = pages[key]
                page.name = 'not-found-ccc', // pages[key]?.split(window.top.location.origin)?.[1]
                usingPages.push(page)
            }
        }
    })
    return usingPages;
}