import { usingPages } from './_fn';

export default [
    {
        title: 'Checkout Events',
        usingInFile: 'src/modules/checkout/views/_BillingGeneralInfoHTML.js',
        events: [
            {
                hook_type: 'filter',
                name: 'Checkout:AdditionalInformation:Title',
                param: 'title',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:SpecialComments:Label',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:SpecialRequest:Label',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                sub_group: true,
                name: 'Checkout Labels',                
            },
            {
                hook_type: 'filter',
                name: 'Checkout:DrivingLicence:Label',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:step_one',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:step_two',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:step_three',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:step_four',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:title',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:page_breadcrumb',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:title_contact',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:btn_back_to_cart',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:btn_back',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_first_name',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_lastname',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_mobile',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_email',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:title_billing',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_address_line_1',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_address_line_2',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_company_name',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_city',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_state',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_zipcode',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_country',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_special_comments',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_special_request',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:title_shipping',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:title_pickup_option',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:title_delivery_option',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:title_shipping_option',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_shipping_name',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_shipping_first_name',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_shipping_last_name',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_shipping_mobile',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_shipping_address_line_1',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_shipping_address_line_2',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_shipping_city',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_shipping_zipcode',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_same_as_billing',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:title_additional',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:title_custom_checkout',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_driving_license',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_alt_mobile',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_select_shipping',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_select_location',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_signature',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_required',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_clear',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_undo',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:terms_and_condition',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:terms_and_condition_link_label',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:btn_continue',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:btn_get_delivery_cost',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:btn_get_shipping_method',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:title_order_summary',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:title_select_shipping_method',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:btn_quote_accept',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_welcome_to_login',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_delivery_cost',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_google_pay_success',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_checkout_text',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_global_checkout_error_msg',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_customer_name',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_billing_address',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_status',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            {
                hook_type: 'filter',
                name: 'Checkout:Billing:label:lbl_address',
                param: 'label',
                return_type: ':string',
                using_pages: usingPages(['checkout']),
                description: '',
            },
            
        ],
    },

];

