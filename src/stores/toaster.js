import { defineStore } from 'pinia'
import { reactive, computed } from 'vue'

export const useToaster = defineStore('toaster', () => {

  const state = reactive({
    messages: [
      // {type: 'success', message: 'Hello world'},
      // {type: 'warning', message: 'Hello world'},
      // {type: 'error', message: 'Hello world'},
    ],
  })

  let timeout = null

  function addToasterMessage(message, type='success', time=4000){
    if(RENTMY_GLOBAL?.using_in_cli_project){
      RentMyEvent.emit('cdn:toaster', {message, type, time})
      return;
    }
    if(message && message?.length){
      state.messages.unshift({type, message})
      state.messages.length = 1
      clearTimeout(timeout)
      timeout = setTimeout(() => {
        let index = state.messages?.findIndex(msg => msg.message == message.message)
        try {
          state.messages?.splice(index, 1)
        } catch (error) {}
      }, time);
    }
  }

  function success(message, time=4000){
    addToasterMessage(message, 'success', time)
  }

  function warning(message, time=4000){
    addToasterMessage(message, 'warning', time)
  }

  function error(message, time=4000){
    addToasterMessage(message, 'error', time)
  }

  function remove(message){
    let index = state.messages?.findIndex(msg => msg.message == message.message)
    state.messages?.splice(index, 1)
  }
  
  return {
    ...state,
    success,
    warning,
    error,
    remove,
  }
})