import { defineStore } from 'pinia'
import { ref, computed, onMounted } from 'vue'
import http from '@utils/http';
import helper from '../utilities/helper/index'
import { Toaster, emitter } from '@/import-hub';


// localstoreage key = user_wishlist
// localstoreage key = wishlist_token

export const useWishlistStore = defineStore('wishlist', () => {


  let wishlist_token = ref(null)
  /**
   * Data Sample of listof_wishlist
   * {
    "id": 79,
    "token": "1747287306392",
    "store_id": 982,
    "location": 1048,
    "wish_list_items": [
        ...{}
    ]
  }
   */
  let listof_wishlist = ref(null)
  let list_api_called = ref(false)


  onMounted(async () => {
    // let has_location_in_query = helper.withURL.hasQuery('location')
   
    
    if(helper.withURL.getQuery('token')){
      let existing_token = localStorage.getItem('wishlist_token') || ''
      localStorage.setItem('wishlist_token', helper.withURL.getQuery('token') || existing_token)
    } 
    wishlist_token.value = localStorage.getItem('wishlist_token') || null
    getList()
  })

  

  async function addToList({ product=null, productDetails=null, rental_type=null }={}) {  
    try {

      product = product || productDetails

      let variant_id = product?.default_variant?.variants_products_id

      let prices = helper.formatBuyRent(product.prices)
      let isBuyType = prices && Object.keys(prices).length && prices?.buy?.type
 

      let payload = {
        quantity: 1,
        product_type: product.type,
        product_id: product.id,
        variants_products_id: variant_id,
        rental_type: rental_type || (isBuyType ? 'buy' : 'rent')
      }
     

      const response = await http.post('/wishlist', { items: [payload], token: wishlist_token.value });
      if(response.data?.status == 'OK'){
        let { message, data } = response.data.result || {}; 
        let { token } = data
        Toaster().success(message)
        wishlist_token.value = token
        listof_wishlist.value = data
        localStorage.setItem('wishlist_token', token)
        localStorage.setItem('user_wishlist', JSON.stringify(data))
      } else {
        localStorage.removeItem('wishlist_token')
        localStorage.removeItem('user_wishlist')
        wishlist_token.value = null
        listof_wishlist.value = null
        if(response.data.result?.message){
          Toaster().error(response.data.result?.message)
        }
      }
     
    } catch (error) {
      console.warn('addToList:error', error);
    } 
  }

  async function getList() {  
    try { 
      if(!wishlist_token.value){
        list_api_called.value = true
        return 
      } 
      const response = await http.get(`/wishlist/${wishlist_token.value}`);
      if(response.data?.status == 'OK'){  
        let data = response.data.result.data 
        listof_wishlist.value = data
        localStorage.setItem('user_wishlist', JSON.stringify(data))
      } else {
        localStorage.removeItem('wishlist_token')
        localStorage.removeItem('user_wishlist')
        wishlist_token.value = null
        listof_wishlist.value = null
      }
      list_api_called.value = true
     
    } catch (error) {
      console.warn('addToList:error', error);
    }
  }
  
  async function deleteItem(id) {  
    try {
 
      const response = await http.delete(`/wishlist-item/${id}`);
      if(response.data?.status == 'OK'){  
        let { message } = response.data.result 
        Toaster().success(message)
        await getList()
      }
     
    } catch (error) {
      console.warn('addToList:error', error);
    }
  }

  async function updateItemQuantity(item, sign='+') {  
    try {

      let payload = {
        [ sign == '+' ? 'increment' : 'decrement' ]: true
      } 
 
      const response = await http.put(`wishlist-item/${item.id}/update`, payload);
      if(response.data?.status == 'OK'){  
        let { message } = response.data.result 
        Toaster().success(message)
        await getList()
      }
     
    } catch (error) {
      console.warn('addToList:error', error);
    }
  }


  function generateWishListPageUrl(withLocation=true){
    if(RENTMY_GLOBAL.page.wish_list){
      let wishlistPageUrl = RENTMY_GLOBAL?.page?.wish_list || ''
      if(wishlistPageUrl.includes('{token}')){
        wishlistPageUrl = wishlistPageUrl.replace(/\{token\}/g, wishlist_token.value)
      } 
      if(withLocation){
        const location = localStorage.getItem('current_location_id')
        wishlistPageUrl = helper.withURL.setQuery({ location, token: wishlist_token.value }, wishlistPageUrl, true)
      } else {
        wishlistPageUrl = helper.withURL.deleteQuery('location', wishlistPageUrl)

      }
      return wishlistPageUrl
    }
    return ''
  }

  function goToWishListPage(){
    if(RENTMY_GLOBAL?.using_in_cli_project){

    } else {
      if(RENTMY_GLOBAL.page.wish_list){
        let wishlistPageUrl = generateWishListPageUrl()  
        wishlistPageUrl = helper.withURL.deleteQuery('token', wishlistPageUrl, true)
        wishlistPageUrl = helper.withURL.deleteQuery('location', wishlistPageUrl, true)
        window.open(wishlistPageUrl, '_self')
      }
    } 
  }
  
  return {
    /**variables */
    wishlist_token, 
    listof_wishlist, 
    list_api_called, 

    /**Functions */
    addToList,
    deleteItem,
    updateItemQuantity,
    goToWishListPage,
    generateWishListPageUrl,
     
  }
})