import http from "../http";
const assetURL = RENTMY_GLOBAL?.env?.ASSET_URL || import.meta.env.VITE_ASSET_URL;

const helpers = {
  clone: function (data, { remove = [], add = {}, only = [], nullToEmptyStr = false } = {}) {
    data = JSON.parse(JSON.stringify(data));

    if (typeof data === "object" && Array.isArray(data) === false) {
      // Delete keys
      if (remove?.length && Array.isArray(remove)) {
        remove.forEach((key) => {
          if (data.hasOwnProperty(key)) {
            delete data[key];
          }
        });
      }

      // keep only
      if (only?.length && Array.isArray(only)) {
        const new_data = {};
        only.forEach((key) => {
          if (data.hasOwnProperty(key)) {
            new_data[key] = data[key];
          }
        });
        data = new_data;
      }

      // applyAll
      if (nullToEmptyStr) {
        Object.keys(data).forEach(key => {
          if(data[key] == null){
            data[key] = "";
          }
        })
      }

      return { ...data, ...add };
    } else {
      return data;
    }
  },
  domParser: function (html_content = "") {
    if (!html_content) return;
    let parser = new DOMParser();
    let dom = parser.parseFromString(html_content, "text/html");
    return dom;
  },
  generateImage: function (filename, id = null, _for = "product") {
    if (_for == "product") {
      var product_id = id;
      return `${assetURL}products/${window.RENTMY_GLOBAL?.store_id}/${product_id}/${filename}`;
    } else if (_for == "customer") {
      return `${assetURL}customers/${window.RENTMY_GLOBAL?.store_id}/${customer_profile.image}`;
    }
  },
  getProductImage: function (cart_item, size = "small") {
    if (!cart_item) return;
    let { product } = cart_item;
    if(!product) product = cart_item;
    let image = product?.images?.[0];
    if (size == "small") {
      image = image?.image_small || image?.image_small_free;
    } else {
      image = image?.image_large || image?.image_large_free;
    }
    if (image) {
      image = helpers.generateImage(image, product?.id);
    } else {
      image = RENTMY_GLOBAL?.images?.default_product_image;
    }
    return image;
  },
  createFormData: function (data = {}) {
    const formData = new FormData();
    try {
      for (const key in data) {
        if (Object.hasOwnProperty.call(data, key)) {
          const value = data[key];
          formData.append(key, value);
        }
      }
      return formData;
    } catch (error) {
      return new FormData();
    }
  },
  formatDate: function (date, format = "YYYY-MM-DD") {
    if (date) return moment(date).format(format);
  },
  formatBuyRent: function (prices) {
    try {
      let data = prices;
      if (data.length > 0) {
        var prices = data[0];
        var obj = {
          buy: { type: false, price: 0, id: null },
          rent: { type: false, price: [] },
        };
        var rent = ["hourly", "daily", "weekly", "monthly"];
        if (prices.base.price > 0) {
          obj.buy["type"] = true;
          obj.buy["price"] = prices.base.price;
          obj.buy["id"] = prices.base.id;
        }
        let ren = [];
        const rentPrices = data[0];

        if (rentPrices.fixed) {
          const fp = {
            type: "",
            price: rentPrices.fixed.price,
            id: rentPrices.fixed.id,
            label: "",
            rent_start: rentPrices.fixed.rent_start,
            rent_end: rentPrices.fixed.rent_end,
          };
          obj.rent["price"].push(fp);
        } else {
          for (let c in rentPrices) {
            for (let i = 0; i < rentPrices[c].length; i++) {
              rentPrices[c][i]["type"] = rentPrices[c][i].label;
              obj.rent["price"].push(rentPrices[c][i]);
            }
          }
        }
        if (obj.rent["price"].length > 0) obj.rent["type"] = true;
        return obj;
      }
      return prices;
    } catch (error) {}
  },
  isLoginPage: function () {
    return window?.RENTMY_GLOBAL?.is_login_page;
  },
  redirect: function (pageKey = "home_url") {
    let redirect_url = pageKey == "home_url" ? window?.RENTMY_GLOBAL.home_url : window?.RENTMY_GLOBAL?.page?.[pageKey];
    redirect_url = this.withURL.setQuery({}, redirect_url, true);
    window.open(redirect_url, '_self')
  },
  productSearchRedirect: function (product) {
    let { page } = window?.RENTMY_GLOBAL;
    let full_url = '';
    if (typeof product == "string") {
      full_url = helpers.withURL.setQuery(
        { search: product },
        page.products_list,
        true
      );

    } else if (product?.type == 1) {
      full_url = helpers.withURL.setQuery(
        { uid: product?.uuid },
        page.product_details,
        true
      );
    } else if (product?.type == 2) {
      full_url = helpers.withURL.setQuery(
        { uid: product?.uuid },
        page.package_details,
        true
      );
    }
    if(full_url){
      window.open(full_url, '_self');      
    }
  },
  changeStoreLocation: function (locationObject) {
    let payload = {
      action: "rentmy_cdn_request",
      method: "update_location_id",
      location_id: locationObject.id,
    };
    if(RENTMY_GLOBAL.ajax_url){
      http.post(RENTMY_GLOBAL.ajax_url, payload).then((response) => {
        if (response?.data?.success) {
          localStorage.setItem(
            "current_location",
            JSON.stringify(locationObject)
          );
          windowLocation().reload();
        }
      });
    }
  },
  listGroupBy: function (array, property) {
    if (!array?.length || !property) return {};
    return array.reduce((result, obj) => {
      const key = obj[property];
      if (!result[key]) {
        result[key] = [];
      }
      result[key].push(obj);
      return result;
    }, {});
  },
  copyToClipboard: function(text='', {el=null}={}) {
    if(text){
      const textarea = document.createElement("textarea");
      textarea.value = text;
      textarea.style.position = "fixed";
      textarea.style.left = "-9999px";
      document.body.appendChild(textarea);
      textarea.select();
      try {
          document.execCommand("copy");
      } catch (err) {
          console.error("Failed to copy text", err);
      }
      document.body.removeChild(textarea);
      if(el && el instanceof HTMLElement){
        el.setAttribute('tooltip', 'copied');
        setTimeout(() => {
          el.setAttribute('tooltip', 'copy');          
        }, 1000);
      }
    }
  },
  /* -------------------------------------------------------------------------- */
  /*                           Device Identify with JS                          */
  /* -------------------------------------------------------------------------- */
  /**
   * How to call this device function
   * Examples:
   * device().type;
   * device().isMobile();
   * device().is('mobile');
   * device().in(['mobile', 'tablet']);
   */
  device: function () {
    const getType = () => {
      if (
        /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
          navigator.userAgent
        )
      ) {
        return "mobile";
      } else if (/iPad/i.test(navigator.userAgent)) {
        return "tablet";
      } else {
        return "desktop";
      }
    };

    function DeviceType() {
      this.type = getType();
    }

    DeviceType.prototype.is = function (deviceType = "") {
      return deviceType ? this.type === deviceType : false;
    };

    DeviceType.prototype.in = function (deviceTypes = []) {
      if (deviceTypes && Array.isArray(deviceTypes) && deviceTypes?.length) {
        return deviceTypes.includes(this.type);
      } else {
        return false;
      }
    };

    DeviceType.prototype.isMobile = function (extra = null) {
      return this.type === "mobile";
    };

    DeviceType.prototype.isTable = function () {
      return this.type === "tablet";
    };

    DeviceType.prototype.isDesktop = function () {
      return this.type === "desktop";
    };

    return new DeviceType();
  },
  /* -------------------------------------------------------------------------- */
  /*                                  With URL                                  */
  /* -------------------------------------------------------------------------- */
  withURL: {
    setQuery: function (params, url = window.location.href, _return = false) {
      try {
        if (!url) url = window.location.href;
        url = url.replace(/\/$/, "").replace(/#$/, "");
        const currentUrl = new URL(url, window.location.origin);
        Object.entries(params).forEach(([key, value]) => {
          currentUrl.searchParams.set(key, value);
        });
        if (_return) {
          return currentUrl.href;
        } else {
          // only push relative path
          const relative = currentUrl.pathname + currentUrl.search + currentUrl.hash;
          window.history.pushState({}, "", relative);
        }
      } catch (error) {
        console.warn('setQuery', error);
        return '';
      }
    },
    getQuery: function (param = "", url = window.location.href) {
      try {
        const currentUrl = new URL(url, window.location.origin);
        if (param) {
          return currentUrl.searchParams.get(param) || '';
        } else {
          const params = {};
          currentUrl.searchParams.forEach((value, key) => {
            params[key] = value;
          });
          return params;
        }
      } catch (error) {
        console.warn('getQuery', error);
        return param ? undefined : {};
      }
    },
    deleteQuery: function (param, url = window.location.href, _return = false) {
      try {
        url = url.replace(/\/$/, "").replace(/#$/, "");
        const currentUrl = new URL(url, window.location.origin);
        currentUrl.searchParams.delete(param);
        if (_return) {
          return currentUrl.href;
        } else {
          const relative = currentUrl.pathname + currentUrl.search + currentUrl.hash;
          window.history.pushState({}, "", relative);
        }
      } catch (error) {
        console.warn('deleteQuery', error);
        return '';
      }
    },
    hasQuery: function (param = "", url = window.location.href) {
      try {
        const urlParams = new URLSearchParams(new URL(url).search);
        const paramObj = {};
        for (var value of urlParams.keys()) {
          paramObj[value] = urlParams.get(value);
        }
        return param in paramObj;
      } catch (error) {
        console.warn('hasQuery', error);
      }
    },
    /**
     * 
     * @param url = 'localhost:3000/product/{url}?uid={uuid}'
     * @param productObj = pass object, that contain placeholder keys or url
     * @returns 'localhost:3000/product/product-url?uid=3D2A4563D2A4563D2A456'
     */
    generateURL: function(url, productObj={}) { 
      let regex = /\{([^}]+)\}/g;
      let matches = [];
      let match;       
      
      while ((match = regex.exec(url)) !== null) {
        matches.push(match[1]);
      }
      if(matches.length){
        matches.forEach(key => {
          url = url.replace(`{${key}}`, productObj?.[key] || 'undefined')
        })
      }
      return url;      
    },  
    parseUIDfromUrl: function(page_key="product_details", RentMyParams={}) {        


        let { getQuery } = helpers.withURL

        let by_slug = RENTMY_GLOBAL.product_pacakge_by_slug
      
        let params = {
            view_type: by_slug ? 'slug' : '',
            key: by_slug ? 'url' : 'uid',
            value: getQuery(by_slug ? 'url' : 'uid'),
        }  

        // if(RentmyParamsObj)
        const uid_ = RentMyParams?.['uid']; 
        const product_id = RentMyParams?.['product_id']; 
        const url_ = RentMyParams?.['url']; 
        if(uid_){
          params.key = 'uid';
          params.value = uid_ ;
          params.view_type = '';
          return params;
        }
        else if (product_id){
          params.key = 'id';
          params.value = product_id ;
          params.view_type = 'id';
          return params;
        }
        else if (url_){
          params.key = 'url';
          params.value = url_ ;
          params.view_type = 'slug';
          return params;
        }
        
        let parts = window.location.pathname.split('/').filter(Boolean)     
        let pagePath = RENTMY_GLOBAL.page[page_key].split('?')[0].replace(/\/$/g, '')
    
        if(pagePath.endsWith('{url}')){
            params.value = parts.at(-1)
        }
        if(pagePath.endsWith('{url}/{uuid}')){
            params.value = parts.at(-2)
        }
        return params;
    
    },  
    parseCategoryUIDfromUrl: function(RentMyParams={}) {        


        let { getQuery } = helpers.withURL
        let { href: fullUrl, pathname } = window.location
        pathname = pathname.replace(/\/$/, '')

        let { products_list_by_category } = RENTMY_GLOBAL.page

        let uuid = null

        if(products_list_by_category){ 

          let parts = pathname.split('/').filter(Boolean)   

          if(products_list_by_category.endsWith('{uuid}/{url}')){
              uuid = parts.at(-2) 
          }
          else if(products_list_by_category.endsWith('{uuid}')){
            uuid = parts.at(-2) 
          }  
          else if(getQuery('uid', fullUrl) || getQuery('uuid', fullUrl)){
            uuid = getQuery('uid', fullUrl) || getQuery('uuid', fullUrl)
          }  
        }
        return uuid
    },  
    /**
     * Example
     * let dynamic_url_pattern = 'http://localhost:8080/category/{uuid}/{url}'
     * let current_url_path = '/category/13f56931127611edba81023ae3a002ea/shows'
     * create a function to match, current url path is matched with dynamic_url_pattern or not
     */
    matchDynamicUrl: function(pattern, path, uuid) {
      const regexStr = pattern
        .replace(/^https?:\/\/[^/]+/, '')       // Remove domain part
        .replace('{uuid}', uuid)                  // Insert actual uuid
        .replace(/\{[^}]+\}/g, '([^/]+)');       // Replace {param} with regex group
      const regex = new RegExp(`^${regexStr}$`);
      return regex.test(path);
    },

    hasCategoryUIDInUrl: function(uuid) {        

      let { getQuery, matchDynamicUrl } = helpers.withURL
      
      
      let { href: fullUrl, pathname } = window.location
      pathname = pathname.replace(/\/$/, '')
      
      let { products_list_by_category } = RENTMY_GLOBAL.page
      let result = false

      if(getQuery('category') && getQuery('category') == uuid){
        result = true
      } else if (products_list_by_category){
        result = matchDynamicUrl(products_list_by_category, pathname, uuid) 
      }

      return result 
    },  
    encodeString: function (data='') {
      try {
        return btoa(unescape(encodeURIComponent(data)));
      } catch (error) {
        return data;
      }
    },
    decodeString: function (data='') {
      try {
        return decodeURIComponent(escape(atob(data)))
      } catch (error) {
        return data;
      }
    },
    decodeString: function (data) {
      try {
        return decodeURIComponent(escape(atob(data)));
      } catch (error) {
        return data;
      }
    },
    urlEncoded: function (url) {
      return encodeURIComponent(url);
    },
  },
  backPreviousPage: function(){
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = RENTMY_GLOBAL.page.products_list 
    }
  },
  toggleLoopItem: function (data, index, key = "isShow") {
    if (!data) return;
    data?.forEach((item, i) => {
      if (i == index) {
        item[key] = !(item[key] ?? false);
      } else {
        item[key] = false;
      }
    });
  },
  /* -------------------------------------------------------------------------- */
  /*                                With Product                                */
  /* -------------------------------------------------------------------------- */
  withProduct: {
    allPriceTypes: ["rent", "hourly", "daily", "weekly", "monthly", "fixed"],
    getRentalTypes: function (prices) {
      const {
        withProduct: { allPriceTypes },
      } = helpers;
      let all_prices = [];
      prices?.forEach((prices) => {
        let priceKeys = Object.keys(prices);
        if (priceKeys?.length) {
          all_prices = [
            ...all_prices,
            ...priceKeys.map((key) =>
              allPriceTypes.includes(key) ? "rent" : key
            ),
          ];
        }
      });
      return Array.from(new Set(all_prices));
    },
    getPrices: function (prices) {
      let formatPrice = { rent: [] };
      prices?.forEach((prices) => {
        let priceKeys = Object.keys(prices);
        priceKeys?.forEach((key) => {
          let value = prices[key];
          if (key == "base") {
            formatPrice.base = value;
          } else if (key == "fixed") {
            if (value) formatPrice.rent.push(value);
          } else {
            Object.keys(value)?.forEach((i) => {
              if (value[i]) formatPrice.rent.push(value[i]);
            });
          }
        });
      });
      return formatPrice;
    },
    calculatePromotionalPercent: function (price, regular_price) {
      let percent = 0;
      let reduced = regular_price - price;
      percent = Math.ceil((reduced / regular_price) * 100);
      return percent + "%";
    },
  },
};
globalThis.cdnHelper = helpers
export default helpers;
