<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Document</title>
        <link rel="stylesheet" href="bootstrap.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.9.0/css/all.min.css"
            integrity="sha512-q3eWabyZPc1XTCmF+8/LuE1ozpg5xxn7iO89yfSOd5/oKvyqLngoNGsx8jq92Y8eXJ/IRxQbEC+FGSYxtk2oiw=="
            crossorigin="anonymous" referrerpolicy="no-referrer" />
        <link rel="stylesheet" href="style.css">
    </head>

    <body class="container">
        <div id="RentMyEventManagement" class="RentMyWrapper">
            <div class="app">
                <nav class="nav">
                    <a href="" class="nav__item active" All>All</a>
                    <a href="" class="nav__item" Active>Filter Hook</a>
                    <a href="" class="nav__item" Inactive>Action Hook</a>
                </nav>
                <h3 class="text-center mb-3 hook-title">Filter Hooks</h3>
                <div class="add">
                    <input placeholder="Search event" type="search" class="add__input">
                </div>
                <div class="list">
                    <div SingleEvent>                    
                        <h1 class="group-title text-center">Cart Page</h1>              
                    </div>                    
                    <div SingleEvent>
                        <div class="item">
                            <label class="item__checkbox">
                                <input type="checkbox" Checkbox>
                                <i class="fas fa-check"></i>
                            </label>

                            <span EventName>ResetPassword:SendEmailSuccessMsg</span>

                            <div class="d-flex justify-content-center align-items-center row-icons">
                                <button EditBtn>Edit</button>
                            </div>
                        </div>
                        <div class="callback_area expand">
                            <div class="description mb-4" Description>
                                THis is event description
                            </div>
                            <div class="usingPages mb-4">
                                <li><a href="/rentmy-event-management-2/">/rentmy-event-management-2/</a></li>
                            </div>


                            <div>
                                <p><strong class="callback">(<span class="parameter">message</span>) => {</strong> </p>
                                <textarea id="w3review" name="w3review" rows="2">return message;</textarea>
                                <p>
                                    <strong class="callback">}</strong> <em class="return_type">:boolean</em>
                                </p>
                            </div>
                        </div>

                    </div>                   
                    <div SingleEvent>                    
                        <h1 class="group-title text-center">Cart Page</h1>              
                    </div>                    
                    <div SingleEvent>
                        <div class="item">
                            <label class="item__checkbox active">
                                <input type="checkbox">
                                <i class="fas fa-check"></i>
                            </label>

                            <span EventName>ResetPassword:SendEmailSuccessMsg</span>

                            <div class="d-flex justify-content-center align-items-center row-icons">
                                <button EditBtn>Edit</button>
                            </div>
                        </div>
                        <div class="callback_area expand">
                            <div class="description mb-4">
                                THis is event description
                            </div>
                            <div class="usingPages mb-4">
                                <li><a href="/rentmy-event-management-2/">/rentmy-event-management-2/</a></li>
                                <li><a href="/rentmy-event-management-2/">/rentmy-event-management-2/</a></li>
                            </div>


                            <div>
                                <p><strong class="callback">(<span class="parameter">message</span>) => {</strong> </p>
                                <textarea id="w3review" name="w3review" rows="2">return message;</textarea>
                                <p>
                                    <strong class="callback">}</strong> <em class="return_type">:boolean</em>
                                </p>
                            </div>
                        </div>

                    </div>                   

                </div>
            </div>
        </div>
    </body>

</html>