<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Document</title>
        <link rel="stylesheet" href="bootstrap.min.css">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.9.0/css/all.min.css"
            integrity="sha512-q3eWabyZPc1XTCmF+8/LuE1ozpg5xxn7iO89yfSOd5/oKvyqLngoNGsx8jq92Y8eXJ/IRxQbEC+FGSYxtk2oiw=="
            crossorigin="anonymous" referrerpolicy="no-referrer" />
        <link rel="stylesheet" href="style.css">
    </head>

    <body>
        <div class="container">

            <div class="RentMyWrapperProductDetails RentMyWrapper" RentMyData="">
                <div class="RentMyProductDetailsRow">
                    <div class="RentMyProductDetilsImg">
                        <div class="RentMyProductDetailsImgList">
                            <ul RentMyProductImages>
                                <li class="ActiveImg">
                                    <img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/hql4fbx_1603633576_59u1aty.jpg" alt="product img" />
                                </li>
                                <li>
                                    <img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/rt3n4xn_1603634009_3grjf7a.jpg" alt="product img" />
                                </li>
                                <li>
                                    <img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/hql4fbx_1603633576_59u1aty.jpg" alt="product img" />
                                </li>
                                <li>
                                    <img src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/rt3n4xn_1603634009_3grjf7a.jpg" alt="product img" />
                                </li>
                            </ul>
                        </div>
                        <div class="RentMyProductDetailsImgShow">
                            <img RentMyProductImage src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/160633/hql4fbx_1603633576_59u1aty.jpg" alt="product show img" />
                        </div>
                    </div>
            
                    <div class="RentMyProductDetilsInfo" RentMyProductDetilsInfo>
                        <div class="product-payment-details">
            
                            <h2 class="RentMyProductName" RentMyProductName>{{ product_name }}</h2>
            
                            <div class="RentMyBuyRentToggle" RentMyBuyRentToggle>
                                <label for="BuyRentToggleSwitch" class="BuyRentToggleSwitch">
                                    <input type="checkbox" id="BuyRentToggleSwitch" BuyRentToggleSwitch />
                                    <div class="ToggleSwitchRound"></div>
                                </label>
                            </div>
            
                            <h2 class="RentMyProductPrice" RentMyProductPrice>{{ product_price_text }}</h2>
            
            
                            <div class="RentMyRecurring" RentMyRecurring>
                                <h6 RecurringTitle>Recurring Pricing</h6>
                                <ul RecurringList>
                                    <li RecurringItem>
                                        Recurring
                                    </li>
                                </ul>
                            </div>
            
            
                            <div class="RentMyVariant" RentMyVariant>
                                <h6 VariantTitle>Rent My Variant Sizes</h6>
                                <ul VariantList>
                                    <li VariantItem>Small</li>
                                </ul>
                            </div>
            
            
                            <div class="RentMyProductOptions" RentMyProductOptions>
                                <div class="CustomFieldInner">
                                    <h6 ProductOptionsTitle>RentMy Product Options</h6>
                                    <ul ProductOptionsItem>
                                        <li>Option 001 (+$2.00)</li>
                                    </ul>
                                </div>
                            </div>
            
            
                            <div class="RentMyRentalStartDate" RentMyRentalStartDate>
                                <div usualDateRange>
                                    <h6 RentalStartDateTitle>Select Rental Start Date</h6>
                                    <ul RentalStartDateList>
                                        <li Today>Today</li>
                                        <li Tomorrow>Tomorrow</li>
                                        <li PickDate>Pick Date</li>
                                    </ul>
                                    <span RentalStartDateSelectedLabel>
                                        Today 08:00 AM
                                    </span>
                                </div>
                            </div>
            
            
                            <div class="RentMyRentalDateRange" RentMyRentalDateRange>
                                <h6 RentalDateRangeTitle>
                                    Rental Date Range
                                </h6>
                                <ul class="mb-4" RentalDateRangeList>
                                    <li RentalDateRangeItem>
                                        1 hour <br />
                                        $10.00
                                    </li>
                                </ul>
                            </div>
            
            
                            <div class="RentMyRentalDateRange" RentMyRentalEndDate>
                                <ul>
                                    <li class="mt-0" RentalEndDatePicker>End Date</li>
                                </ul>
            
                                <span RentalEndDateSelectedLabel>
                                    Today 09:00 AM
                                </span>
                            </div>

                            <div class="RentMyExactSelectDuration" RentMyExactSelectDuration>
                                <h6 RentMyExactSelectDurationTitle>
                                    Select Duration
                                </h6>
                                <ul RentMyExactSelectDurationList>
                                    <li class="ExactSelectDurationActive" RentMyExactSelectDurationItem>
                                        Exact Select Duration
                                    </li>
                                </ul>
                            </div>

                            <div class="RentMyExactSelectTime" RentMyExactSelectTime>
                                <h6 RentMyExactSelectTimeTitle>
                                    Select Exact Start time
                                </h6>
                                <ul RentMyExactSelectTimeList>
                                    <li class="ExactSelectTimeActive" RentMyExactSelectTimeItem>
                                        Extact Times
                                    </li>
                                </ul>
                            </div>
            
            
                            <div class="RentMyDeliveryOptions" RentMyDeliveryOptions>
                                <h6 DeliveryOptionsTitle>Delivery Options</h6>
                                <ul DeliveryOptionsList>
                                    <li DeliveryOptionsItem>Local Move</li>
                                </ul>
                            </div>
            
            
                            <div class="RentMySelectLocation" RentMySelectLocation>
                                <h6 SelectLocationTitle>Select Location</h6>
                                <ul SelectLocationList>
                                    <li SelectLocationItem>Default location</li>
                                </ul>
                            </div>
            
            
                            <div class="QuantityContainer" RentmyQuantityContainer>
                                <label QuantityContainerTitle>Quantity</label>
                                <div class="QuantityBtn">
                                    <button class="RentMyBtn" QuantityDecrementBtn>-</button>
                                    <input type="text" autocomplete="off" name="qty" class="InputQuantity" NumberOfQuantity />
                                    <button class="RentMyBtn" QuantityIncrementBtn>+</button>
                                </div>
            
                                <div class="info"> 
                                    <p>
                                        <span RentmyAvailableLabel>Available:</span> 
                                        <span RentmyAvailableQty>17</span> 
                                    </p>
                                    <div RentmyAvailableNotice></div> 
                                </div>
                            </div>
            
            
                            <div class="RentMyCartBtnArea" RentMyCartBtnArea>
                                <button class="RentMyBtn RentMyAddCartBtn" RentMyAddCartBtn>Add To Cart</button>
                                <button class="RentMyBtn RentMyAddWishlistBtn">Add To Wishlist</button>
                            </div>
            
                        </div>
                    </div>
                </div>
            
            
                <div class="RentMyProductDescription">
                    <h3 class="RentMyProductDesTitle">Product Description</h3>
                    <div class="RentMyProductDesBody" RentMyProductDescription>
                        <p>
                            This complete backpacking kit rental has everything two people need for an hiking trip in the backcountry! Whether it is for a weekend trek or that bucket list thru-hike, this kit has more award-winning gear than anywhere
                            else. You have everything you need other than the food and fuel.
                            <br />
                            <br />
                            Only Mountain Side includes this much gear in one package. Our kits are based on the 10 essentials, required gear for any backcountry trip. Not sure what to pack? Checkout our backpackers checklist to ensure you don’t forget
                            anything important. Upgrades to ultralight tents are available.
                            <br />
                            <br />
                            Trail weight per person is 14-15.5lbs, depending on how you divvy up the gear.
                            <br />
                            <br />
                            Is your hiking trip a cold weather adventure? If so, consider adding a foam sleeping pad rental to your backpacking kit. The closed cell construction make for a fantastic thermal barrier when placed between your air pad and
                            sleeping bag.
                            <br />
                            <br />
                            Mountain Side offers a 20 degree synthetic sleeping bag. If preferred to down, just tell us in the order notes.
                        </p>
                    </div>
                </div>
            
            
                <div class="RentMyRelatedProduct">
                    <h3 class="RentMyRelatedProductTitle">Related Products</h3>
                    <div class="RentMyRelatedProductBody">
                        <div class="RentMyRow" RentMyRelatedProducts>
                            <div class="RentMyProductItem" RentMyProductItem>
                                <div class="RentMyProductItemInner">
                                    <div class="RentMyProductImg">
                                        <a href="#" RentMyProductImageUrl>
                                            <img RentMyProductImage
                                                src="https://s3.us-east-2.amazonaws.com/images.rentmy.co/products/982/193260/hrfnd9e_1669558177_2qqwq6o.jpg"
                                                class="ProductImg" alt="product img" />
                                        </a>
                                        <div class="RentMyProductOverlay">
                                            <a class="ProductDetailsIcon" href="#"><i class="fa fa-eye"></i></a>
                                            <a class="ProductCartIcon" href="#"><i class="fa fa-shopping-bag"></i></a>

                                            <div class="WishlistSingleItemOption">
                                                <button class="WishlistAddButton"> <i class="la la-heart-o"></i> </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="RentMyProductBody">
                                        <h4 class="ProductName" RentMyProductName> Product_name </h4>
                                        <h5 class="ProductPrice" RentMyProductPrice> product_price </h5>
                                        <div class="ProductButton">
                                            <a class="ProductDetailsBtn" href="#" RentMyViewDetailsBtn>View Details</a>
                                            <button class="ProductCartBtn" href="#" RentMyAddToCartBtn>Add to Cart</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
        </div>
    </body>

</html>